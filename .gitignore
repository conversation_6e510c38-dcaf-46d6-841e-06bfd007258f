# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Miscellaneous

*.pyc
*.swp
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws

#generated
*.g.dart
*.freezed.dart
*.backfit.dart
inject.config.dart
pubspec.lock

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/pubspec.lock
/build/app/
lib/objectbox-model.json
lib/objectbox.g.dart

# Need to check if it required

/.android/.gradle/
/.android/build/
/.android/plugins_build_output/
/.android/Flutter/build/

*.lock
ios/Runner.xcodeproj/project.pbxproj
*.pbxproj
macos/Flutter/GeneratedPluginRegistrant.swift
windows/flutter/generated_plugin_registrant.cc
windows/flutter/generated_plugins.cmake
lib/framework/dependency_injection/inject.config.dart
lib/ui/utils/theme/assets.gen.dart
lib/ui/utils/theme/fonts.gen.dart
pubspec.lock
android/app/release/app-release.apk
android/app/release/odigo-ad.apk
android/app/release/odigo-tv.apk
/linux/
/macos/
/ios/
android/app/release/odigo-stable.apk

# FVM Version Cache
.fvm/