plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "2"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0.0"
}

android {
    namespace = "com.kody.odigo_display"
    compileSdk = 34
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:-options"
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        debug {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging true
        }
        dex {
            useLegacyPackaging true
        }
    }

    defaultConfig {
        applicationId = "com.kody.odigo_display"
        minSdk = 23
        targetSdk = 34
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        archivesBaseName = "odigo-v${versionName}(${versionCode})-${new Date().format('MMMdd-hh_mma')}"

        //check device architecture command : "adb shell getprop ro.product.cpu.abi"
        ndk {
            abiFilters 'arm64-v8a'
        }

    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }

        debug {
            signingConfig signingConfigs.release
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
    }
}

flutter {
    source = "../.."
}

configurations {
    debugImplementation {
        exclude group: 'io.objectbox', module: 'objectbox-android'
    }
}

dependencies {
    // Add objectbox-android-objectbrowser only for debug builds.
    // Replace <version> with the included objectbox-android version,
    // e.g. check https://github.com/objectbox/objectbox-dart/releases
    // Warning: when ObjectBox for Dart updates check if <version>
    // needs to be updated.
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation fileTree(dir: 'jnilibs', include: ['*.jar', '*.aar'])
    implementation files('../app/libs/REEMAN-ROS-1.0-202304181455.aar')
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.appcompat:appcompat:1.6.1'

    implementation 'com.android.support:multidex:1.0.3'
    implementation files('libs/ysapi.jar')
    debugImplementation("io.objectbox:objectbox-android-objectbrowser:4.0.3")
}
