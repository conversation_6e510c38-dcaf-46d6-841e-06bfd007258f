{"v": "5.9.0", "fr": 30, "ip": 0, "op": 285, "w": 1048, "h": 736, "nm": "waiting", "ddd": 0, "assets": [{"id": "comp_0", "nm": "92-Time Saving", "fr": 29.9700012207031, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape - 1", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [196.744, 153.36, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [196.742, 153.361, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 27.027, "s": [0, 0, 100]}, {"t": 38.038038038038, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.5, 0.004], [1.062, -0.621], [0.484, -1.805], [-0.937, -1.621], [-2.508, 0], [-1.066, 0.614], [-0.489, 1.809], [0.937, 1.625]], "o": [[-1.223, 0.004], [-1.625, 0.934], [-0.48, 1.813], [1.25, 2.164], [1.227, 0], [1.625, -0.941], [0.484, -1.809], [-1.251, -2.164]], "v": [[-0.008, -7.018], [-3.508, -6.072], [-6.782, -1.818], [-6.079, 3.51], [0.007, 7.018], [3.507, 6.08], [6.777, 1.822], [6.078, -3.506]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [196.742, 153.361], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 27.027027027027, "op": 310.31031031031, "st": 27.027027027027, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape - 2", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [153.361, 196.74, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [153.36, 196.743, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 26.026, "s": [0, 0, 100]}, {"t": 37.037037037037, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.348, 1.934], [1.223, -0.004], [1.254, -2.164], [-3.355, -1.938], [-1.219, -0.003], [-1.254, 2.168]], "o": [[-1.063, -0.613], [-2.504, 0], [-1.93, 3.352], [1.066, 0.617], [2.508, 0], [1.926, -3.355]], "v": [[3.512, -6.077], [0.012, -7.018], [-6.078, -3.51], [-3.504, 6.08], [-0.008, 7.021], [6.082, 3.51]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [153.359, 196.741], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 26.026026026026, "op": 310.31031031031, "st": 26.026026026026, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape - 3", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [137.482, 255.996, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [137.482, 255.998, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 25.025, "s": [0, 0, 100]}, {"t": 36.036036036036, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.868, 0], [0, -3.864], [-3.868, -0.004], [0, 3.875]], "o": [[-3.871, 0], [0, 3.875], [3.867, 0], [0, -3.867]], "v": [[0.002, -7.018], [-7.018, -0.002], [-0.002, 7.018], [7.018, -0.002]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [137.482, 255.998], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 25.025025025025, "op": 310.31031031031, "st": 25.025025025025, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape - 4", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [153.36, 315.258, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [153.361, 315.26, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 24.024, "s": [0, 0, 100]}, {"t": 35.035035035035, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.503, -0.004], [1.07, -0.614], [-1.93, -3.348], [-2.5, 0], [0, 0], [-1.062, 0.617], [1.926, 3.352]], "o": [[-1.219, -0.004], [-3.348, 1.938], [1.254, 2.168], [0, 0], [1.223, 0], [3.355, -1.93], [-1.254, -2.165]], "v": [[-0.008, -7.016], [-3.508, -6.082], [-6.078, 3.508], [0.008, 7.02], [0.012, 7.02], [3.504, 6.075], [6.082, -3.511]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [153.36, 315.258], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 24.024024024024, "op": 310.31031031031, "st": 24.024024024024, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shape - 5", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [196.737, 358.636, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [196.739, 358.638, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 23.023, "s": [0, 0, 100]}, {"t": 34.034034034034, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.352, 1.929], [1.219, -0.004], [1.25, -2.164], [-0.481, -1.805], [-1.625, -0.938], [-1.227, 0], [-1.246, 2.164]], "o": [[-1.066, -0.617], [-2.508, 0], [-0.938, 1.633], [0.484, 1.813], [1.066, 0.613], [2.5, -0.004], [1.938, -3.348]], "v": [[3.133, -6.076], [-0.367, -7.017], [-6.453, -3.513], [-7.156, 1.815], [-3.883, 6.081], [-0.383, 7.022], [5.699, 3.505]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [197.117, 358.635], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 23.023023023023, "op": 310.31031031031, "st": 23.023023023023, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Shape - 6", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [255.994, 374.51, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.996, 374.51, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 22.022, "s": [0, 0, 100]}, {"t": 33.033033033033, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.871, 0.004], [0, -3.868], [-3.871, 0], [0, 3.868]], "o": [[-3.871, 0], [0, 3.868], [3.875, 0], [0, -3.871]], "v": [[0.004, -7.018], [-7.02, 0.002], [0.004, 7.018], [7.02, 0.002]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.996, 374.51], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 22.022022022022, "op": 310.31031031031, "st": 22.022022022022, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape - 7", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [315.255, 358.634, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [315.257, 358.633, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 21.021, "s": [0, 0, 100]}, {"t": 32.032032032032, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.5, 0], [1.066, -0.617], [-1.926, -3.348], [-2.504, 0], [-1.062, 0.614], [-0.488, 1.813], [0.941, 1.629]], "o": [[-1.227, 0], [-3.352, 1.93], [1.254, 2.164], [1.227, 0], [1.621, -0.938], [0.484, -1.805], [-1.246, -2.167]], "v": [[0.368, -7.02], [-3.137, -6.074], [-5.71, 3.508], [0.38, 7.02], [3.88, 6.082], [7.152, 1.815], [6.445, -3.508]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [314.882, 358.633], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 21.021021021021, "op": 310.31031031031, "st": 21.021021021021, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape - 8", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [358.636, 315.261, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [358.637, 315.259, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 20.02, "s": [0, 0, 100]}, {"t": 31.031031031031, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.621, 0.929], [1.219, 0], [1.25, -2.164], [-3.351, -1.93], [-1.223, 0], [-1.246, 2.164], [0.489, 1.805]], "o": [[-1.066, -0.625], [-2.504, 0.004], [-1.93, 3.352], [1.066, 0.621], [2.5, 0], [0.941, -1.625], [-0.48, -1.813]], "v": [[3.883, -6.074], [0.379, -7.02], [-5.707, -3.512], [-3.137, 6.074], [0.363, 7.02], [6.449, 3.512], [7.148, -1.816]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [358.262, 315.259], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20.02002002002, "op": 310.31031031031, "st": 20.02002002002, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Shape - 9", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [374.512, 255.998, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [374.512, 255.996, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 19.019, "s": [0, 0, 100]}, {"t": 30.03003003003, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.867, 0], [0.003, -3.868], [-3.871, 0.004], [0, 3.875]], "o": [[-3.875, 0], [0.004, 3.871], [3.867, 0], [-0.001, -3.867]], "v": [[0.004, -7.018], [-7.02, -0.002], [0.004, 7.014], [7.02, -0.002]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [374.512, 255.998], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 19.019019019019, "op": 310.31031031031, "st": 19.019019019019, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Shape - 10", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [358.631, 196.743, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [358.63, 196.741, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 18.018, "s": [0, 0, 100]}, {"t": 29.029029029029, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.5, 0], [1.066, -0.617], [0.485, -1.809], [-0.937, -1.625], [-2.504, 0.004], [-1.066, 0.618], [1.93, 3.352]], "o": [[-1.219, 0], [-1.621, 0.938], [-0.48, 1.816], [1.25, 2.164], [1.223, 0.004], [3.344, -1.938], [-1.253, -2.164]], "v": [[-0.383, -7.02], [-3.883, -6.074], [-7.153, -1.82], [-6.454, 3.512], [-0.368, 7.016], [3.136, 6.078], [5.702, -3.512]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [359.008, 196.742], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 18.018018018018, "op": 310.31031031031, "st": 18.018018018018, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Shape - 11", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [315.254, 153.365, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [315.252, 153.364, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 17.017, "s": [0, 0, 100]}, {"t": 28.028028028028, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.347, 1.934], [1.219, -0.004], [1.25, -2.156], [-0.489, -1.816], [-1.621, -0.938], [-1.223, 0], [-1.25, 2.164]], "o": [[-1.066, -0.621], [-2.504, 0], [-0.93, 1.625], [0.48, 1.813], [1.07, 0.613], [2.504, 0], [1.937, -3.352]], "v": [[3.133, -6.073], [-0.367, -7.014], [-6.457, -3.514], [-7.148, 1.822], [-3.886, 6.08], [-0.382, 7.018], [5.7, 3.51]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [315.629, 153.362], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 17.017017017017, "op": 310.31031031031, "st": 17.017017017017, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Shape - 12", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.001, "ix": 10}, "p": {"a": 0, "k": [255.999, 137.485, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.996, 137.484, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 16.016, "s": [0, 0, 100]}, {"t": 27.027027027027, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outBack(t, b, c, d) {\n    return add(mul(c, add(mul(mul(t = $bm_sub($bm_div(t, d), 1), t), add(mul(add(s, 1), t), s)), 1)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(add(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = $bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.871, 0], [0, -3.871], [-3.871, 0], [0, 3.868]], "o": [[-3.867, 0], [0, 3.867], [3.867, 0], [0, -3.868]], "v": [[0.004, -7.02], [-7.02, 0.008], [0.008, 7.02], [7.02, 0.004]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662999987602, 0.694000005722, 0.736999988556, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.996, 137.484], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 16.016016016016, "op": 310.31031031031, "st": 16.016016016016, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Shape - 13", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [255.994, 256, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.994, 256, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.004, -6.617], [6.629, 0], [0, 6.625], [-6.625, -0.004]], "o": [[0.004, 6.629], [-6.621, 0], [0, -6.621], [6.625, 0]], "v": [[11.988, -0.002], [-0.004, 11.99], [-11.992, -0.002], [0, -11.986]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.187999993563, 0.219999998808, 0.259000003338, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.996, 255.998], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 19.019019019019, "op": 310.31031031031, "st": 19.019019019019, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Shape - 14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19.019, "s": [-97]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23.023, "s": [0]}, {"t": 36.036, "s": [0], "h": 1}, {"t": 47.097, "s": [28], "h": 1}, {"t": 74.0740740740741, "s": [58], "h": 1}], "ix": 10}, "p": {"a": 0, "k": [262.496, 262.496, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.996, 255.996, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.727, 43.547], [-2.727, 43.547], [-2.724, -43.547], [2.722, -43.547]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.259000003338, 0.286000013351, 0.324999988079, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.996, 299.543], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 36.036036036036, "op": 310.31031031031, "st": 19.019019019019, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Shape - 15", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [255.766, 256.727, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.766, 256.727, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-30.152, -13.377], [-24.13, -22.494], [30.152, 13.373], [24.12, 22.494]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.187999993563, 0.219999998808, 0.259000003338, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [228.863, 237.545], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 19.019019019019, "op": 310.31031031031, "st": 19.019019019019, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Shape - 16", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [256.109, 256.566, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256.109, 256.566, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-42.328, 28.589], [-45.961, 22.294], [42.325, -28.589], [45.961, -22.281]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.187999993563, 0.219999998808, 0.259000003338, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [299.945, 230.226], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 19.019019019019, "op": 310.31031031031, "st": 19.019019019019, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Shape - 17", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19.019, "s": [-97]}, {"t": 23.023023023023, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [262.496, 262.496, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.996, 255.996, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 19.019, "s": [0, 0, 100]}, {"t": 23.023023023023, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.727, 43.547], [-2.727, 43.547], [-2.724, -43.547], [2.722, -43.547]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.259000003338, 0.286000013351, 0.324999988079, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.996, 299.543], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 19.019019019019, "op": 36.036036036036, "st": 19.019019019019, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Shape - 18", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.496, 262.494, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.996, 255.994, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 12.012, "s": [0, 0, 100]}, {"t": 17.017017017017, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[26.254, -26.254], [37.129, 0], [26.254, 26.254], [0, 37.133], [-26.254, 26.254], [-37.133, 0], [-26.254, -26.25], [0, -37.125]], "o": [[-26.258, 26.254], [-37.133, 0], [-26.254, -26.254], [0, -37.125], [26.254, -26.25], [37.129, 0.004], [26.254, 26.254], [0, 37.129]], "v": [[98.293, 98.295], [0, 139.01], [-98.289, 98.295], [-139.004, 0.002], [-98.289, -98.287], [0, -138.998], [98.293, -98.287], [139.004, 0.002]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[88.91, 0], [0.004, -88.899], [-88.899, 0], [0, 88.906]], "o": [[-88.895, 0], [0, 88.906], [88.906, 0], [-0.004, -88.898]], "v": [[-0.004, -160.971], [-160.973, 0.002], [0, 160.971], [160.973, 0.002]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.996, 255.994], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 12.012012012012, "op": 310.31031031031, "st": 12.012012012012, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Shape - 19", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.497, 262.498, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [255.997, 255.998, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 13.013, "s": [0, 0, 100]}, {"t": 18.018018018018, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[82.707, 0], [-0.003, 82.703], [-82.707, 0], [0.008, -82.703]], "o": [[-82.699, 0.004], [-0.004, -82.703], [82.703, 0], [0.005, 82.703]], "v": [[-0.007, 149.984], [-149.984, -0.004], [-0.004, -149.988], [149.98, -0.004]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.957000017166, 0.964999973774, 0.976000010967, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [255.999, 256], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 13.013013013013, "op": 310.31031031031, "st": 13.013013013013, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Shape - 20", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.5, 262.498, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 255.998, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 10.01, "s": [0, 0, 100]}, {"t": 15.015015015015, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -93.887], [93.883, -0.004], [0, 93.887], [-93.891, 0]], "o": [[0, 93.891], [-93.891, 0], [0, -93.887], [93.883, 0]], "v": [[170, -0.002], [0, 170.002], [-170, -0.002], [0, -170.002]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.090196078431, 0.090196078431, 0.090196078431, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [256, 255.998], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10.01001001001, "op": 310.31031031031, "st": 10.01001001001, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Background", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.5, 262.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [256, 256, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"t": 10.01001001001, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : sub(time, key(n).time), $bm_rt = $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), add(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = $bm_rt = value = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-255.986, -255.987], [-256, 255.987], [255.98, 255.991], [256, -255.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [256, 256], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 360, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 300.3003003003, "st": 0, "bm": 0}]}, {"id": "comp_1", "nm": "Ellipse_20", "fr": 25, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [480, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-97.164, 95.746, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 78, "op": 198, "st": 78, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "2", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [640, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 49.2, "op": 169.2, "st": 49.2, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "3", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 56.4, "op": 176.4, "st": 56.4, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "4", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [960, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-97.164, 95.746, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 15.6, "op": 135.6, "st": 15.6, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "5", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1120, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-104.254, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 42, "op": 162, "st": 42, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "6", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [1280, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-102.836, 101.418, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 42, "op": 162, "st": 42, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "7", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1440, 240, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 42, "op": 162, "st": 42, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "8", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [480, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 57.6, "op": 177.6, "st": 57.6, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "9", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [640, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 50.4, "op": 170.4, "st": 50.4, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "10", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 33.6, "op": 153.6, "st": 33.6, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "11", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [88.655, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 18, "op": 138, "st": 18, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "12", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [1120, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-97.164, 95.746, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 19.2, "op": 139.2, "st": 19.2, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "13", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [1280, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 27.6, "op": 147.6, "st": 27.6, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "14", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [1440, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 33.6, "op": 153.6, "st": 33.6, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 0, "nm": "15", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [480, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 33.6, "op": 153.6, "st": 33.6, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "16", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [640, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 16.8, "op": 136.8, "st": 16.8, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "17", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 0, "nm": "18", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [960, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 12, "op": 132, "st": 12, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 0, "nm": "19", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1120, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 0, "nm": "20", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1280, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[320, 0], [0, 0], [0, 160], [320, 160]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 320, "h": 160, "ip": 31.2, "op": 151.2, "st": 31.2, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "21", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1440, 560, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 48, "op": 168, "st": 48, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "22", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [480, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 55.2, "op": 175.2, "st": 55.2, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 0, "nm": "23", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [640, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-104.731, 98.423, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 36, "op": 156, "st": 36, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 0, "nm": "24", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [800, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 16.8, "op": 136.8, "st": 16.8, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 0, "nm": "25", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [960, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-102.836, 101.418, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 24, "op": 144, "st": 24, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 0, "nm": "26", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1120, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 51.6, "op": 171.6, "st": 51.6, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 0, "nm": "27", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1280, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 70.8, "op": 190.8, "st": 70.8, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 0, "nm": "28", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 270, "ix": 10}, "p": {"a": 0, "k": [1440, 720, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 37.2, "op": 157.2, "st": 37.2, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 0, "nm": "29", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [480, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, -100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 76.8, "op": 196.8, "st": 76.8, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 0, "nm": "30", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [640, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 38.4, "op": 158.4, "st": 38.4, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 0, "nm": "31", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 21.6, "op": 141.6, "st": 21.6, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 0, "nm": "32", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 4.8, "op": 124.8, "st": 4.8, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 0, "nm": "33", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1120, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 49.2, "op": 169.2, "st": 49.2, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 0, "nm": "34", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1280, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 48, "op": 168, "st": 48, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 0, "nm": "35", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1440, 880, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160, 80, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 320, "h": 160, "ip": 64.8, "op": 184.8, "st": 64.8, "bm": 0}]}, {"id": "comp_2", "nm": "35", "fr": 25, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9.6, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10.8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105.6, "s": [100]}, {"t": 106.8, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.838, "y": 0}, "t": 0, "s": [-10.25, 80, 0], "to": [28.375, 0, 0], "ti": [-28.375, 0, 0]}, {"i": {"x": 0, "y": 0}, "o": {"x": 0.333, "y": 0.333}, "t": 30, "s": [160, 80, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.614, "y": 0}, "t": 97.2, "s": [160, 80, 0], "to": [28.788, 0, 0], "ti": [-28.788, 0, 0]}, {"t": 115.2, "s": [332.731, 80, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [149.776, 15.906, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [20, 20], "ix": 2}, "p": {"a": 0, "k": [150, 15], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 120, "st": -9.999609375, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 3, "ty": 0, "nm": "92-Time Saving", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [508, 372, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.5, 262.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 84, "s": [124, 124, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 92, "s": [133.933, 133.933, 100]}, {"t": 106, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 525, "h": 525, "ip": 0, "op": 106.106106106106, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "Ellipse_20", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [508, 362.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [59, 59, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1920, "h": 1080, "ip": 103, "op": 285, "st": 103, "bm": 0}], "markers": []}