<svg xmlns="http://www.w3.org/2000/svg" width="510" height="563" viewBox="0 0 510 563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_96" data-name="Rectangle 96" width="510" height="563" rx="50" transform="translate(0 -0.025)" fill="#212121" stroke="#5e5e5e" stroke-width="1"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_78" data-name="Rectangle 78" width="190.485" height="173.677" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_110273" data-name="Group 110273" transform="translate(0 0.025)">
    <g id="Rectangle_47" data-name="Rectangle 47" transform="translate(0 -0.025)" fill="#212121" stroke="#3a3a3a" stroke-width="1">
      <rect width="510" height="563" rx="50" stroke="none"/>
      <rect x="0.5" y="0.5" width="509" height="562" rx="49.5" fill="none"/>
    </g>
    <g id="Mask_Group_1" data-name="Mask Group 1" clip-path="url(#clip-path)">
      <ellipse id="Ellipse_11" data-name="Ellipse 11" cx="181" cy="103" rx="181" ry="103" transform="translate(169 384.975)" fill="#4a4a4a" opacity="0.52"/>
    </g>
    <g id="Group_110117" data-name="Group 110117" transform="matrix(0.999, 0.035, -0.035, 0.999, 2943.7, -9464.543)">
      <path id="Path_452" data-name="Path 452" d="M107.887-2.523C167.472-3.916,215.453,8.255,215.056,24.662S166.034,55.5,106.449,56.893-1.116,46.115-.719,29.708,48.3-1.13,107.887-2.523Z" transform="matrix(0.891, 0.454, -0.454, 0.891, -2355.048, 9903.852)" fill="#212121"/>
    </g>
    <g id="Group_110124" data-name="Group 110124" transform="translate(229.757 281.475)">
      <g id="Group_110081" data-name="Group 110081" clip-path="url(#clip-path-2)">
        <path id="Path_348" data-name="Path 348" d="M44.013,29.225a44.081,44.081,0,0,1,7.063,5.139,50.923,50.923,0,0,1-1.534-11.987c0-19.666,13.79-27.636,30.8-17.8,7.05,4.075,13.543,10.572,18.735,18.181,6.381-8.386,17.887-9.795,31.022-2.2,20.065,11.6,36.33,39.8,36.33,63a40.249,40.249,0,0,1-1.011,9.246c14.09,11.417,24.7,31.936,24.7,49.189,0,19.665-13.791,27.636-30.8,17.8L44.087,93.18h0c-.024-.015-.05-.027-.075-.042C28.746,84.312,16.37,62.851,16.37,45.2S28.746,20.4,44.013,29.225" fill="#ececec" fill-rule="evenodd"/>
        <path id="Path_349" data-name="Path 349" d="M44.013,29.225a44.081,44.081,0,0,1,7.063,5.139,50.923,50.923,0,0,1-1.534-11.987c0-19.666,13.79-27.636,30.8-17.8,7.05,4.075,13.543,10.572,18.735,18.181,6.381-8.386,17.887-9.795,31.022-2.2,20.065,11.6,36.33,39.8,36.33,63a40.249,40.249,0,0,1-1.011,9.246c14.09,11.417,24.7,31.936,24.7,49.189,0,19.665-13.791,27.636-30.8,17.8L44.087,93.18h0c-.024-.015-.05-.027-.075-.042C28.746,84.312,16.37,62.851,16.37,45.2S28.746,20.4,44.013,29.225" fill="none" stroke="#141414" stroke-width="0.724"/>
        <path id="Path_350" data-name="Path 350" d="M9.861,35.9l14.465-8.527,3.811,6.79L17.958,45Z" fill="#ececec" fill-rule="evenodd"/>
        <path id="Path_351" data-name="Path 351" d="M9.861,35.9l14.465-8.527,3.811,6.79L17.958,45Z" fill="none" stroke="#141414" stroke-width="0.724"/>
        <path id="Path_352" data-name="Path 352" d="M43.537,11.208,59.075,2.131,57.056,14.915Z" fill="#ececec" fill-rule="evenodd"/>
        <path id="Path_353" data-name="Path 353" d="M43.537,11.208,59.075,2.131,57.056,14.915Z" fill="none" stroke="#141414" stroke-width="0.724"/>
        <path id="Path_354" data-name="Path 354" d="M165.421,171.066l15.335-8.923-11.386-.68Z" fill="#ececec" fill-rule="evenodd"/>
        <path id="Path_355" data-name="Path 355" d="M165.421,171.066l15.335-8.923-11.386-.68Z" fill="none" stroke="#141414" stroke-width="0.724"/>
        <path id="Path_356" data-name="Path 356" d="M28,38.538a44.027,44.027,0,0,1,7.063,5.14A50.931,50.931,0,0,1,33.534,31.69c0-19.665,13.79-27.636,30.8-17.8,7.05,4.075,13.543,10.573,18.734,18.181,6.382-8.386,17.888-9.8,31.023-2.2,20.064,11.6,36.33,39.8,36.33,63a40.3,40.3,0,0,1-1.011,9.245c14.09,11.417,24.705,31.936,24.705,49.189,0,19.665-13.791,27.636-30.8,17.8L36.856,107.566l-8.777-5.073h0c-.025-.015-.051-.027-.076-.042C12.738,93.625.362,72.165.362,54.516S12.738,29.713,28,38.538" fill="#ececec" fill-rule="evenodd"/>
        <path id="Path_357" data-name="Path 357" d="M28,38.538a44.027,44.027,0,0,1,7.063,5.14A50.931,50.931,0,0,1,33.534,31.69c0-19.665,13.79-27.636,30.8-17.8,7.05,4.075,13.543,10.573,18.734,18.181,6.382-8.386,17.888-9.8,31.023-2.2,20.064,11.6,36.33,39.8,36.33,63a40.3,40.3,0,0,1-1.011,9.245c14.09,11.417,24.705,31.936,24.705,49.189,0,19.665-13.791,27.636-30.8,17.8L36.856,107.566l-8.777-5.073h0c-.025-.015-.051-.027-.076-.042C12.738,93.625.362,72.165.362,54.516S12.738,29.713,28,38.538" fill="none" stroke="#141414" stroke-width="0.724"/>
        <path id="Path_358" data-name="Path 358" d="M79.619,71.044l16.557,9.57V75.829c0-5.277-3.714-11.717-8.279-14.356s-8.278-.491-8.278,4.785ZM84.8,99.028a1.978,1.978,0,0,0,.257,1.079,2.164,2.164,0,0,0,.772.844l4.139,2.392a.7.7,0,0,0,.771.048A.834.834,0,0,0,91,102.61l-.653-7.164c1.06-.278,1.695-1.322,1.695-2.869A9.175,9.175,0,0,0,87.9,85.4c-2.283-1.32-4.139-.246-4.139,2.393a8.773,8.773,0,0,0,1.693,4.827ZM70.305,65.66l3.105,1.794V62.669c0-9.234,6.5-12.991,14.487-8.373s14.487,15.887,14.487,25.122V84.2l3.1,1.794a2.291,2.291,0,0,1,1.035,1.795V117.7a2.942,2.942,0,0,1-1.169,2.654,2.874,2.874,0,0,1-2.971-.261L73.41,103.343a9.171,9.171,0,0,1-4.139-7.178V66.258c0-.661.463-.928,1.034-.6" fill="#ececec" fill-rule="evenodd"/>
        <path id="Path_359" data-name="Path 359" d="M79.619,71.044l16.557,9.57V75.829c0-5.277-3.714-11.717-8.279-14.356s-8.278-.491-8.278,4.785ZM84.8,99.028a1.978,1.978,0,0,0,.257,1.079,2.164,2.164,0,0,0,.772.844l4.139,2.392a.7.7,0,0,0,.771.048A.834.834,0,0,0,91,102.61l-.653-7.164c1.06-.278,1.695-1.322,1.695-2.869A9.175,9.175,0,0,0,87.9,85.4c-2.283-1.32-4.139-.246-4.139,2.393a8.773,8.773,0,0,0,1.693,4.827ZM70.305,65.66l3.105,1.794V62.669c0-9.234,6.5-12.991,14.487-8.373s14.487,15.887,14.487,25.122V84.2l3.1,1.794a2.291,2.291,0,0,1,1.035,1.795V117.7a2.942,2.942,0,0,1-1.169,2.654,2.874,2.874,0,0,1-2.971-.261L73.41,103.343a9.171,9.171,0,0,1-4.139-7.178V66.258C69.271,65.6,69.734,65.33,70.305,65.66Z" fill="none" stroke="#141414" stroke-width="0.724"/>
      </g>
    </g>
  </g>
</svg>
