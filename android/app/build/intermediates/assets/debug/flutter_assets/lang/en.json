{"keyBack": "Back", "keyNo": "No", "keyYes": "Yes", "keyOk": "Ok", "keyEdit": "Edit", "keyCancel": "Cancel", "keyAdd": "Add", "keyYourPasswordHasBeenSuccessfullyChanged": "Your Password Has Been Successfully Changed.", "keyPasswordSpecialCharacterValidation": "Password must have one or more special characters.", "keyCurrentPasswordSpecialCharacterValidation": "Old Password must have one or more special characters.", "keyNewPasswordSpecialCharacterValidation": "New Password must have one or more special characters.", "keyConfirmPasswordSpecialCharacterValidation": "Confirm Password must have one or more special characters.", "keyPasswordNumericValueValidation": "Password must have one or more numeric values.", "keyCurrentPasswordNumericValueValidation": "Old Password must have one or more numeric values.", "keyNewPasswordNumericValueValidation": "New Password must have one or more numeric values.", "keyConfirmPasswordNumericValueValidation": "Confirm Password must have one or more numeric values.", "keyPasswordLowerCaseValidation": "Password must have one or more lowercase characters.", "keyCurrentPasswordLowerCaseValidation": "Old Password must have one or more lowercase characters.", "keyNewPasswordLowerCaseValidation": "New Password must have one or more lowercase characters.", "keyConfirmPasswordLowerCaseValidation": "Confirm Password must have one or more lowercase characters.", "keyPasswordUpperCaseValidation": "Password must contain one or more uppercase characters.", "keyCurrentPasswordUpperCaseValidation": "Old Password must contain one or more uppercase characters.", "keyNewPasswordUpperCaseValidation": "New Password must contain one or more uppercase characters.", "keyConfirmPasswordUpperCaseValidation": "Confirm Password must contain one or more uppercase characters.", "keySearchHere": "Search Here", "keyPrivacyPolicies": "Privacy Policies", "keyTermsAndConditions": "Terms & Conditions", "keyAboutUs": "About Us", "keyPhotoFromGallery": "Photo from gallery", "keyPhotoFromCamera": "Photo from camera", "keySignIn": "Sign In", "keyEmailRequiredValidation": "Email should be required.", "keyInvalidEmailValidation": "You have entered an invalid email.", "keyPasswordRangeValidation": "Password range should be between 8-16 characters.", "keyPasswordRequiredValidation": "Password should be required.", "keyContactNumberValidation": "Mobile Number should be required.", "keyContactNumberLengthValidation": "Mobile Number length should be 7-15 digits.", "keyOtpRangeValidation": "Otp range should be 6 characters.", "keyOtpInvalidValidation": "Otp Is Invalid", "keyOtpRequiredValidation": "Otp should be required.", "keyCurrentPasswordRangeValidation": "Current Password length must be greater than or equal to 8 to 16.", "keyCurrentPasswordRequiredValidation": "Old Password should be required.", "keyNewPasswordRangeValidation": "New Password length must be greater than or equal to 8 to 16.", "keyNewPasswordRequiredValidation": "New Password should be required.", "keyConfirmPasswordRangeValidation": "Confirm Password length must be greater than or equal to 8 to 16.", "keyConfirmPasswordRequiredValidation": "Confirm Password should be required.", "keyConfirmPassword": "Confirm Password", "keyNewAndConfirmPasswordNotMatchValidation": "Password And Confirm Password not match.", "keySplashNote": "Because your personal cell\nnumber is not for everyone!", "keyGetStarted": "Get Started", "keyExit": "Exit", "keyAreYouSureWantToExitFromApp": "Are you sure you want to exit from app?", "keyYourSessionExpiredNote": "Your session is expired. Please login again to use application!", "keyDashboard": "Dashboard", "keyRoutes": "Routes", "keyProductionPoint": "Production Point", "keyChargingPoint": "Charging Point", "keyRelocate": "Relocate", "keyObstacleSound": "Obstacle\nSound", "keyLights": "Lights", "keyStartRouteNavigation": "Start Route\nNavigation", "keyPlayPauseNavigation": "Play/Pause\nNavigation", "keyCancelNavigation": "Cancel\nNavigation", "keyBackgroundMusic": "Background\nMusic", "keyStoreList": "Store List", "keyRouteList": "Route List", "keyProductionList": "PRODUCTION", "keyStorage": "Storage", "keySync": "Sync", "keyPreview": "Preview", "keySelect": "Select", "keyLogin": "<PERSON><PERSON>", "keyAuthenticate": "Authenticate", "keyCruiseMode": "Cruise Mode", "keyJanuary": "January", "keyFebruary": "February", "keyMarch": "March", "keyApril": "April", "keyMay": "May", "keyJune": "June", "keyJuly": "July", "keyAugust": "August", "keySeptember": "September", "keyOctober": "October", "keyNovember": "November", "keyDecember": "December", "keyDownloads": "Downloads", "keyNoDownloadsFound": "No Downloads Found.", "keyAvailableWifi": "AVAILABLE WI-FI", "keyConnect": "Connect", "keyDisconnect": "Disconnect", "keyNoDataFound": "No Data Found", "keyMobileNumber": "Mobile Number", "keyLoginToODIGO": "Login To Odigo", "keyEnterServerDetails": "Enter Server Details", "keyTryAgain": "Try Again", "keyToContinuePleaseEnterYourRegisteredMobileNumber": "To continue please enter your registered mobile number", "keyEnterTheNetworkSecurityKey": "Enter The Network Security Key", "keyConnectedSecured": "Connected, Secured", "keySecurityKeyIsRequired": "Security key is required.", "keySTORES": "STORES", "keyClient": "Client", "keyClients": "Clients", "keyPARTNERS": "PARTNERS", "keyPARTNERSMSG": "Explore Your Partners", "keySTORESMSG": "Explore Nearby Stores", "keyENTITIES": "ENTITIES", "keyEntitiesMSG": "Explore Nearby Entities", "keySTORAGE": "STORAGE", "keySTORAGEMSG": "Secure Your Data", "keyROUTELIST": "ROUTE LIST", "keyROUTELISTMSG": "View Available Routes", "keyPRODUCTIONLISTMSG": "View Available Production Points", "keySYNCDATA": "SYNC DATA", "keySYNCDATAMSG": "Keep Data Updated", "keyHistory": "History", "keyHistoryMsg": "View Advertisement History", "keyWalletBalance": "Wallet Balance", "keyAds": "Ads", "keyAdvertisements": "Advertisements", "keyPlayed": "Played", "keyPlayBuzzer": "Play Buzzer", "keyMore": "More", "keyExploreNearbyStores": "Explore Nearby Stores", "keyExploreStore": "Explore Store Details", "keyStores": "Stores", "keyStore": "Store", "keyStoreName": "Store Name", "keyStoreAddress": "Store Address", "keyNoSearchFound": "No Search Found", "keyNoSearchFoundDescription": "Sorry, there are no result for you", "keyViewAvailableRoutes": "View Available Routes", "keyViewAvailablePoints": "View Available Production Points", "keyFloor": "Floor", "keyNavigation": "Navigation", "keyManageNavigationPoints": "Manage Navigation Points", "keyDoYouWantToStartCruiseMode": "Do you want to start cruise mode?", "keyAvailableMemory": "Available Memory", "keyUsedMemory": "Used Memory", "keyFreeMemory": "Free Memory", "keyTotalMemory": "Total Memory", "keyUsed": "Used", "keySTOREWISEUSAGE": "STORE WISE USAGE", "keyAreYouSure": "Are You Sure?", "keyNoThanks": "No, Thanks", "keyYesSure": "Yes, Sure", "keyLetMeGuideYou": "Let Me Guide You", "keyThisWay": "This Way!", "keyWeveMadeIt": "We’ve Made It!", "keyWelcomeToYourDestination": "Welcome To Your Destination", "keyFetchingData": "Fetching Data", "keyFetchingDataDesc": "Please wait while we fetch your data", "keyProcessingData": "Processing Data", "keyProcessingDataDesc": "Please wait while we process your data", "keyPleaseWait": "Please wait", "keyConnectingToWifi": "Connecting to Wifi", "keyNext": "Next", "keyAboutDevice": "About Device", "keyRobotUuid": "Robot Uuid", "keyROSIP": "ROS IP", "keyFloorNumber": "Floor Number", "keySpeed": "Speed", "keySpeakerSound": "Speaker Sound", "keyAndroidId": "Android Id", "keyMainDisplayId": "Main Display Id", "keyBackDisplayId": "Back Display Id", "keySocketUrl": "Socket Url", "keySocketPortNumber": "Socket Port Number", "keyChassisId": "<PERSON><PERSON><PERSON>", "keySerialNumber": "Serial Number", "keyHostName": "Host Name", "keyNavigationVersion": "Navigation Version", "keyPowerBoardVersion": "PowerBoard Version", "keyCopiedToYourClipboard": "Copied to your clipboard !", "keyInformation": "Information", "keyInformationMsg": "View Robot Information", "keySelectDate": "Select Date", "keyTiming": "Timing", "keyScheduled": "Scheduled", "keyInterrupted": "Interrupted", "keyRefresh": "Refresh", "keyAd": "Ad", "keyWaitingTime": "Waiting Time", "keySave": "Save", "keySec": "Sec", "keyLocationPermissionMsg": "Allow Odigo", "keyLocationPermissionDescription": "To Access Your Current Location", "keyAllow": "Allow", "keyDeny": "<PERSON><PERSON>", "keyFreeUpSpace": "Free up Space(i)", "keyRemoveOlderAdvertisementMediaFiles": "Remove older advertisement media files.", "keyAssets": "Assets", "keyDEFAULT": "DEFAULT", "keySCHEDULED": "SCHEDULED", "keyEmergencySwitchIsPressed": "Emergency Switch Is Pressed", "keyChangeColor": "Change Color", "keySkip": "<PERSON><PERSON>", "keyNoCategoryFound": "No Category Found", "keyNoStoreFound": "No Store Found", "keyNoStoreFoundCategories": "No store is assigned to the category, please try to look into different categories", "keyReconnectSocket": "Reconnect Socket", "keyDisplayMode": "Display Mode", "keyDownloadNewApk": "Downloading", "keyDownloadingNote": "Almost There! Your Download Is Loading Up.", "keyBackToLogin": "Back to Login", "keyBackToHome": "Back to home", "keyOrders": "Orders", "keyCancelOrder": "Cancel Order", "keyOrderDetails": "Order Details", "keyDepartment": "Department", "keyInstruction": "Instruction", "keyOrderId": "Order ID", "keyOrderTime": "Order Time", "keyLogoutConfirmationMessageWeb": "Are you sure you want to Logout ?", "keyLogout": "Logout", "keyWithoutMilk": "Without milk", "keySoyMilk": "Soya Milk", "keyAlmondMilk": "Almond Milk", "keyWithoutSugar": "Without Sugar", "keySingleSugar": "Single Sugar", "keyDoubleSugar": "Double Sugar", "keySmall": "Small", "keyMedium": "Medium", "keyLarge": "Large", "keyAdditionalNoteMsg": "Additional note is required", "keyAllItems": "All Items", "keyDrinks": "Drinks", "keySnacks": "Snacks", "keyTray": "Tray", "keyServices": "Services", "keySettings": "Settings", "keyForgotPassword": "Forgot Password", "keyLoginToDasher": "Login <PERSON>", "keyRobotCode": "Robot Code", "keyPasswordRequired": "Password is required.", "keyPassword": "Password", "keyInvalidPasswordValidation": "Password must be between 8-16 characters long.", "keyRobotCodeValidation": "Please Enter Valid Robot Code.", "keyPauseAndReturn": "Pause / Return", "keyHome": "Home", "keyClearTask": "Clear Task", "keySound": "Sound", "keyLight": "Light", "keyEmergencyStop": "Emergency Stop", "keyGoToChargingPile": "Go To Charging", "keyMap": "Map", "keyBotStatus": "Bot Status", "keyIntermission": "Intermission", "Module_Name": "<PERSON><PERSON><PERSON>", "keyWifi": "Wi-fi", "keyNoWifiNetwork": "No Wi-Fi networks found nearby", "keyStayConnectedAndDiscoverNetwork": "Stay connected and discover Wi-Fi networks around you!", "keyEnableWifi": "Enable Wifi", "keyYourWifiIsOff": "Please turn on your wifi", "keyConnected": "Connected", "keySecured": "Secured", "keyEnterNetworkSecurityKey": "Enter The Network Security Key", "keyPastOrder": "Past Order", "keyShowing": "keyShowing", "keyTo": "To", "keyFrom": "From", "keyOf": "Of", "keyEntries": "Entries", "keyPrevious": "Previous", "keyCurrentOrder": "Current Order", "keyDasher": "<PERSON><PERSON>", "keyId": "ID:", "keyNoOfTray": "No. of Tray", "keyAssignedFor": "Assigned For", "keyProfile": "Profile", "keyCategories": "Categories", "keyOrder": "Order", "keyAdditionalNote": "Additional Note", "keyNote": "Note", "keySubmit": "Submit", "keyDefaultLocation": "Default Location", "keyOrderIsPreparing": "Your order is currently being processed and is in a preparing state.", "keyYourOrderIsPreparing": "Your Order Is Preparing", "keyClose": "Close", "keyChooseYourLocation": "Choose Your Location", "keyCustomize": "Customize", "keyConfirmOrder": "Confirm Order", "keyConfirm": "Confirm", "keyPriorityProductDialog": "Are you sure you want to order on priority?", "keyAlwaysYesDontAskAgain": "Always yes, Don't ask me again", "keyAddMoreProduct": "+Add More Product", "keyPriorityOrder": "Priority Order", "keyAddMoreItems": "Add More Items", "keyCart": "<PERSON><PERSON>", "keyAddToTray": "Add To Tray", "keyPlaceOrder": "Place Order", "keySelectLocation": "Select Location", "keyReOrder": "Reorder", "keyAlreadyItemsConfirmationMessage": "Already items in cart.", "keyAlreadyItemsRemoveConfirmationMessage": "Are you sure to remove previously added items from cart?", "keySelectLanguage": "Select Language", "keyMealDeliveryMode": "Meal Delivery Mode", "keyCelebrationMode": "Celebration Mode", "keyRecyclingMode": "Recycling Mode", "keyFreeDistributorMode": "Free Distributor Mode", "keyRobotInfo": "Robot Info", "keyTips": "Tips", "keyRelocationTip": "Ensure the normal positioning, please dock the machine on the Production point, and then click relocate below", "keyOperatingSpeed": "Operating Speed (m/s)", "keyDoNotPlay": "Do Not Play", "keyBackGroundMusic": "Background Music", "keyResidenceTime": "Residence Time", "keyPromptForPlacingRecyclables": "Prompt for placing recyclables", "keyPromptAfterPlacingRecyclables": "Prompt after placing recyclables", "keyPlayMusicAtArrival": "Play music on arrival", "keyOpenThePromptAfterPlacingRecyclables": "Open the prompt after placing recyclables", "keyBasicSetting": "Basic Setting", "keyLanguageSetting": "Language Setting", "keyNetworkSetting": "Network Setting", "keyDistributionMode": "Distribution Mode", "keyVersionSetting": "Version Setting", "keyMediaVolume": "Media Volume", "keyObstacleAvoidancePrompt": "Obstacle Avoidance Prompt", "keyScreenBrightness": "Screen Brightness", "keyLowBatterySetting": "Low Battery Setting", "keyEnableObstacleAvoidancePrompt": "Enable obstacle avoidance prompt", "keyDisplayContentDuringDelivery": "Display Content During Delivery", "keyPleaseSelectRoute": "Please Select Route", "keyStart": "Start", "keyDataSynchronization": "Data Synchronization", "keyOtherSettings": "Other Settings", "keyAdministratorPassword": "Administrator Password", "keyTableDistribution": "Table Distribution", "keyNumberOfTrays": "Number of Trays", "keyCruiseSetting": "Cruise Setting", "keyMealDeliverySetting": "Meal Delivery Setting", "keyRecycleModeSetting": "Recycle Mode Setting", "keySelectBackgroundMusic": "Select Background Music", "keyBroadcastInterval": "Broadcast Interval", "keyCircularBroadcast": "Circular Broadcast ", "keyTurnOnCruiseBroadcast": "Turn on Cruise broadcast", "keyChoose": "<PERSON><PERSON>", "keyBackGroundMusicFileSelect": "Please select background music file", "keyPromptForDeliveryArrival": "Prompt for delivery arrival", "keyAudition": "Audition", "keyCelebrationSetting": "Celebration Setting", "keyBirthdayCelebration": "Birthday Celebration", "keyAnniversaryCelebration": "Anniversary Celebration", "keyAwardCelebration": "Award Celebration", "keyOther": "Other", "keyContinue": "Continue", "keyWaitingTimeForMealTaking": "Waiting Time for meal taking", "keyOpenThePromptPlacingRecyclables": "Open the prompt placing recyclables", "keyPromptAfterTakingMeal": "Prompt after taking meal", "keyNextPoint": "Next Point", "keyCruiseModeRunning": "Cruise Mode Running", "keyTaskSuspended": "Task Suspended", "keyContinueTaskIn": "Continue the task in 17 seconds", "keyCancelTask": "Cancel Task", "keyGoToProduction": "Go To Production", "keyResume": "Resume", "keyGoNow": "Go Now", "keyNavigationWiFi": "Navigation WiFi", "keyAndroidWifi": "Android WiFi", "keyNavigationIp": "Navigation IP", "keyMachineId": "Machine ID", "keyKodyWifi": "<PERSON>dy wifi", "keyNavigationIpValue": "", "keyMachineIdValue": "fbot912b-234232-002-112", "keyBatteryLowGoToCharging": "The Battery is low. i will go to the charging pile to charge my self in 9 seconds ", "keyReallocate": "Reallocate", "keyEnterPersonName": "Enter Person Name", "keyPersonNameValidation": "Please Enter Person Name", "keyPleaseSelectLocationPoint": "Please select location point.", "keyPleaseSelectTray": "Please Select Tray.", "keyWeHaveReached": "We have reached", "keyComplete": "Complete", "keyContinueTaskAfter": "Continue task after", "keySeconds": "Seconds", "keyFreeDistributorModeSetting": "Free Distribution Setting ", "keyArrivalReminder": "Food delivery arrival reminder", "keyLocation": "Location", "keyUpdate": "Update", "keyArrivalReminderCustomMessage": "Custom food delivery arrival reminder (________please \nremove food from the tray on level ___)", "keySelectMusicFile": "Select music file", "keyRebuildMap": "Rebuild Map", "keyIncrementMap": "Increment Map", "keyAddDeliveryPoint": "Set delivery", "keySetChargingPoint": "Set charging", "keySetProductionPoint": "Set production", "keyMakeARoute": "Make a route", "keyRecyclePointLocation": "Set recycle", "keyExitDeployment": "Exit Deployment", "keyPleaseEnterPoint": "Please enter the point name", "keyClickRebuildMapToDiscard": "click rebuild map to discard\nthe previous map. please confirm your operation.", "keyClickIncrementMapToExtent": "Click on confirm to start incremental mapping, This will add to your existing map", "keyCreatingRoute": "Creating Route", "keyTheProductionPointIsTheStartingLocation": "The production point is the starting position of each delivery task. Are you sure to set the current position as production point?", "keyTheRouteIsBeingDrawn": "The route is being drawn, please push the robot along the specified route and click to save the route when it reaches the end of the route.", "keyTheCollectionPointIsTheLocation": "The collection point is the location where the recyclables are placed. Are you sure to set the cut tent location as the collection point?", "keyPointName": "Point Name", "keyPleaseMakeSureThatTheRobot": "Please make sure that the robot is correctly docked on the charging pile, and then click confirm to set the charging pile", "keySwitchingToMapBuilding": "Switching to map building mode,\n please wait", "keyDeliveryMode": "Meal Delivery", "keyExitingRoutes": "Exiting routes", "keyStartCruiseMode": "Start cruise mode", "keyRedirectToModeSettings": "Open mode settings", "keyThisAreDifferentModeOfDasher": "This are different\n mode of dasher", "keyBatteryLevelOfDasher": "Battery level of dasher", "keyRedirectToSettingScreen": "Open setting screen", "keyRedirectToMapScreen": "Open map screen", "keyNavigationToProductionPoint": "Navigate to production point", "keyNavigationToChargingPoint": "Navigate to charging point", "keyManageOperatingSpeed": "Manage operating speed", "keyManageBackgroundMusic": "Manage background music", "keyManageBroadCastIntervalTime": "Manage broadcast interval time", "keyCreateNewBroadCast": "Create new broadcast", "keyManageBroadCast": "Manage broadcast", "keySaveSetting": "Save setting", "keyTrays": "Trays", "keyStartDistributionMode": "Start distribution mode", "keyManageWaitingTime": "Manage waiting time", "keyCreateNewReminder": "Create new reminder", "keyManageReminder": "manage reminder", "keyLocationPoints": "Location points", "keyStartRecyclingMode": "Start recycling mode", "keyOpenRecycleModeSetting": "Open recycle mode setting", "keyTypesOfCelebration": "Types of celebration", "keySelectLocationPoints": "Select location points", "keyModeSettings": "Mode settings", "keyValidateMealDelivery": "Please select tray and delivery location point.", "keyValidateCruiseMode": "Please select route.", "keySelectCelebrationTypeMsg": "Please select celebration type.", "keySelectLocationPointMsg": "Please select location point.", "keyDistributor": "Distributor", "keyVoiceSelection": "Voice selection", "keyPhoneNumber": "Phone Number / Email", "keyEnterYourPhoneNumber": "Enter your phone number or Email", "keyEnterTheSixDigitCode": "Enter OTP", "keyVerify": "Verify", "keyDoYouWantToAddExistingConfigurationOrOld": "Do you want to add existing configuration or old?", "keyCongratulationsYourOTPVerificationWasSuccessful": "Congratulations!!\nOTP verified successfully.", "keySelectMap": "Select Map", "keyCreate": "Create", "keyYouDontHaveExistingMap": "You don't have existing map \nplease create new map.", "keyDoYouWantToAddExistingScreen": "Do you want to continue with \nyour existing configuration?", "keyPhoneNumberRequired": "Field value is required.", "keyPhoneNumberInvalid": "Please enter valid phone number / Email.", "keyEmailIDInvalid": "Please enter valid Email.", "keyEnterOtp": "Otp is required.", "keyEnterValidOtp": "Please enter valid otp.", "keyPleaseSelectCelebrationType": "Please select celebration type.", "keyResendCode": "Resend code", "keyResendCodeIn": "Resend code in", "keyCreateRoute": "Create Route", "keyManageMusic": "Manage Music", "keyManagePromptVoice": "Manage prompt voice", "keyDone": "Done", "keyCruiseModeSettings": "Open cruise mode setting", "keySetRecyclePointLocation": "Set recycle point location", "keyDoYouWantToRelocate": "Do you want to relocate ?", "keySwitchToDifferentMap": "Switch to a different map?", "keyConfigureModuleSettings": "Configure module settings?", "keyMultiMachineSettings": "Multi-machine settings?", "keySwitchMap": "Switch Map", "keySwitchNetwork": "Switch Network", "keyCallModuleSetting": "Call Module Setting", "keyMultiMachineConfiguration": "Multi Machine Configuration", "keyAddingPoint": "Adding Point", "keySelectMapToApply": "Select Map to Apply", "keyApplyMap": "Apply Map", "keyApplyingMap": "Applying Map", "keyLoadingMap": "Loading Map", "keyYouCannotApplyAlreadyAppliedMap": "You cannot apply already applied map", "keySyncingMap": "Syncing Map", "keySyncingMapOnServer": "Syncing Map On Server", "keyConnectingToNetwork": "Connecting to the network\nPlease wait", "keyStartTutorial": "Start Tutorial", "keyDoYouWantToSeeTheTutorial": "Do you want to see the tutorial", "keyMS": "(m/s)", "keySyncingMapData": "Syncing Map Data", "keySavingMap": "Saving Map", "keyOnMachine": "(On Machine)", "keyOnServer": "(On Server)", "keyMale": "Male", "keyFemale": "Female", "keyConfirmRouteManageMessage": "Please take the robot to the starting point of the route and press confirm", "keyRecyclePoint": "Recycle point", "keySelectRelocationPoint": "Select relocation point", "keyDownloadBackgroundMusic": "Download Background Music", "keyExitRebuildMap": "Exit Map Building", "keyExitRebuildMapMessage": "Please make sure \"Set Production\", \"Set charging\", \"Make a route\" and \"Set delivery\" are added to navigate the robot", "keyMapBuildingFailedMessage": "Unable to save map, please make sure to move the robot around to create a map", "keyTroubleshoot": "Troubleshoot", "keyStartTroubleshoot": "Start troubleshoot", "keyTroubleshootTip": "Ensure the normal positioning, please dock the machine on the Production point, and then click confirm below", "keyStartingTroubleshoot": "Starting troubleshoot", "keyGettingRobotDetails": "Getting robot details", "keyGettingConnectedWifiDetails": "Getting Connected Wifi", "keyGettingHardwareDetails": "Getting Hardware Details", "keyManageAnniversaryMusic": "Manage anniversary music", "keyManageBirthdayMusic": "Manage birthday music", "keyManageAwardMusic": "Manage award music", "keyManageOtherMusic": "Manage other celebrations music", "keyListOfAvailableRoutes": "List of available routes", "keyAnniversaryArrivalMusic": "Anniversary Arrival Music", "keyBirthdayArrivalMusic": "Birthday Arrival Music", "keyAwardArrivalMusic": "Award Arrival Music", "keyOtherArrivalMusic": "Other Arrival Music", "keyEnterYourMessage": "Enter your message", "keyRobots": "Robots", "keyAvailableMaps": "Available Maps for your robot", "keyAvailableMapsLocal": "Available Maps at local server", "keyTotalRobots": "Registered Robots", "keyAvailableRobotsMapSyncing": "Please select the one for map syncing (Also if created new map)", "keyPointNameMustBeRequired": "Points name must be Required", "keySaveMapMessage": "Save Map", "keyRouteNameMustBeRequired": "Route name must be Required", "keyWayPathNotFoundForMap": "Way Path not found for map", "keyCouldNotFoundWayPathDoYouWantToSwitchMap": "Could not found way path do you want to switch map?", "keyLastSyncedDateIsNull": "Last synced date is null", "keyLocationMapped": "Location Mapped", "keyPleaseReSyncTheDataInOrderToFetchDetailsOfTheStore": "Please re-sync the data in order to fetch details of the store", "keyAliasNameRequiredValidation": "<PERSON><PERSON> name should be required", "keyAlias": "<PERSON><PERSON>", "keyEnterAliasName": "<PERSON><PERSON> Name", "keyCreateNewMap": "Create New Map", "keyCreateNewMapNote": "Do you want to create new map?", "keyNoMapsFoundOnServer": "No maps found on the server!", "keyLidar": "Lidar", "keyIMU": "IMU", "keyOdo": "<PERSON><PERSON>", "key3DCamera": "3DCamera", "keySensorInformation": "Sensor Information", "keyTouchorPulluptoclose": "Touch or Pull-up to close", "keySelectedTypes": "Selected Types : ", "keySwitchWifi": "Switch Wi-fi", "keyCRUISE": "CRUISE", "keyWalletDeduction": "Wallet Deduction", "keyDeductWallet": "Deduct Wallet", "keyAbnormalTitle": "Communication Error with ROS", "keyAbnormalMessage": "We've encountered an abnormal communication issue with ROS. To ensure system stability, we will terminate the app", "keyUpdateBuild": "Update APK", "keyUpdateBuildMessage": "Update Latest APK", "keyHowtoUse": "How to Use?", "keySmartAdvertisingRobot": "Smart Advertising Robot", "keyWhere": "Where", "keySearchDestinations": "Search Destinations", "keySearch": "Search", "keyHowToUseOdigo": "HOW TO USE ODIGO?", "keyHowToUseOdigoDesc": "Follow these simple steps to find stores, discover promotions, and make the most of your mall visit.", "keyStep1TapToBegin": "STEP 1: TAP TO BEGIN", "keyTouchTheScreenToStartAndExploreTheMenu": "Touch the screen to start and explore the menu.", "keyStep2SearchAndFollow": "STEP 2: SEARCH & FOLLOW", "keyFindYourStoreSelectItAndLetOdigoGuideYouEffortlessly": "Find your store, select it, and let <PERSON><PERSON> guide you effortlessly.", "keyStep3ExploreAndEnjoy": "STEP 3: EXPLORE & ENJOY", "keyDiscoverPromotionsAlongTheWayAndRestartAnytimeForMoreAssistance": "Discover promotions along the way, and restart anytime for more assistance.", "keyEmergencyPressed": "Emergency stop switch is pressed", "keyEmergencyPressedDescription": "The robot is unable to proceed with navigation because the emergency switch has been activated.", "keyInteraction": "Interaction", "keySyncInteractionDataWithServer": "Sync interaction data with server", "keyLanguage": "Language", "keyVideoSound": "Video Sound", "keyPromptVolume": "Prompt Volume", "keyBattery": "Battery", "keyMinimumOperatingBatteryPercentage": "Minimum Operating Battery Percentage", "keyManageMap": "Manage Map", "keyManageYourCurrentMap": "Manage your current map", "keyNotConnectedToNavigationWiFi": "Not connected to Navigation Wi-Fi", "keyOdigoIsNotAtTheChargingStation": "Odigo is not at the Charging Station", "keyTouchToNavigateToYourDestination": "Touch to navigate to your destination", "keyAssignStores": "Assign Stores", "keyAssignElevator": "Assign Elevator", "keyELEVATOR": "ELEVATOR", "keyMappingStores": "Mapping Stores", "keyBalance": "Balance", "keyMapRobotPoint": "Map Robot Point", "keyDeleteLocation": "Delete Location?", "keyAreYouSureYouWantToDeleteThisLocation": "Are you sure you want to delete this location", "keyAddLocationPoint": "Add Location Point", "keyYouHaveNoNewStoresToAssign": "You have no new stores to assign", "keyDeletingLocationAndLocationPointsAssociatedWithTheLocation": "Deleting Location and Location points associated with the location", "keyAssignStoresToRobotPoints": "Assign stores to robot points", "keyFetchingPoints": "Fetching Points", "keySearchYourBrandHere": "Search your brand here", "keyFetchingStores": "Fetching Stores", "keyVirtualWall": "Virtual Wall", "keySetCharging": "Set Charging", "keySetProduction": "Set Production", "keySetPoint": "Set Point", "keyRebuild": "Rebuild", "keyIncrement": "Increment", "keyRefreshing": "Refreshing", "keySwitchingToIncrementalMapMode": "Switching to incremental map mode", "keyAreYouSureYouWantToDeleteThisPoint": "Are you sure you want to delete this point?", "keyEraseWall": "<PERSON><PERSON>", "keySelectThePointYouWantToEditOrDelete": "Select the point you want to edit or delete", "keyExitingMapBuildingMode": "Exiting Map Building Mode", "keyFinalPoint": "Final Point", "keyRouteName": "Route Name", "keySavingRoutes": "Saving Routes", "keyExitEditMap": "Exit Edit Map", "keyPleaseMakeSureTheCurrentMapContainsAtLeastOneChargingPointOneProductionAndOneNaviRoute": "Please make sure the current map contains at-least one charging point, one production and one navi route", "keyOperator": "Operator", "keyEmergency": "Emergency", "keyCharging": "Charging", "keyExport": "Export", "keyNoAdsFound": "Due to insufficient ads wallet deduction can not be performed", "keyNoInteractionFound": "No Interaction found", "keyPleaseContactTheAdminToDownloadNewApp": "Please contact the admin to download new App", "keyAlert": "<PERSON><PERSON>", "keyAgency": "Agency", "keyWouldYouLikeToUpdateTheAPK": "Would you like to update the APK?", "keyTheCurrentAPKVersionIs": "The current APK version is", "keySomethingWentWrong": "Something went wrong", "keyOperatorInterruption": "Operator Interruption", "keyStartTime": "Start Time", "keyEndTime": "End Time", "keyChargingInterruption": "Charging Interruption", "keyStartCharge": "Start Charge", "keyEndCharge": "End Charge", "keyMapView": "Map View", "keySorryIAmCurrentlyAssistingAnotherShopper": "Sorry I’m Currently Assisting Another Shopper", "keyWouldYouLikeToUploadData": "Would you like to upload data?", "keyWouldYouLikeToDeductWallet": "Would you like to deduct wallet?", "keyAmountOf": "Amount of", "keyWillBeDeductedForATotalOf": "will be deducted for a total of", "keyAdsDisplayed": "ads displayed", "keyYouAreReadyToVisit": "You’re Ready To Visit", "keyOn": "on", "keyIfYouDontNeedAssistanceIWillContinueOnMyWayIn": "If you don’t need assistance, I’ll continue on my way in", "keyExploreCategories": "Explore Categories", "keyYouHaveArrivedAtYourDestination": "You've Arrived At Your Destination!", "keyWouldYouLikeToGoToADifferentStore": "Would You Like To Go To A Different Store?", "keyCruiseModeStartingIn": "Cruise Mode Starting in ", "keyResetFilter": "Reset Filter", "keyCount": "Count", "keySrNo": "Sr. No", "keyExportToCSV": "Export to CSV", "keyFilter": "Filter", "keyDate": "Date", "keyEmergencyInterruption": "Emergency Interruption", "keyStoreInfoInterruption": "Store Info Interruption", "keyVendor": "<PERSON><PERSON><PERSON>"}