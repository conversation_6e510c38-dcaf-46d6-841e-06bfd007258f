<svg width="29" height="46" viewBox="0 0 29 46" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.31092 4.20386L6.31092 3.74873C6.31092 1.92636 7.79337 0.443908 9.61575 0.443908L20.0023 0.443908C21.8247 0.443908 23.3072 1.92636 23.3072 3.74873L23.3072 4.20386L23.7793 4.20386C26.6441 4.20386 28.9726 6.53423 28.9726 9.39715L28.9726 40.5569C28.9726 43.4199 26.6441 45.7502 23.7793 45.7502L5.83881 45.7502C2.97399 45.7502 0.645508 43.4199 0.645508 40.5569L0.645506 9.39716C0.645506 6.53423 2.97399 4.20386 5.8388 4.20386L6.31092 4.20386Z" fill="url(#paint0_linear_53_2128)"/>
<g filter="url(#filter0_bd_53_2128)">
<path d="M11.037 35.302C10.8592 35.302 10.678 35.2679 10.5084 35.1967C9.96989 34.9711 9.68399 34.4368 9.82694 33.9204L12.2305 25.2844H7.59127C7.12586 25.2844 6.69867 25.0529 6.48424 24.6833C6.26982 24.3138 6.30473 23.8686 6.574 23.5287L15.6879 12.027C16.0203 11.6085 16.627 11.4467 17.1573 11.6322C17.6908 11.8177 18.015 12.303 17.9418 12.8091L16.903 20.0901H21.72C22.1904 20.0901 22.621 20.326 22.8321 20.7C23.0448 21.074 23.0033 21.5237 22.7257 21.8621L12.0427 34.8479C11.8016 35.1418 11.4243 35.302 11.037 35.302Z" fill="url(#paint1_linear_53_2128)" shape-rendering="crispEdges"/>
<path d="M10.5455 35.1085L10.5454 35.1085C10.0451 34.8989 9.79071 34.4097 9.9191 33.9459L12.3226 25.31L12.3564 25.1888H12.2305H7.59127C7.15614 25.1888 6.76244 24.9723 6.56695 24.6354C6.37268 24.3005 6.40353 23.8978 6.64895 23.5881L15.7628 12.0865C16.068 11.7022 16.6309 11.5494 17.1257 11.7225L17.1259 11.7225C17.6229 11.8953 17.9128 12.3414 17.8472 12.7954L17.8472 12.7956L16.8083 20.0766L16.7927 20.1857H16.903H21.72C22.16 20.1857 22.5565 20.4065 22.7488 20.747L22.7489 20.7473C22.9416 21.086 22.9049 21.4929 22.6517 21.8014L11.9688 34.7872L11.9687 34.7873C11.748 35.0564 11.3991 35.2064 11.037 35.2064C10.8713 35.2064 10.7027 35.1746 10.5455 35.1085Z" stroke="url(#paint2_linear_53_2128)" stroke-width="0.191246" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_bd_53_2128" x="-2.9845" y="2.22683" width="35.2805" height="42.4045" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.66462"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_53_2128"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.39058"/>
<feGaussianBlur stdDeviation="1.19529"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.513726 0 0 0 0 0.988235 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_53_2128" result="effect2_dropShadow_53_2128"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_53_2128" result="shape"/>
</filter>
<linearGradient id="paint0_linear_53_2128" x1="14.698" y1="0.22027" x2="14.698" y2="45.7502" gradientUnits="userSpaceOnUse">
<stop stop-color="#009AF1"/>
<stop offset="1" stop-color="#83D1FF"/>
</linearGradient>
<linearGradient id="paint1_linear_53_2128" x1="14.6558" y1="11.5561" x2="14.6558" y2="35.302" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_53_2128" x1="14.6558" y1="11.5561" x2="14.6558" y2="35.302" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
