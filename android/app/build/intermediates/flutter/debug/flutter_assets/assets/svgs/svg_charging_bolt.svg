<svg width="25" height="33" viewBox="0 0 25 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_bd_386_4694)">
<path d="M8.36354 28C8.16024 28 7.95314 27.9598 7.75934 27.8758C7.14375 27.6098 6.81695 26.9798 6.98035 26.3708L9.72773 16.1877H4.42487C3.89287 16.1877 3.40457 15.9147 3.15947 15.479C2.91438 15.0433 2.95428 14.5183 3.26207 14.1175L13.6797 0.555278C14.0597 0.061787 14.7532 -0.128959 15.3593 0.0897869C15.9692 0.308533 16.3397 0.880771 16.2561 1.47751L15.0686 10.0628H20.5747C21.1124 10.0628 21.6045 10.3411 21.8458 10.7821C22.089 11.2231 22.0415 11.7533 21.7242 12.1523L9.51303 27.4645C9.23753 27.811 8.80623 28 8.36354 28Z" fill="url(#paint0_linear_386_4694)" shape-rendering="crispEdges"/>
<path d="M7.79727 27.788L7.79737 27.788C7.97864 27.8666 8.17273 27.9044 8.36354 27.9044C8.77994 27.9044 9.18253 27.7265 9.43818 27.405L9.43827 27.4049L21.6494 12.0928L7.79727 27.788ZM7.79727 27.788C7.22094 27.5389 6.92282 26.9544 7.07268 26.3957C7.07269 26.3956 7.0727 26.3956 7.0727 26.3956L9.82005 16.2126L9.85257 16.0921M7.79727 27.788L9.85257 16.0921M9.85257 16.0921H9.72773M9.85257 16.0921H9.72773M9.72773 16.0921H4.42487C3.92458 16.0921 3.46964 15.8354 3.24282 15.4321C3.0168 15.0303 3.05306 14.5466 3.33791 14.1758L13.7555 0.613618C14.1087 0.154846 14.7577 -0.0256748 15.3268 0.179732L15.327 0.179796M9.72773 16.0921L15.327 0.179796M15.327 0.179796C15.899 0.384963 16.238 0.917216 16.1614 1.46424L16.1614 1.46441M15.327 0.179796L16.1614 1.46441M16.1614 1.46441L14.9739 10.0497L14.9588 10.1585M16.1614 1.46441L14.9588 10.1585M14.9588 10.1585H15.0686M14.9588 10.1585H15.0686M15.0686 10.1585H20.5747C21.0805 10.1585 21.5388 10.4202 21.762 10.828L21.7621 10.8283M15.0686 10.1585L21.7621 10.8283M21.7621 10.8283C21.9863 11.2348 21.9431 11.7233 21.6495 12.0927L21.7621 10.8283Z" stroke="url(#paint1_linear_386_4694)" stroke-width="0.191246" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_bd_386_4694" x="-6.32923" y="-9.32923" width="37.6585" height="46.6585" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.66462"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_386_4694"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.39058"/>
<feGaussianBlur stdDeviation="1.19529"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.513726 0 0 0 0 0.988235 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_386_4694" result="effect2_dropShadow_386_4694"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_386_4694" result="shape"/>
</filter>
<linearGradient id="paint0_linear_386_4694" x1="12.5" y1="0" x2="12.5" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_386_4694" x1="12.5" y1="0" x2="12.5" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
