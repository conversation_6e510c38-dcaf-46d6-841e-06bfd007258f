<svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_730_1612)">
<rect width="49" height="49" rx="24.5" fill="url(#paint0_linear_730_1612)"/>
</g>
<defs>
<filter id="filter0_i_730_1612" x="0" y="0" width="49" height="53.1587" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.15869"/>
<feGaussianBlur stdDeviation="23.1847"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670588 0 0 0 0 0.760784 0 0 0 0 0.831373 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_730_1612"/>
</filter>
<linearGradient id="paint0_linear_730_1612" x1="0" y1="0" x2="0" y2="49" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F6FBFF"/>
</linearGradient>
</defs>
</svg>
