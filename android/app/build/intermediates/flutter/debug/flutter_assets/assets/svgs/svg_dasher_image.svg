<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_40908" data-name="Rectangle 40908" width="14.57" height="38.689" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_40906" data-name="Rectangle 40906" width="0.454" height="6.666" fill="none"/>
    </clipPath>
    <clipPath id="clip-Custom_Size_88">
      <rect width="40" height="40"/>
    </clipPath>
  </defs>
  <g id="Custom_Size_88" data-name="Custom Size – 88" clip-path="url(#clip-Custom_Size_88)">
    <g id="Group_110871" data-name="Group 110871" transform="translate(-1437.809 -9335.981)">
      <g id="Group_110869" data-name="Group 110869" transform="translate(1444.809 9336.981)">
        <rect id="Rectangle_40909" data-name="Rectangle 40909" width="14.501" height="21.994" rx="7.251" transform="translate(26.587 13.263) rotate(90)" fill="#ff6c3e"/>
        <g id="Group_110866" data-name="Group 110866">
          <g id="Group_110865" data-name="Group 110865" clip-path="url(#clip-path)">
            <path id="Path_200049" data-name="Path 200049" d="M0,29.276,1.12,22.44l2.12-4.157V11.813L1.03,6.83.142,0Z" transform="translate(11.33 4.708)" fill="#0b131e"/>
            <g id="Group_110864" data-name="Group 110864" transform="translate(0 0)">
              <g id="Group_110863" data-name="Group 110863" clip-path="url(#clip-path)">
                <path id="Path_200050" data-name="Path 200050" d="M2.852.329l.056.009,1.556.25a1.845,1.845,0,0,1,.248.174c.184.143,4.8,4.016,4.8,4.016L9.432,11.2l1.043.383a.616.616,0,0,1,.083.309c.006.2-.077,14.841-.077,14.841l-.04.414-.022.082a.155.155,0,0,1-.125.109c-.109.027-.956.332-.956.332L9.316,33.82l.027.16L4.335,38.088s-.022.062-.218.085-3.506.512-3.506.512L.185,31.2,0,9.488.867,0,2.052.181Z" transform="translate(2.01 0)" fill="#354e6a"/>
                <path id="Path_200051" data-name="Path 200051" d="M2.852,0s.2-.071.214.535.018,6.746.018,6.746a5.91,5.91,0,0,0,.267,1.693c.258.668.668,1.72.668,1.72l-.111,17.3s-.558,1.417-.745,2.023A5.979,5.979,0,0,0,2.906,32.1c.018.945-.053,5.667-.053,5.667s.009.989-.232.918l-.481-.544a8.708,8.708,0,0,0-.282-1.7c-.208-.612-1.282-3.5-1.282-3.5L.5,32.538l.031-8.213A3.263,3.263,0,0,1,.1,22.733c-.04-1.016-.16-4.277-.067-5.24s.04-3.034.535-3.449l.043-3.012L.63,6.453a4.78,4.78,0,0,1,.3-1.194,36.879,36.879,0,0,0,1.319-3.7,5.855,5.855,0,0,0,.12-.89V.519S2.8.029,2.852,0" transform="translate(0 0)" fill="#0b131e"/>
                <g id="Group_110862" data-name="Group 110862" transform="translate(4.583 0.409)" opacity="0.64">
                  <g id="Group_110861" data-name="Group 110861">
                    <g id="Group_110860" data-name="Group 110860" clip-path="url(#clip-path-3)">
                      <path id="Path_200052" data-name="Path 200052" d="M.454.045S.339.125.321.561s-.08,6.1-.08,6.1H0L.094.713A1.971,1.971,0,0,1,.227,0Z" transform="translate(0 0)" fill="#0b131e"/>
                    </g>
                  </g>
                </g>
                <path id="Path_200053" data-name="Path 200053" d="M2.913,9.865A.05.05,0,0,1,2.9,9.856,23.382,23.382,0,0,1,.231,7.429,8.74,8.74,0,0,1,.017,4.7s0,0,0,0C.017,4.617,0,.588.084.265.136.073.189.017.225,0A.049.049,0,0,1,.274.012L5.119,4.06a.05.05,0,0,1,.018.039l-.08,6.4a.043.043,0,0,0,0,.006.051.051,0,0,1-.066.053Z" transform="translate(6.385 0.701)" fill="#274259"/>
                <path id="Path_200054" data-name="Path 200054" d="M2.916.508a.054.054,0,0,0-.02.01A23.307,23.307,0,0,0,.231,2.944,8.741,8.741,0,0,0,.017,5.669s0,0,0,0c0,.084-.021,4.113.067,4.436.052.192.105.248.141.262a.049.049,0,0,0,.049-.009L5.119,6.313a.051.051,0,0,0,.018-.039V0Z" transform="translate(6.211 27.671)" fill="#274259"/>
                <path id="Path_200055" data-name="Path 200055" d="M7.777,2.768.649.032A.478.478,0,0,0,0,.478V19.815a.478.478,0,0,0,.589.464l7.132-1.71.146-.192L7.984,3.553v-.4S8,2.8,7.777,2.768" transform="translate(4.583 8.769)" fill="#638cb1"/>
                <path id="Path_200056" data-name="Path 200056" d="M1.7,1.029A.952.952,0,0,1,.849,2.058.952.952,0,0,1,0,1.029.952.952,0,0,1,.849,0,.952.952,0,0,1,1.7,1.029" transform="translate(7.61 6.045)" fill="#881d25"/>
                <path id="Path_200057" data-name="Path 200057" d="M1.7,1.029A.952.952,0,0,1,.849,2.058.952.952,0,0,1,0,1.029.952.952,0,0,1,.849,0,.952.952,0,0,1,1.7,1.029" transform="translate(7.61 31.828)" fill="#0b131e"/>
              </g>
            </g>
          </g>
        </g>
        <path id="Path_200058" data-name="Path 200058" d="M4.468,3.234a1,1,0,0,1-.707-.293L2.234,1.414.707,2.941a1,1,0,0,1-1.414,0,1,1,0,0,1,0-1.414L1.527-.707A1,1,0,0,1,2.234-1a1,1,0,0,1,.707.293L5.175,1.527a1,1,0,0,1-.707,1.707Z" transform="translate(21.133 18.478) rotate(90)" fill="#fff"/>
      </g>
    </g>
  </g>
</svg>
