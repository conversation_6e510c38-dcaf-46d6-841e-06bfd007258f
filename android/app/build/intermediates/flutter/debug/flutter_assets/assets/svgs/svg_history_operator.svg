<svg id="robot_Info" data-name="robot Info" xmlns="http://www.w3.org/2000/svg" width="510" height="563" viewBox="0 0 510 563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_6" data-name="Rectangle 6" width="510" height="563" rx="50" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.902"/>
      <stop offset="0.06" stop-color="#fff" stop-opacity="0.812"/>
      <stop offset="0.26" stop-color="#fff" stop-opacity="0.569"/>
      <stop offset="0.44" stop-color="#fff" stop-opacity="0.369"/>
      <stop offset="0.61" stop-color="#fff" stop-opacity="0.212"/>
      <stop offset="0.77" stop-color="#fff" stop-opacity="0.09"/>
      <stop offset="0.9" stop-color="#fff" stop-opacity="0.02"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Layer_2" data-name="Layer 2">
    <g id="Layer_1" data-name="Layer 1">
      <g id="Group_110273" data-name="Group 110273">
        <g id="Rectangle_47" data-name="Rectangle 47">
          <rect id="Rectangle_4" data-name="Rectangle 4" width="510" height="563" rx="50" fill="#212121"/>
          <rect id="Rectangle_5" data-name="Rectangle 5" width="509" height="562" rx="49.5" transform="translate(0.5 0.5)" fill="none" stroke="#3a3a3a" stroke-width="1"/>
        </g>
        <g id="Group_4" data-name="Group 4" clip-path="url(#clip-path)">
          <g id="Mask_Group_1" data-name="Mask Group 1">
            <ellipse id="Ellipse_11" data-name="Ellipse 11" cx="181" cy="103" rx="181" ry="103" transform="translate(169 385)" fill="#4a4a4a" opacity="0.52"/>
          </g>
        </g>
        <path id="Path_28" data-name="Path 28" d="M413.65,417.04v14.18a24.478,24.478,0,0,1-4.64,14.49.066.066,0,0,1-.04.05c-.37.54-.77,1.08-1.19,1.61l-.09.12c-.4.51-.83,1.01-1.28,1.51-.05.06-.1.13-.16.19q-.645.705-1.35,1.41c-.08.08-.15.16-.24.25-.45.44-.93.88-1.41,1.31-.11.1-.22.21-.33.31-.47.41-.97.82-1.47,1.22-.15.12-.28.24-.42.35-.5.39-1.03.78-1.55,1.16-.17.12-.33.25-.5.37-.55.39-1.12.77-1.7,1.14-.17.11-.32.23-.5.34-.73.47-1.49.93-2.27,1.38-.02.02-.05.03-.07.05a76,76,0,0,1-15.9,6.76,99.244,99.244,0,0,1-14.9,3.26c-3.08.43-6.2.73-9.34.91q-3.135.165-6.29.17c-2.1,0-4.21-.06-6.3-.17-13.31-.74-26.31-3.83-36.99-9.24l-5.04,2.91a7.559,7.559,0,0,1-1.01,3.47,4.4,4.4,0,0,1-2.24,1.88l-3.82,2.21a3.954,3.954,0,0,1-.98.56l-57.39,33.14-.2.12a12.616,12.616,0,0,1-14.06-20.95c.2-.14.42-.26.63-.39v-.03l54.74-31.61-.12.06,4.11-2.37,4.92-2.84h.01a4.7,4.7,0,0,1,2.69-.69c-.37-.54-.72-1.08-1.05-1.63,0-.01-.01-.01-.01-.02a23.943,23.943,0,0,1-3.54-12.29V416.96c-.16-9.83,6.23-19.69,19.21-27.18,25.65-14.81,67.23-14.81,92.87,0,13.02,7.51,19.41,17.4,19.21,27.26Z" fill="#212121"/>
        <g id="Group_43" data-name="Group 43">
          <g id="Magnifier">
            <path id="Path_29" data-name="Path 29" d="M369.43,384.08c25.52,14.73,25.52,38.62,0,53.35-10.69,6.17-24.17,9.75-38.1,10.75,19.31,1.39,39.48-2.19,54.31-10.75,25.52-14.73,25.52-38.62,0-53.35-14.82-8.56-35-12.14-54.31-10.75,13.93,1,27.41,4.58,38.1,10.75Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_30" data-name="Path 30" d="M293.47,384.15c-25.65,14.81-25.64,38.81,0,53.62s67.22,14.8,92.86,0,25.64-38.81,0-53.62-67.22-14.81-92.86,0h0Zm7.66,4.42c21.41-12.36,56.13-12.36,77.54,0s21.41,32.41,0,44.77-56.13,12.36-77.54,0S279.72,400.93,301.13,388.57Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_31" data-name="Path 31" d="M378.67,373.49c14.05,8.11,18.88,19.54,14.49,29.93-2.3-5.44-7.13-10.6-14.49-14.84-21.41-12.36-56.13-12.36-77.54,0-7.36,4.25-12.19,9.4-14.49,14.84-4.4-10.39.43-21.81,14.49-29.93,21.41-12.36,56.13-12.36,77.54,0Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_32" data-name="Path 32" d="M301.02,378.19c-21.41,12.36-21.41,32.4,0,44.76s56.12,12.36,77.53,0,21.41-32.4,0-44.76-56.12-12.36-77.53,0Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_34" data-name="Path 34" d="M301.02,380.43c-10.21,5.89-15.54,13.54-16.01,21.26-.52-8.47,4.81-17.03,16.01-23.5,21.41-12.36,56.12-12.36,77.53,0,11.2,6.47,16.53,15.03,16.01,23.5-.47-7.72-5.8-15.36-16.01-21.26-21.41-12.36-56.12-12.36-77.53,0Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_35" data-name="Path 35" d="M293.47,422.68c25.64,14.81,67.22,14.81,92.86,0s25.64-38.81,0-53.61-67.22-14.81-92.86,0-25.64,38.81,0,53.62Zm7.66-49.19c21.41-12.36,56.13-12.36,77.54,0s21.41,32.41,0,44.77-56.13,12.36-77.54,0S279.72,385.85,301.13,373.49Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_36" data-name="Path 36" d="M386.33,422.68c12.63-7.29,19.02-16.81,19.21-26.36v14.27c.17,9.83-6.23,19.68-19.21,27.18-25.64,14.8-67.22,14.8-92.86,0-12.99-7.5-19.38-17.36-19.22-27.18h0V396.26c.16,9.58,6.56,19.12,19.22,26.43,25.64,14.81,67.22,14.81,92.86,0h0Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_37" data-name="Path 37" d="M293.47,424.7c1.62.93,3.29,1.8,5.03,2.62l-11.04,6.38-9.57-5.52,11.05-6.38q2.115,1.5,4.54,2.9h0Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_38" data-name="Path 38" d="M287.46,433.7l11.05-6.38v11.05l-11.05,6.38Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_39" data-name="Path 39" d="M291.61,441.62c0,3.22-1.28,5.36-3.29,6.1h0l-63.66,36.76-12.17-22.1,63.66-36.76h0c1.63-1.05,3.94-.97,6.51.51,4.94,2.85,8.94,9.79,8.94,15.49h0Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_40" data-name="Path 40" d="M283.4,450.56c2-.74,3.29-2.88,3.29-6.1,0-5.71-4-12.64-8.94-15.49-2.57-1.48-4.88-1.56-6.51-.5h0l-4.22,2.43c1.63-1.06,3.94-.98,6.51.5,4.94,2.85,8.94,9.79,8.94,15.49,0,2.57-.83,4.44-2.18,5.47l3.11-1.8Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_41" data-name="Path 41" d="M225.93,483.75c1.26-1.05,2.02-2.89,2.02-5.37,0-5.7-4-12.64-8.94-15.49-2.35-1.35-4.47-1.53-6.06-.75a11.842,11.842,0,0,0-1.07.66,12.625,12.625,0,1,0,14.27,20.83l-.21.12Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
            <path id="Path_42" data-name="Path 42" d="M224.66,484.48Z" fill="#fff" stroke="#000" stroke-miterlimit="10" stroke-width="0.76"/>
          </g>
          <g id="Group_42" data-name="Group 42">
            <g id="Group_41" data-name="Group 41">
              <g id="Group_7" data-name="Group 7">
                <g id="Group_5" data-name="Group 5">
                  <path id="Path_43" data-name="Path 43" d="M378.93,249.45a6.025,6.025,0,0,1-2.69,4.71l-3.7,2.15a5.982,5.982,0,0,0,2.69-4.71c.49-3.97-1.15-8.78-3.87-12.59a11.722,11.722,0,0,1-2.35-6.57l.08-22.98a6.92,6.92,0,0,0-3.15-5.45,2.212,2.212,0,0,0-2.24-.21l3.7-2.15a2.212,2.212,0,0,1,2.24.21,6.977,6.977,0,0,1,3.15,5.45l-.09,22.98a11.9,11.9,0,0,0,2.35,6.57c2.72,3.8,4.37,8.61,3.88,12.58Z" fill="#373b42"/>
                  <path id="Path_44" data-name="Path 44" d="M372.55,256.68a.371.371,0,0,1-.19-.69,5.663,5.663,0,0,0,2.51-4.43c.45-3.69-1.01-8.41-3.8-12.33a12.073,12.073,0,0,1-2.42-6.78l.08-22.98a6.6,6.6,0,0,0-2.96-5.13,1.908,1.908,0,0,0-1.83-.23l-.04.02a.383.383,0,0,1-.51-.13.374.374,0,0,1,.13-.51c.02,0,.03-.02.05-.03l3.65-2.13a2.552,2.552,0,0,1,2.61.21,7.38,7.38,0,0,1,3.33,5.78l-.09,22.98a11.5,11.5,0,0,0,2.28,6.35c2.91,4.07,4.42,9,3.95,12.85h0a6.422,6.422,0,0,1-2.87,4.98l-3.7,2.15h0a.308.308,0,0,1-.18.05Zm-7.28-53.35a3.721,3.721,0,0,1,.86.37,7.371,7.371,0,0,1,3.34,5.78l-.08,22.98a11.4,11.4,0,0,0,2.27,6.35c2.9,4.06,4.41,8.99,3.94,12.85a7.841,7.841,0,0,1-.94,3l1.4-.81a5.684,5.684,0,0,0,2.51-4.43h0c.45-3.68-1.01-8.4-3.81-12.33a12.3,12.3,0,0,1-2.42-6.78l.09-22.98a6.575,6.575,0,0,0-2.96-5.13,1.874,1.874,0,0,0-1.86-.21l-2.32,1.35Z" fill="#373b42"/>
                </g>
                <g id="Group_6" data-name="Group 6">
                  <path id="Path_45" data-name="Path 45" d="M365.95,204.02a6.92,6.92,0,0,1,3.15,5.45l-.09,22.97a11.93,11.93,0,0,0,2.35,6.58c2.71,3.8,4.36,8.61,3.88,12.58-.56,4.6-4.07,6.54-8.35,4.63-.37-.17-.74-.35-1.1-.56-5.25-3.03-9.5-10.39-9.48-16.42,0-3.65,1.59-5.98,3.99-6.59,1.48-.36,2.4-1.77,2.4-3.85l.09-22.97c0-2,1.43-2.81,3.16-1.8Z" fill="#fff"/>
                  <path id="Path_46" data-name="Path 46" d="M370.02,257.33a8.249,8.249,0,0,1-3.29-.76,12.1,12.1,0,0,1-1.13-.58c-5.35-3.09-9.68-10.6-9.67-16.75.01-3.68,1.61-6.28,4.27-6.95,1.32-.32,2.11-1.63,2.12-3.49l.09-22.97a2.288,2.288,0,0,1,3.72-2.13h0a7.382,7.382,0,0,1,3.34,5.78l-.09,22.97a11.578,11.578,0,0,0,2.28,6.36c2.9,4.06,4.41,8.98,3.95,12.84a6.327,6.327,0,0,1-2.93,5.01,5.31,5.31,0,0,1-2.66.67Zm-5.48-53.37a1.242,1.242,0,0,0-.64.16,1.837,1.837,0,0,0-.74,1.7l-.09,22.97c0,2.23-1.01,3.8-2.68,4.21-2.35.59-3.7,2.86-3.71,6.23-.02,5.91,4.16,13.13,9.3,16.1a10.852,10.852,0,0,0,1.07.55,5.859,5.859,0,0,0,5.28.13,5.663,5.663,0,0,0,2.56-4.46c.45-3.68-1.02-8.4-3.81-12.32a12.306,12.306,0,0,1-2.42-6.79l.09-22.97a6.566,6.566,0,0,0-2.97-5.13,2.474,2.474,0,0,0-1.23-.39Z" fill="#373b42"/>
                </g>
              </g>
              <g id="Group_15" data-name="Group 15">
                <g id="Group_10" data-name="Group 10">
                  <g id="Group_8" data-name="Group 8">
                    <path id="Path_47" data-name="Path 47" d="M388.01,294.74c-2.4-1.4-5.73-1.21-9.41.92l-40.32,23.28c-7.32,4.22-13.22,14.45-13.19,22.85l.09,32.32c.01,9.01,3.2,15.35,8.34,18.34l-36.96-21.49c-5.14-2.98-8.32-9.32-8.35-18.34l-.09-32.32c-.01-8.39,5.89-18.62,13.19-22.84l40.33-23.29c3.67-2.12,7.01-2.32,9.41-.92l36.96,21.48Z" fill="#f0f2f5"/>
                    <path id="Path_48" data-name="Path 48" d="M333.52,392.83a.449.449,0,0,1-.19-.05l-36.96-21.49c-5.48-3.17-8.51-9.8-8.54-18.66l-.09-32.32c-.01-8.5,5.99-18.89,13.38-23.16l40.33-23.29c3.68-2.13,7.15-2.46,9.79-.92l36.93,21.46a.145.145,0,0,1,.03.02h0a.372.372,0,0,1-.38.64h0l-.03-.02c-2.38-1.37-5.58-1.04-9.01.94l-40.31,23.28c-7.2,4.15-13.03,14.26-13,22.53l.09,32.32c.01,8.57,2.91,14.96,8.15,18.02h0a.371.371,0,0,1-.19.69Zm14.04-120.09a11.638,11.638,0,0,0-5.74,1.77l-40.33,23.28c-7.18,4.15-13.02,14.25-13,22.52l.09,32.32c.02,8.59,2.92,14.99,8.16,18.02l33.02,19.19c-3.21-3.62-4.94-9.04-4.95-15.73l-.09-32.32c-.03-8.51,5.97-18.9,13.38-23.17l40.31-23.29a12,12,0,0,1,6.7-1.85l-34.25-19.9a6.465,6.465,0,0,0-3.3-.85Z" fill="#373b42"/>
                  </g>
                  <g id="Group_9" data-name="Group 9">
                    <path id="Path_49" data-name="Path 49" d="M378.59,295.67c7.31-4.22,13.25-.83,13.28,7.57l.09,32.32c.05,18.14-12.7,40.25-28.5,49.37l-9.6,5.54c-15.8,9.12-28.64,1.79-28.69-16.35l-.09-32.32c-.02-8.4,5.88-18.63,13.19-22.85Z" fill="#fff"/>
                    <path id="Path_50" data-name="Path 50" d="M341.03,394.76a15.153,15.153,0,0,1-7.65-1.96c-5.51-3.17-8.55-9.81-8.58-18.69l-.09-32.32c-.02-8.5,5.98-18.9,13.38-23.17l40.32-23.28c3.66-2.12,7.13-2.45,9.76-.94,2.61,1.5,4.06,4.64,4.07,8.82l.09,32.32c.05,18.24-12.82,40.53-28.69,49.69l-9.6,5.54a26.437,26.437,0,0,1-13.02,3.98Zm43.5-100.54a11.7,11.7,0,0,0-5.75,1.77h0l-40.32,23.28c-7.2,4.15-13.03,14.26-13.01,22.53l.09,32.32c.02,8.61,2.94,15.02,8.21,18.05,5.3,3.05,12.38,2.34,19.92-2.02l9.6-5.54c15.66-9.04,28.37-31.04,28.31-49.04l-.09-32.32c0-3.91-1.32-6.81-3.7-8.18a6.44,6.44,0,0,0-3.27-.83Zm-5.94,1.45Z" fill="#373b42"/>
                  </g>
                </g>
                <g id="Group_11" data-name="Group 11">
                  <path id="Path_51" data-name="Path 51" d="M377.06,293.07l-39.54,22.82a9.11,9.11,0,0,1-9.13-.01l-25.51-14.83a1.571,1.571,0,0,1-.79-1.37v-4.5l44.88-25.92,30.86,17.94.02,4.49a1.587,1.587,0,0,1-.79,1.38Z" fill="#373b42"/>
                  <path id="Path_52" data-name="Path 52" d="M332.97,317.48a9.533,9.533,0,0,1-4.77-1.28l-25.51-14.83a1.952,1.952,0,0,1-.97-1.69v-4.5a.35.35,0,0,1,.18-.32l44.89-25.92a.352.352,0,0,1,.37,0l30.86,17.94a.382.382,0,0,1,.18.32l.02,4.49a1.957,1.957,0,0,1-.98,1.7h0L337.7,316.21a9.516,9.516,0,0,1-4.74,1.27ZM302.45,295.4v4.29a1.258,1.258,0,0,0,.61,1.05l25.51,14.83a8.735,8.735,0,0,0,8.76.01l39.54-22.82a1.218,1.218,0,0,0,.61-1.06l-.02-4.28L346.97,269.7l-44.52,25.7Zm74.61-2.33Z" fill="#373b42"/>
                </g>
                <g id="Group_14" data-name="Group 14">
                  <g id="Group_12" data-name="Group 12">
                    <path id="Path_53" data-name="Path 53" d="M387.86,240.26c-2.41-1.4-5.74-1.21-9.41.92l-40.33,23.28c-7.3,4.22-13.22,14.45-13.19,22.85l.05,17.11c.01,4.17,1.48,7.11,3.85,8.48l-36.96-21.47c-2.37-1.38-3.84-4.31-3.85-8.5l-.05-17.11c-.03-8.39,5.89-18.63,13.19-22.84l40.33-23.29c3.67-2.12,7-2.32,9.41-.92l36.96,21.48Z" fill="#f0f2f5"/>
                    <path id="Path_54" data-name="Path 54" d="M328.84,313.28a.374.374,0,0,1-.18-.05h0L291.7,291.75c-2.59-1.5-4.02-4.63-4.04-8.82l-.05-17.11c-.03-8.51,5.97-18.9,13.38-23.16l40.33-23.28c3.67-2.13,7.15-2.45,9.79-.92l36.95,21.47h0a.363.363,0,0,1,.13.51.377.377,0,0,1-.51.14h0c-2.4-1.4-5.61-1.07-9.04.92l-40.33,23.28c-7.19,4.16-13.03,14.27-13,22.53l.05,17.11c.01,3.89,1.31,6.79,3.66,8.15h.01a.368.368,0,0,1,.13.51.36.36,0,0,1-.32.18Zm18.57-95.03a11.638,11.638,0,0,0-5.74,1.77L301.34,243.3c-7.2,4.15-13.03,14.25-13,22.52l.05,17.11c.01,3.91,1.31,6.81,3.67,8.18l34.26,19.91a11.794,11.794,0,0,1-1.71-6.59l-.05-17.11c-.03-8.5,5.97-18.9,13.37-23.17l40.33-23.29a11.963,11.963,0,0,1,6.7-1.85l-34.25-19.9a6.465,6.465,0,0,0-3.3-.85Z" fill="#373b42"/>
                  </g>
                  <g id="Group_13" data-name="Group 13">
                    <path id="Path_55" data-name="Path 55" d="M378.44,241.18c7.31-4.22,13.25-.83,13.28,7.57l.05,17.11c.02,8.41-5.88,18.63-13.19,22.85l-40.32,23.28c-7.31,4.22-13.25.84-13.28-7.57l-.05-17.11c-.02-8.4,5.88-18.63,13.19-22.85Z" fill="#fff"/>
                    <path id="Path_56" data-name="Path 56" d="M332.32,314.18a7.223,7.223,0,0,1-3.64-.93c-2.61-1.5-4.05-4.64-4.07-8.83l-.05-17.11c-.02-8.5,5.98-18.9,13.38-23.17l40.32-23.28c3.67-2.12,7.13-2.45,9.76-.94,2.61,1.5,4.06,4.64,4.07,8.82l.05,17.11c.02,8.5-5.98,18.9-13.38,23.17L338.44,312.3a12.48,12.48,0,0,1-6.13,1.87Zm46.12-73,.18.32L338.3,264.78c-7.19,4.15-13.03,14.26-13.01,22.53l.05,17.11c0,3.91,1.32,6.82,3.69,8.18,2.39,1.38,5.6,1.04,9.02-.94l40.32-23.28c7.19-4.15,13.03-14.26,13.01-22.53l-.05-17.11c-.01-3.91-1.32-6.81-3.7-8.18s-5.59-1.04-9.02.93l-.18-.32Z" fill="#373b42"/>
                  </g>
                </g>
              </g>
              <g id="Group_18" data-name="Group 18">
                <g id="Group_16" data-name="Group 16">
                  <path id="Path_57" data-name="Path 57" d="M317.31,285.04a6.025,6.025,0,0,1-2.69,4.71l-3.7,2.15a5.982,5.982,0,0,0,2.69-4.71c.49-3.97-1.15-8.78-3.87-12.59a11.722,11.722,0,0,1-2.35-6.57l.08-22.98a6.92,6.92,0,0,0-3.15-5.45,2.212,2.212,0,0,0-2.24-.21l3.7-2.15a2.212,2.212,0,0,1,2.24.21,6.977,6.977,0,0,1,3.15,5.45l-.09,22.98a11.9,11.9,0,0,0,2.35,6.57c2.72,3.8,4.37,8.61,3.88,12.58Z" fill="#373b42"/>
                  <path id="Path_58" data-name="Path 58" d="M310.92,292.27a.371.371,0,0,1-.19-.69,5.663,5.663,0,0,0,2.51-4.43c.45-3.69-1.01-8.41-3.8-12.33a12.077,12.077,0,0,1-2.42-6.79l.08-22.98a6.6,6.6,0,0,0-2.96-5.13,1.906,1.906,0,0,0-1.83-.23l-.04.02a.383.383,0,0,1-.51-.13.374.374,0,0,1,.13-.51c.02,0,.03-.02.05-.03l3.65-2.12a2.575,2.575,0,0,1,2.61.21,7.39,7.39,0,0,1,3.34,5.78l-.09,22.98a11.469,11.469,0,0,0,2.28,6.35c2.91,4.07,4.42,9,3.95,12.85h0a6.422,6.422,0,0,1-2.87,4.98l-3.7,2.15a.414.414,0,0,1-.19.05Zm-7.27-53.36a3.721,3.721,0,0,1,.86.37,7.355,7.355,0,0,1,3.34,5.78l-.08,22.98a11.377,11.377,0,0,0,2.27,6.35c2.9,4.06,4.41,8.98,3.94,12.85a7.841,7.841,0,0,1-.94,3l1.4-.81a5.684,5.684,0,0,0,2.51-4.43h0c.45-3.68-1.01-8.4-3.81-12.33a12.3,12.3,0,0,1-2.42-6.78l.09-22.98a6.56,6.56,0,0,0-2.96-5.13,1.874,1.874,0,0,0-1.86-.21l-2.32,1.36Z" fill="#373b42"/>
                </g>
                <g id="Group_17" data-name="Group 17">
                  <path id="Path_59" data-name="Path 59" d="M304.32,239.6a6.92,6.92,0,0,1,3.15,5.45l-.09,22.97a11.93,11.93,0,0,0,2.35,6.58c2.71,3.8,4.36,8.61,3.88,12.58-.56,4.6-4.07,6.54-8.35,4.63-.37-.17-.74-.35-1.1-.56-5.25-3.03-9.5-10.39-9.48-16.42,0-3.65,1.59-5.98,3.99-6.59,1.48-.36,2.4-1.77,2.4-3.85l.09-22.97c0-2,1.43-2.81,3.16-1.8Z" fill="#fff"/>
                  <path id="Path_60" data-name="Path 60" d="M308.4,292.91a8.249,8.249,0,0,1-3.29-.76,10.974,10.974,0,0,1-1.13-.58c-5.35-3.09-9.69-10.6-9.67-16.75,0-3.68,1.61-6.28,4.27-6.95,1.32-.32,2.11-1.63,2.12-3.49l.09-22.97a2.288,2.288,0,0,1,3.72-2.13,7.355,7.355,0,0,1,3.34,5.78l-.09,22.97a11.578,11.578,0,0,0,2.28,6.36c2.9,4.06,4.41,8.98,3.95,12.84a6.327,6.327,0,0,1-2.93,5.01,5.31,5.31,0,0,1-2.66.67Zm-5.49-53.37a1.242,1.242,0,0,0-.64.16,1.844,1.844,0,0,0-.74,1.71l-.09,22.97c0,2.23-1.01,3.8-2.68,4.21-2.35.59-3.7,2.86-3.71,6.23-.02,5.91,4.15,13.13,9.3,16.1a12.013,12.013,0,0,0,1.07.55,5.859,5.859,0,0,0,5.28.13,5.663,5.663,0,0,0,2.56-4.46c.44-3.68-1.02-8.4-3.81-12.32a12.306,12.306,0,0,1-2.42-6.79l.09-22.97a6.566,6.566,0,0,0-2.97-5.13h0a2.452,2.452,0,0,0-1.23-.38Z" fill="#373b42"/>
                </g>
              </g>
              <g id="Group_20" data-name="Group 20">
                <path id="Path_61" data-name="Path 61" d="M382.47,271.22a8.584,8.584,0,0,1-3.92,6.79L338.23,301.3c-2.18,1.26-3.95.25-3.95-2.24l-.05-17.12a8.656,8.656,0,0,1,3.92-6.79l40.33-23.27c2.16-1.26,3.93-.25,3.95,2.24l.04,17.11Z" fill="#373b42"/>
                <g id="Group_19" data-name="Group 19">
                  <path id="Path_62" data-name="Path 62" d="M347.73,275.51c1-.58,1.81-.11,1.81,1.03a4,4,0,0,1-1.8,3.12l-5.03,2.9c-1,.58-1.82.12-1.82-1.03a4,4,0,0,1,1.81-3.13l5.03-2.9Z" fill="#fff"/>
                  <path id="Path_63" data-name="Path 63" d="M361.94,276.48a.451.451,0,0,1,.53-.02.612.612,0,0,1,.18.56,9.659,9.659,0,0,1-4.27,6.75c-2.15,1.24-4,.47-4.29-1.81a1.309,1.309,0,0,1,.17-.76,1.347,1.347,0,0,1,.52-.58l7.16-4.13Z" fill="#fff"/>
                  <path id="Path_64" data-name="Path 64" d="M373.97,260.37c1-.58,1.81-.11,1.81,1.03a4,4,0,0,1-1.8,3.12l-5.03,2.9c-1,.58-1.81.11-1.81-1.03a3.984,3.984,0,0,1,1.8-3.12l5.03-2.91Z" fill="#fff"/>
                </g>
              </g>
              <g id="Group_40" data-name="Group 40">
                <path id="Path_65" data-name="Path 65" d="M382.66,340.92c.03,12.22-8.6,27.16-19.25,33.31l-9.6,5.53c-10.63,6.15-19.31,1.19-19.35-11.03l-.09-32.32a8.629,8.629,0,0,1,3.92-6.79l40.33-23.27c2.16-1.26,3.93-.25,3.95,2.24l.09,32.32Z" fill="#373b42"/>
                <g id="Group_39" data-name="Group 39">
                  <g id="Group_21" data-name="Group 21">
                    <line id="Line_1" data-name="Line 1" y1="6.23" x2="10.79" transform="translate(362.42 335.12)" fill="none"/>
                    <path id="Path_66" data-name="Path 66" d="M362.42,341.72a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l10.78-6.22a.374.374,0,1,1,.37.65l-10.78,6.22a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_22" data-name="Group 22">
                    <line id="Line_2" data-name="Line 2" y1="7.19" x2="12.46" transform="translate(346.77 343.19)" fill="none"/>
                    <path id="Path_67" data-name="Path 67" d="M346.77,350.75a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l12.45-7.19a.374.374,0,1,1,.37.65l-12.45,7.19a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_23" data-name="Group 23">
                    <line id="Line_3" data-name="Line 3" y1="0.75" x2="1.3" transform="translate(343.34 351.61)" fill="none"/>
                    <path id="Path_68" data-name="Path 68" d="M343.34,352.73a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l1.3-.75a.374.374,0,1,1,.37.65l-1.3.75a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_24" data-name="Group 24">
                    <line id="Line_4" data-name="Line 4" x1="2.99" y2="1.72" transform="translate(370.22 341.57)" fill="none"/>
                    <path id="Path_69" data-name="Path 69" d="M370.22,343.66a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l2.99-1.73a.374.374,0,1,1,.37.65l-2.99,1.73a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_25" data-name="Group 25">
                    <line id="Line_5" data-name="Line 5" y1="6.71" x2="11.62" transform="translate(356.19 344.68)" fill="none"/>
                    <path id="Path_70" data-name="Path 70" d="M356.19,351.76a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l11.63-6.71a.374.374,0,1,1,.37.65l-11.63,6.71a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_26" data-name="Group 26">
                    <line id="Line_6" data-name="Line 6" y1="4.27" x2="7.4" transform="translate(365.81 328.63)" fill="none"/>
                    <path id="Path_71" data-name="Path 71" d="M365.81,333.27a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l7.39-4.27a.374.374,0,1,1,.37.65L366,333.23a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_27" data-name="Group 27">
                    <line id="Line_7" data-name="Line 7" y1="6.2" x2="10.74" transform="translate(352.65 334.3)" fill="none"/>
                    <path id="Path_72" data-name="Path 72" d="M352.65,340.87a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l10.74-6.2a.374.374,0,1,1,.37.65l-10.74,6.2a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_28" data-name="Group 28">
                    <line id="Line_8" data-name="Line 8" y1="3.44" x2="5.96" transform="translate(343.34 342.43)" fill="none"/>
                    <path id="Path_73" data-name="Path 73" d="M343.34,346.24a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l5.96-3.44a.374.374,0,1,1,.37.65l-5.96,3.44a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_29" data-name="Group 29">
                    <line id="Line_9" data-name="Line 9" y1="2.57" x2="4.46" transform="translate(368.59 322.26)" fill="none"/>
                    <path id="Path_74" data-name="Path 74" d="M368.59,325.2a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l4.46-2.57a.374.374,0,1,1,.37.65l-4.46,2.57a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_30" data-name="Group 30">
                    <line id="Line_10" data-name="Line 10" y1="7.72" x2="13.37" transform="translate(353 326.11)" fill="none"/>
                    <path id="Path_75" data-name="Path 75" d="M353,334.2a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l13.37-7.71a.374.374,0,1,1,.37.65l-13.37,7.71a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_31" data-name="Group 31">
                    <line id="Line_11" data-name="Line 11" y1="4.03" x2="6.99" transform="translate(343.34 335.37)" fill="none"/>
                    <path id="Path_76" data-name="Path 76" d="M343.34,339.78a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l6.99-4.03a.374.374,0,1,1,.37.65l-6.99,4.03a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_32" data-name="Group 32">
                    <line id="Line_12" data-name="Line 12" x1="9.31" y2="5.37" transform="translate(343.34 353.43)" fill="none"/>
                    <path id="Path_77" data-name="Path 77" d="M343.34,359.18a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l9.31-5.38a.374.374,0,1,1,.37.65l-9.31,5.38a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_33" data-name="Group 33">
                    <line id="Line_13" data-name="Line 13" y1="3.65" x2="6.33" transform="translate(366.88 348.01)" fill="none"/>
                    <path id="Path_78" data-name="Path 78" d="M366.88,352.03a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l6.33-3.65a.374.374,0,1,1,.37.65l-6.33,3.65a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_34" data-name="Group 34">
                    <line id="Line_14" data-name="Line 14" y1="5.94" x2="10.3" transform="translate(353.9 353.21)" fill="none"/>
                    <path id="Path_79" data-name="Path 79" d="M353.9,359.52a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l10.3-5.94a.374.374,0,1,1,.37.65l-10.3,5.94a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_35" data-name="Group 35">
                    <line id="Line_15" data-name="Line 15" y1="4.47" x2="7.74" transform="translate(343.34 360.78)" fill="none"/>
                    <path id="Path_80" data-name="Path 80" d="M343.34,365.62a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l7.74-4.47a.374.374,0,1,1,.37.65l-7.74,4.47a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_36" data-name="Group 36">
                    <line id="Line_16" data-name="Line 16" y1="0.2" x2="0.34" transform="translate(370.7 355.7)" fill="none"/>
                    <path id="Path_81" data-name="Path 81" d="M370.7,356.27a.37.37,0,0,1-.32-.19.377.377,0,0,1,.14-.51l.34-.2a.374.374,0,1,1,.37.65l-.34.2a.407.407,0,0,1-.18.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_37" data-name="Group 37">
                    <line id="Line_17" data-name="Line 17" y1="4.1" x2="7.1" transform="translate(361.78 356.95)" fill="none"/>
                    <path id="Path_82" data-name="Path 82" d="M361.78,361.42a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l7.11-4.1a.374.374,0,1,1,.37.65l-7.11,4.1a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                  <g id="Group_38" data-name="Group 38">
                    <line id="Line_18" data-name="Line 18" y1="9.07" x2="15.71" transform="translate(343.34 362.62)" fill="none"/>
                    <path id="Path_83" data-name="Path 83" d="M343.34,372.06a.382.382,0,0,1-.32-.18.377.377,0,0,1,.14-.51l15.71-9.07a.374.374,0,1,1,.37.65l-15.71,9.07a.414.414,0,0,1-.19.05Z" fill="#fff"/>
                  </g>
                </g>
              </g>
            </g>
            <path id="Path_104" data-name="Path 104" d="M399.5,290.27,366.03,390.71c.01,7.18-4.98,14.05-14.07,19.32-9.6,5.59-22.45,8.65-36.17,8.65s-26.63-3.08-36.29-8.65c-9.19-5.31-14.27-12.19-14.32-19.39h-.03L231.64,290.05c-2.9-10.76,3.04-21.77,17.5-30.77,16.12-10.03,39.88-15.82,66.14-15.82s50.1,5.76,66.36,15.82c14.69,9.06,20.75,20.16,17.85,30.99Z" transform="translate(24.41 6)" opacity="0.57" fill="url(#linear-gradient)"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
