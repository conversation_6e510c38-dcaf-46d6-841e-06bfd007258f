<svg id="Asset_5" data-name="Asset 5" xmlns="http://www.w3.org/2000/svg" width="510" height="563"
    viewBox="0 0 510 563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_9" data-name="Rectangle 9" width="510" height="563" rx="50" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.902"/>
      <stop offset="0.06" stop-color="#fff" stop-opacity="0.812"/>
      <stop offset="0.26" stop-color="#fff" stop-opacity="0.569"/>
      <stop offset="0.44" stop-color="#fff" stop-opacity="0.369"/>
      <stop offset="0.61" stop-color="#fff" stop-opacity="0.212"/>
      <stop offset="0.77" stop-color="#fff" stop-opacity="0.09"/>
      <stop offset="0.9" stop-color="#fff" stop-opacity="0.02"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Layer_2" data-name="Layer 2">
    <g id="Layer_1" data-name="Layer 1">
      <g id="Group_110273" data-name="Group 110273">
        <g id="Rectangle_47" data-name="Rectangle 47">
          <rect id="Rectangle_7" data-name="Rectangle 7" width="510" height="563" rx="50" fill="#212121"/>
          <rect id="Rectangle_8" data-name="Rectangle 8" width="509" height="562" rx="49.5" transform="translate(0.5 0.5)" fill="none" stroke="#3a3a3a" stroke-width="1"/>
        </g>
        <g id="Group_44" data-name="Group 44" clip-path="url(#clip-path)">
          <g id="Mask_Group_1" data-name="Mask Group 1">
            <ellipse id="Ellipse_11" data-name="Ellipse 11" cx="181" cy="103" rx="181" ry="103" transform="translate(169 385)" fill="#4a4a4a" opacity="0.52"/>
          </g>
        </g>
        <path id="Path_85" data-name="Path 85" d="M233.21,401.67v16.55a28.582,28.582,0,0,0,5.41,16.91c.01.02.02.05.05.06.43.63.9,1.26,1.39,1.88.03.05.07.09.1.14.47.6.97,1.18,1.49,1.76.06.07.12.15.19.22.5.55,1.03,1.1,1.58,1.65.09.09.18.19.28.29.53.51,1.09,1.03,1.65,1.53.13.12.26.25.39.36.55.48,1.13.96,1.72,1.42.18.14.33.28.49.41.58.45,1.2.91,1.81,1.35.2.14.39.29.58.43.64.46,1.31.9,1.98,1.33.2.13.37.27.58.4.85.55,1.74,1.09,2.65,1.61.02.02.06.04.08.06a88.882,88.882,0,0,0,18.55,7.89,115.639,115.639,0,0,0,17.39,3.8c3.59.5,7.23.85,10.9,1.06q3.66.2,7.34.2c2.45,0,4.91-.07,7.35-.2,15.53-.86,30.7-4.47,43.16-10.78l5.88,3.4a8.919,8.919,0,0,0,1.18,4.05,5.093,5.093,0,0,0,2.61,2.19l4.46,2.58a4.433,4.433,0,0,0,1.14.65l66.96,38.67.23.14a14.721,14.721,0,0,0,16.4-24.45c-.23-.16-.49-.3-.74-.45v-.04l-63.87-36.88c.05.02.09.05.14.07l-4.8-2.77-5.74-3.31h-.01a5.54,5.54,0,0,0-3.14-.81c.43-.63.84-1.26,1.23-1.9,0-.01.01-.01.01-.02a27.971,27.971,0,0,0,4.13-14.34v-17.2c.19-11.47-7.27-22.97-22.41-31.71-29.93-17.28-78.45-17.28-108.36,0-15.19,8.76-22.65,20.3-22.42,31.81Z" fill="#212121"/>
        <g id="Magnifier">
          <path id="Path_86" data-name="Path 86" d="M280.36,373.32c12.75-7.36,28.82-11.63,45.43-12.82-23.02-1.65-47.08,2.61-64.76,12.82-30.43,17.57-30.43,46.05,0,63.61,17.68,10.21,41.73,14.47,64.75,12.82-16.61-1.19-32.68-5.46-45.43-12.82-30.43-17.57-30.43-46.05,0-63.61h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_87" data-name="Path 87" d="M370.94,373.4c-30.58-17.65-80.15-17.65-110.73,0s-30.58,46.28,0,63.93,80.15,17.65,110.73,0,30.58-46.28,0-63.93Zm-9.14,58.65c-25.53,14.74-66.92,14.74-92.46,0s-25.53-38.64,0-53.38,66.93-14.74,92.46,0S387.33,417.31,361.8,432.05Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_88" data-name="Path 88" d="M269.34,360.69c25.53-14.74,66.93-14.74,92.46,0,16.76,9.68,22.52,23.3,17.28,35.69-2.74-6.49-8.5-12.63-17.28-17.7-25.53-14.74-66.93-14.74-92.46,0-8.77,5.07-14.53,11.21-17.27,17.7-5.24-12.39.52-26.01,17.27-35.68h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_89" data-name="Path 89" d="M361.94,366.29c-25.53-14.74-66.91-14.74-92.44,0s-25.53,38.63,0,53.37,66.92,14.74,92.44,0,25.53-38.63,0-53.37Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_91" data-name="Path 91" d="M361.94,368.97c-25.53-14.74-66.91-14.74-92.44,0-12.18,7.03-18.53,16.14-19.09,25.35-.61-10.1,5.74-20.31,19.09-28.02,25.53-14.74,66.92-14.74,92.44,0,13.35,7.71,19.71,17.92,19.09,28.02-.56-9.21-6.92-18.32-19.09-25.35Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_92" data-name="Path 92" d="M370.94,355.41c-30.58-17.65-80.15-17.65-110.73,0s-30.58,46.27,0,63.93,80.15,17.66,110.73,0S401.52,373.07,370.94,355.41Zm-9.14,58.66c-25.53,14.74-66.92,14.74-92.46,0s-25.53-38.64,0-53.38,66.93-14.74,92.46,0S387.33,399.33,361.8,414.07Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_93" data-name="Path 93" d="M260.21,419.34c30.58,17.66,80.15,17.66,110.73,0,15.09-8.71,22.72-20.1,22.92-31.52v17.09h0c.2,11.72-7.43,23.47-22.91,32.41-30.57,17.65-80.15,17.65-110.73,0-15.48-8.94-23.11-20.69-22.91-32.41V387.9c.23,11.39,7.85,22.74,22.91,31.44h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_94" data-name="Path 94" d="M370.93,421.74c1.93-1.11,3.73-2.27,5.42-3.46l13.17,7.6-11.41,6.59-13.17-7.61q3.105-1.455,6-3.12h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_95" data-name="Path 95" d="M378.11,445.65l-13.17-7.6V424.87l13.17,7.61v13.18Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_96" data-name="Path 96" d="M373.15,441.93c0-6.8,4.77-15.07,10.66-18.47,3.06-1.77,5.82-1.86,7.76-.59h0l75.91,43.82-14.51,26.35-75.91-43.83h0c-2.39-.88-3.92-3.43-3.92-7.28h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_97" data-name="Path 97" d="M382.94,452.58h0l3.71,2.14c-1.61-1.23-2.6-3.45-2.6-6.52,0-6.8,4.77-15.07,10.66-18.47,3.06-1.77,5.82-1.86,7.76-.59l-5.03-2.91h0c-1.94-1.25-4.7-1.16-7.76.61-5.89,3.4-10.66,11.67-10.66,18.47,0,3.84,1.53,6.39,3.92,7.27Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_98" data-name="Path 98" d="M451.22,492.01a15.049,15.049,0,0,0,17.01-24.83c-.41-.28-.84-.54-1.28-.78-1.9-.93-4.44-.73-7.23.89-5.89,3.4-10.66,11.67-10.66,18.47,0,2.96.9,5.15,2.41,6.4Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_99" data-name="Path 99" d="M452.97,493.02Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_105" data-name="Path 105" d="M399.5,290.27,366.03,390.71c.01,7.18-4.98,14.05-14.07,19.32-9.6,5.59-22.45,8.65-36.17,8.65s-26.63-3.08-36.29-8.65c-9.19-5.31-14.27-12.19-14.32-19.39h-.03L231.64,290.05c-2.9-10.76,3.04-21.77,17.5-30.77,16.12-10.03,39.88-15.82,66.14-15.82s50.1,5.76,66.36,15.82c14.69,9.06,20.75,20.16,17.85,30.99Z" opacity="0.57" fill="url(#linear-gradient)"/>
        </g>
        <g id="Exclamation_mark" data-name="Exclamation mark">
          <path id="Path_100" data-name="Path 100" d="M311.87,337.17l-4.58-2.64q-.735-14.28-4.86-36.35l-5.49-29.66q-4.965-26.685-4.96-37.01,0-9.36,4.84-12.28t12.63,1.58a37.494,37.494,0,0,1,17.58,30.57q0,9.24-5.27,31.47l-5.58,23.27a199.952,199.952,0,0,0-4.32,31.05h0Zm-2.29,16.02q7.035,4.065,12.03,12.74a33.888,33.888,0,0,1,4.99,16.97q0,8.175-4.99,11.08t-12.03-1.15q-7.05-4.065-12.03-12.74t-4.99-16.85q0-8.175,4.94-11.18t12.09,1.12Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_101" data-name="Path 101" d="M342.63,221.95a34.407,34.407,0,0,0-12.58-13.03q-7.785-4.5-12.63-1.58l-20.6,11.89q4.845-2.91,12.63,1.58a37.494,37.494,0,0,1,17.58,30.57q0,9.24-5.27,31.47l-5.58,23.27a199.952,199.952,0,0,0-4.32,31.05l20.6-11.89a200.129,200.129,0,0,1,4.32-31.05l5.58-23.27q5.265-22.23,5.27-31.47a34.726,34.726,0,0,0-5-17.53h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
          <path id="Path_102" data-name="Path 102" d="M342.21,354.04q-4.995-8.67-12.03-12.74-7.155-4.125-12.09-1.12l-20.6,11.89q4.935-3,12.09,1.12,7.035,4.065,12.03,12.74a33.888,33.888,0,0,1,4.99,16.97q0,8.175-4.99,11.08l20.6-11.89q4.995-2.91,4.99-11.08a33.888,33.888,0,0,0-4.99-16.97Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
        </g>
      </g>
    </g>
  </g>
</svg>
