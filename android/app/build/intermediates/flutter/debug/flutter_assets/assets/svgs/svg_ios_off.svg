<svg width="114" height="69" viewBox="0 0 114 69" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="iOS Toggle">
<g clip-path="url(#clip0_1346_9152)">
<rect id="Track" width="113.516" height="69" rx="34.5" fill="#39393D"/>
<g id="Knob" filter="url(#filter0_d_1346_9152)">
<rect x="4.45117" y="4.45312" width="60.0968" height="60.0968" rx="30.0484" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_1346_9152" x="-11.1295" y="-4.4501" width="91.258" height="91.2589" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6.67742"/>
<feGaussianBlur stdDeviation="7.79032"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1346_9152"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1346_9152" result="shape"/>
</filter>
<clipPath id="clip0_1346_9152">
<rect width="113.516" height="69" rx="34.5" fill="white"/>
</clipPath>
</defs>
</svg>
