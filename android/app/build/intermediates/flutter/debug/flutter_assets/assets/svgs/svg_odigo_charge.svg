<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="260.555" height="528.036" viewBox="0 0 260.555 528.036">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_40900" data-name="Rectangle 40900" width="133.461" height="182.98" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_40817" data-name="Rectangle 40817" width="214.989" height="528.036" fill="none" stroke="#707070" stroke-width="0.5"/>
    </clipPath>
  </defs>
  <g id="Group_110840" data-name="Group 110840" transform="translate(-428.506 -513.5)">
    <g id="Group_110833" data-name="Group 110833" transform="translate(-5 59.003)">
      <g id="Group_110835" data-name="Group 110835" transform="translate(560.6 777.517)">
        <path id="Path_199976" data-name="Path 199976" d="M152.286,68.232v126.44L76.8,174.442V47.7Z" transform="translate(-76.799 -11.693)" fill="#ececec" stroke="#2c2c2c" stroke-width="1"/>
        <path id="Path_199977" data-name="Path 199977" d="M125.1,135.028l7.171-15.928-1.51,10.417h3.774l-7.775,16.758,2.793-12.38-4.378,1.057Z" transform="translate(-95.509 -29.195)" fill="#3298b4"/>
        <g id="Group_110836" data-name="Group 110836" transform="translate(0)">
          <g id="Group_110835-2" data-name="Group 110835" clip-path="url(#clip-path)">
            <path id="Path_199978" data-name="Path 199978" d="M124.084,60.9,48.6,40.371,70.036,17.8s75.789,20.457,75.336,20.608-21.363,22.5-21.363,22.5Z" transform="translate(-48.597 -4.363)" fill="#ececec" stroke="#707070" stroke-width="1"/>
            <path id="Path_199979" data-name="Path 199979" d="M0,13.437,37.215,0l74.807,20.834L75.336,34.044Z" transform="translate(21.438)" fill="#ececec" stroke="#707070" stroke-width="1"/>
            <path id="Path_199980" data-name="Path 199980" d="M57.974,27.6V171.909L0,189.745V63.305l21.287-22.5Z" transform="translate(75.487 -6.766)" fill="#ececec" stroke="#000" stroke-width="1"/>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_110837" data-name="Group 110837" transform="translate(-15 -25.75)">
      <g id="Group_110830" data-name="Group 110830" transform="translate(446.557 562.96)">
        <g id="Group_110562" data-name="Group 110562" transform="translate(-3.051 -23.71)" clip-path="url(#clip-path-2)">
          <path id="Path_199700" data-name="Path 199700" d="M6.109,224.127s4.143-59.961,5.015-72.39,2.592-52.718,2.592-52.718,1.55-6.59,2.035-8.625,5.33-21.9,5.33-21.9,1.454-4.361,4.361-6.009S36.587,55.9,36.587,55.9s1.841-.678,5.621,0,5.426.872,5.426.872v9.957L135.263,89.4l8.939,2.4,19.406-12.429s1.963-1.744,11.338,0,28.127,6.105,30.09,6.977,6.105,5.887,7.631,13.3a155.317,155.317,0,0,1,2.18,17.444s-6.541,14.391-12.864,26.165-22.022,42.737-22.022,42.737L175.6,199.076l-2.18,50.586L166.444,392.7s.509,4.651,2.18,5.669l4.143,19.406s3.1,8.044,3,12.5a166.526,166.526,0,0,1-3.295,28.2c-2.52,11.823-8.818,35.662-9.884,38.763s-8.722,36.05-35.759,44.772c0,0-11.484,7.995-44.19,7.123l-1.89.066-.727,1.024a31.139,31.139,0,0,0-3.561-2.544c-.655-.218-24.833,1.26-46.2-21.417,0,0-7.123-11.871-9.158-17.685l-.776.388a62.965,62.965,0,0,1-7.219-21.853L10.93,474.029s-1.453-15.118,1.309-22.968L3.517,409.488S.61,391.9.755,389.719s.993-36.728.993-36.728" transform="translate(-0.305 -22.631)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199701" data-name="Path 199701" d="M1.749,352.99,6.109,224.127s4.143-59.961,5.015-72.39,2.592-52.718,2.592-52.718,1.55-6.59,2.035-8.625,5.33-21.9,5.33-21.9,1.454-4.361,4.361-6.009S36.587,55.9,36.587,55.9s1.841-.678,5.621,0,5.426.872,5.426.872v9.957L135.263,89.4l8.939,2.4,19.406-12.429s1.963-1.744,11.338,0,28.127,6.105,30.09,6.977,6.105,5.887,7.631,13.3a155.317,155.317,0,0,1,2.18,17.444s-6.541,14.391-12.864,26.165-22.022,42.737-22.022,42.737L175.6,199.076l-2.18,50.586L166.444,392.7s.509,4.651,2.18,5.669l4.143,19.406s3.1,8.044,3,12.5a166.526,166.526,0,0,1-3.295,28.2c-2.52,11.823-8.818,35.662-9.884,38.763s-8.722,36.05-35.759,44.772c0,0-11.484,7.995-44.19,7.123l-1.89.066-.727,1.024a31.139,31.139,0,0,0-3.561-2.544c-.655-.218-24.833,1.26-46.2-21.417,0,0-7.123-11.871-9.158-17.685l-.776.388a62.965,62.965,0,0,1-7.219-21.853L10.93,474.029s-1.453-15.118,1.309-22.968L3.517,409.488S.61,391.9.755,389.719,1.749,352.99,1.749,352.99Z" transform="translate(-0.305 -22.631)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199702" data-name="Path 199702" d="M89.4,502.118l-.516,3.93s3.052,2.084,8.916,0,30.938-15.8,40.047-24.518,12.889-9.593,16.475-24.032,8.867-42.543,8.867-42.543L184.7,260.726s1.309-13.591,20.5-48.478,25.075-40.556,26.165-56.255,1.793-27.788,1.793-27.788,3.973-12.889-13.373-16.377-22.095-5.621-25.39-4.943-42.761,24.4-42.761,24.4Z" transform="translate(-36.184 -43.46)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199703" data-name="Path 199703" d="M89.4,502.118l-.516,3.93s3.052,2.084,8.916,0,30.938-15.8,40.047-24.518,12.889-9.593,16.475-24.032,8.867-42.543,8.867-42.543L184.7,260.726s1.309-13.591,20.5-48.478,25.075-40.556,26.165-56.255,1.793-27.788,1.793-27.788,3.973-12.889-13.373-16.377-22.095-5.621-25.39-4.943-42.761,24.4-42.761,24.4Z" transform="translate(-36.184 -43.46)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199704" data-name="Path 199704" d="M98.34,71.363l3.779-11.629L87.147,56.972s-6.977-1.163-7.123-9.885,0-20.2,0-20.2.751-2.569,2.059-3.15S102.12,1.71,102.12,1.71s1.478-1.72,8.746-.557S181.56,11.474,181.56,11.474s7.558.582,7.462,8.14a71.6,71.6,0,0,1-1.163,12.6S186.115,39.19,177.1,45.585l-10.369,9.788L168.526,73.2s-12.5,5.96-18.9,7.414c0,0-4.909,2.689-15.137-.363l-34.237-7.39s-2.253-.458-1.914-1.5" transform="translate(-32.551 -0.305)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199705" data-name="Path 199705" d="M98.34,71.363l3.779-11.629L87.147,56.972s-6.977-1.163-7.123-9.885,0-20.2,0-20.2.751-2.569,2.059-3.15S102.12,1.71,102.12,1.71s1.478-1.72,8.746-.557S181.56,11.474,181.56,11.474s7.558.582,7.462,8.14a71.6,71.6,0,0,1-1.163,12.6S186.115,39.19,177.1,45.585l-10.369,9.788L168.526,73.2s-12.5,5.96-18.9,7.414c0,0-4.909,2.689-15.137-.363l-34.237-7.39S98,72.407,98.34,71.363Z" transform="translate(-32.551 -0.305)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199706" data-name="Path 199706" d="M3.129,409.787,4.437,379.7l3.78-76.46,6.251-131.407,2.325-45.789s-1.308-9.448,7.123-7.413S148.927,146.8,148.927,146.8s9.594.145,7.559,15.263-29.654,231.417-29.654,231.417l-7.268,58.435s-.727,20.205-16.717,31.689S70,506.283,63.745,507.591c0,0-3.343,2.762-9.449-.872,0,0-25.584-11.774-34.305-21.8,0,0-4.53-5.6-5.305-9.57S6.448,437.7,4.994,427.666s-1.865-17.879-1.865-17.879" transform="translate(-1.274 -48.174)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199707" data-name="Path 199707" d="M3.129,409.787,4.437,379.7l3.78-76.46,6.251-131.407,2.325-45.789s-1.308-9.448,7.123-7.413S148.927,146.8,148.927,146.8s9.594.145,7.559,15.263-29.654,231.417-29.654,231.417l-7.268,58.435s-.727,20.205-16.717,31.689S70,506.283,63.745,507.591c0,0-3.343,2.762-9.449-.872,0,0-25.584-11.774-34.305-21.8,0,0-4.53-5.6-5.305-9.57S6.448,437.7,4.994,427.666,3.129,409.787,3.129,409.787Z" transform="translate(-1.274 -48.174)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199708" data-name="Path 199708" d="M18.8,366.239l99.282,48.7,2.737,1.381s1.405.775,1.744-1.5,13.083-106.5,13.083-106.5l17.5-143.109s.872-2.871-2.907-3.525S31.7,134.085,31.7,134.085s-2.931-1.382-2.883,2.495-11.035,215-11.035,215l-.654,10.648s-.872,3.186,1.671,4.009" transform="translate(-6.913 -54.487)" fill="#ff6c3e" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199709" data-name="Path 199709" d="M18.8,366.239l99.282,48.7,2.737,1.381s1.405.775,1.744-1.5,13.083-106.5,13.083-106.5l17.5-143.109s.872-2.871-2.907-3.525S31.7,134.085,31.7,134.085s-2.931-1.382-2.883,2.495-11.035,215-11.035,215l-.654,10.648S16.255,365.416,18.8,366.239Z" transform="translate(-6.913 -54.487)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199710" data-name="Path 199710" d="M13.473,537.795,12.6,553.639s-.337,2.471,2.594,4.143,99.377,52.039,99.377,52.039,3.926,2.762,4.143-1.963l2.181-15.844s-.8-3.779-4.361-4.651L17.046,537.795s-2.531-2.059-3.573,0" transform="translate(-5.124 -218.556)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199711" data-name="Path 199711" d="M13.473,537.795,12.6,553.639s-.337,2.471,2.594,4.143,99.377,52.039,99.377,52.039,3.926,2.762,4.143-1.963l2.181-15.844s-.8-3.779-4.361-4.651L17.046,537.795S14.514,535.736,13.473,537.795Z" transform="translate(-5.124 -218.556)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199712" data-name="Path 199712" d="M77.573,657.561l6.784-16.717s.484-2.616,3.246-1.211,4.313,2.374,4.8,4.457,0,14.052,0,14.052l-.291,5.185s.34,3.973-3.1,2.568L78.59,660.032a2.594,2.594,0,0,1-1.017-2.471" transform="translate(-31.57 -260.216)" fill="#fff" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199713" data-name="Path 199713" d="M77.573,657.561l6.784-16.717s.484-2.616,3.246-1.211,4.313,2.374,4.8,4.457,0,14.052,0,14.052l-.291,5.185s.34,3.973-3.1,2.568L78.59,660.032A2.594,2.594,0,0,1,77.573,657.561Z" transform="translate(-31.57 -260.216)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199714" data-name="Path 199714" d="M82.085,39.247s.5-.87,6.22,0,49.374,8.114,49.374,8.114,11.629,0,11.7,7.995,0,23.4,0,23.4-1.744,5.524-5.451,4.87-41.809-8.381-41.809-8.381L86.763,72.4s-6.861-1.188-6.8-11.125.065-18.875.065-18.875a5.278,5.278,0,0,1,2.059-3.15" transform="translate(-32.551 -15.82)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199715" data-name="Path 199715" d="M82.085,39.247s.5-.87,6.22,0,49.374,8.114,49.374,8.114,11.629,0,11.7,7.995,0,23.4,0,23.4-1.744,5.524-5.451,4.87-41.809-8.381-41.809-8.381L86.763,72.4s-6.861-1.188-6.8-11.125.065-18.875.065-18.875A5.278,5.278,0,0,1,82.085,39.247Z" transform="translate(-32.551 -15.82)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199718" data-name="Path 199718" d="M88.885,46.946l.545,23.476,57.636,11.435V56.928Z" transform="translate(-36.184 -19.111)" fill="#fff" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199719" data-name="Path 199719" d="M88.885,46.946l.545,23.476,57.636,11.435V56.928Z" transform="translate(-36.184 -19.111)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199720" data-name="Path 199720" d="M89.74,47.093l.634,23.517-.944-.187-.545-23.476Z" transform="translate(-36.184 -19.111)" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="0.5"/>
          <path id="Path_199721" data-name="Path 199721" d="M282.137,88.977s.218-3.779,1.307-4.724S295,73.06,295,73.06s1.09-2.253,7.268-1.09S317.314,74.8,317.314,74.8a4.681,4.681,0,0,1,2.4,4.724c-.219,3.562-.727,23.839-.727,23.839H317.75s-1.6-6.468-6.977-7.995-28.636-6.4-28.636-6.4" transform="translate(-114.854 -29.162)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199722" data-name="Path 199722" d="M282.137,88.977s.218-3.779,1.307-4.724S295,73.06,295,73.06s1.09-2.253,7.268-1.09S317.314,74.8,317.314,74.8a4.681,4.681,0,0,1,2.4,4.724c-.219,3.562-.727,23.839-.727,23.839H317.75s-1.6-6.468-6.977-7.995S282.137,88.977,282.137,88.977Z" transform="translate(-114.854 -29.162)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199723" data-name="Path 199723" d="M259.356,364.765l-12.6,112.05s-.36,1.672.949,1.817,2.035-2.835,2.035-2.835l13.156-111.808s-.145-1.6-1.357-1.4a2.5,2.5,0,0,0-2.18,2.18" transform="translate(-100.431 -147.596)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199724" data-name="Path 199724" d="M259.356,364.765l-12.6,112.05s-.36,1.672.949,1.817,2.035-2.835,2.035-2.835l13.156-111.808s-.145-1.6-1.357-1.4A2.5,2.5,0,0,0,259.356,364.765Z" transform="translate(-100.431 -147.596)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199725" data-name="Path 199725" d="M279.081,308.89l-8.039,74.531-7.365,68.223-5.234,50.973s-2.713,26.359-2.713,27.231-1.163,11.532,5.717,8.818,10.466-6.4,12.695-12.307,2.166-6.535,2.166-6.535l-3.916-18.344a7.767,7.767,0,0,1-2.18-5.669c.03-3.709,8.869-186.922,8.869-186.922" transform="translate(-104.074 -125.745)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199726" data-name="Path 199726" d="M279.081,308.89l-8.039,74.531-7.365,68.223-5.234,50.973s-2.713,26.359-2.713,27.231-1.163,11.532,5.717,8.818,10.466-6.4,12.695-12.307,2.166-6.535,2.166-6.535l-3.916-18.344a7.767,7.767,0,0,1-2.18-5.669C270.242,492.1,279.081,308.89,279.081,308.89Z" transform="translate(-104.074 -125.745)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199727" data-name="Path 199727" d="M73.977,770.939a163.11,163.11,0,0,0,33.963,4.415c16.112.192,41.127.048,55.165-8.3,0,0,4.7-4.391,6.152-6.55,0,0-3.329,12.956-12.158,19.722s-21.42,19.434-75.982,19.722c0,0-24.459,2.735-51.668-23.609,0,0-8.973-14.108-8.829-43.187l.723-6.622s1.679,6.743,5.943,10.232L33.295,745l1.744-.775s4.361,8.335,9.4,12.792,14.342,9.5,20.351,10.563l6.009,1.066a14.715,14.715,0,0,0,3.179,2.295" transform="translate(-8.394 -295.759)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199728" data-name="Path 199728" d="M73.977,770.939a163.11,163.11,0,0,0,33.963,4.415c16.112.192,41.127.048,55.165-8.3,0,0,4.7-4.391,6.152-6.55,0,0-3.329,12.956-12.158,19.722s-21.42,19.434-75.982,19.722c0,0-24.459,2.735-51.668-23.609,0,0-8.973-14.108-8.829-43.187l.723-6.622s1.679,6.743,5.943,10.232L33.295,745l1.744-.775s4.361,8.335,9.4,12.792,14.342,9.5,20.351,10.563l6.009,1.066A14.715,14.715,0,0,0,73.977,770.939Z" transform="translate(-8.394 -295.759)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199729" data-name="Path 199729" d="M138.445,832l-22.689-.922a4.754,4.754,0,1,1,.386-9.5l22.689.922a4.754,4.754,0,0,1-.386,9.5" transform="translate(-45.266 -334.451)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199730" data-name="Path 199730" d="M138.445,832l-22.689-.922a4.754,4.754,0,1,1,.386-9.5l22.689.922a4.754,4.754,0,0,1-.386,9.5Z" transform="translate(-45.266 -334.451)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199731" data-name="Path 199731" d="M138.114,831.343l-14.669-.6a2.871,2.871,0,1,1,.233-5.737l14.669.6a2.871,2.871,0,1,1-.233,5.737" transform="translate(-49.131 -335.848)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199732" data-name="Path 199732" d="M138.114,831.343l-14.669-.6a2.871,2.871,0,1,1,.233-5.737l14.669.6a2.871,2.871,0,1,1-.233,5.737Z" transform="translate(-49.131 -335.848)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199733" data-name="Path 199733" d="M42.953,783.019l-7.341-7.12a4.754,4.754,0,0,1-.1-6.722h0a4.754,4.754,0,0,1,6.722-.1l7.341,7.12a4.754,4.754,0,0,1-6.619,6.825" transform="translate(-13.91 -312.533)" fill="#ececec" stroke="#7f7f7f" stroke-width="0.5"/>
          <path id="Path_199734" data-name="Path 199734" d="M42.953,783.019l-7.341-7.12a4.754,4.754,0,0,1-.1-6.722h0a4.754,4.754,0,0,1,6.722-.1l7.341,7.12a4.754,4.754,0,0,1-6.619,6.825Z" transform="translate(-13.91 -312.533)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199735" data-name="Path 199735" d="M44.972,782.393l-4.859-4.712a2.871,2.871,0,0,1,4-4.122l4.858,4.713a2.871,2.871,0,0,1-4,4.121" transform="translate(-15.975 -314.574)" fill="#ececec" stroke="#707070" stroke-width="0.5"/>
          <path id="Path_199736" data-name="Path 199736" d="M44.972,782.393l-4.859-4.712a2.871,2.871,0,0,1,4-4.122l4.858,4.713a2.871,2.871,0,0,1-4,4.121Z" transform="translate(-15.975 -314.574)" fill="none" stroke="#7f7f7f" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
          <path id="Path_199738" data-name="Path 199738" d="M60.275,775.074v4.044s7.123,5.887,8.94,6.251,2.253.291,3.053-.726c0,0,3.561,3.343,12.573,5.814,0,0,41.422,3.846,59.3-1.2,0,0-38.518,2.511-57.269-3.449,0,0-3.949-2.035-4.385-2.907,0,0-15.554-3.78-20.714-7.123,0,0-9.722-6.977-13.4-15.045" transform="translate(-19.693 -309.682)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"/>
        </g>
      </g>
    </g>
  </g>
</svg>
