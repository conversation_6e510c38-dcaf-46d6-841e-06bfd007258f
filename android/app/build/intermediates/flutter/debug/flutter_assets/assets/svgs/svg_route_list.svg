<svg xmlns="http://www.w3.org/2000/svg" width="510" height="563" viewBox="0 0 510 563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_96" data-name="Rectangle 96" width="510" height="563" rx="50" transform="translate(0 -0.025)" fill="#212121" stroke="#5e5e5e" stroke-width="1"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_89" data-name="Rectangle 89" width="224.192" height="142.693" fill="#212121"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_80" data-name="Rectangle 80" width="227.121" height="221.057" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_110274" data-name="Group 110274" transform="translate(0 0.025)">
    <g id="Rectangle_47" data-name="Rectangle 47" transform="translate(0 -0.025)" fill="#212121" stroke="#3a3a3a" stroke-width="1">
      <rect width="510" height="563" rx="50" stroke="none"/>
      <rect x="0.5" y="0.5" width="509" height="562" rx="49.5" fill="none"/>
    </g>
    <g id="Mask_Group_1" data-name="Mask Group 1" clip-path="url(#clip-path)">
      <ellipse id="Ellipse_11" data-name="Ellipse 11" cx="181" cy="103" rx="181" ry="103" transform="translate(169 384.975)" fill="#4a4a4a" opacity="0.52"/>
    </g>
    <g id="Group_110198" data-name="Group 110198" transform="translate(205 366.975)">
      <g id="Group_110122" data-name="Group 110122" transform="translate(6 -9)" clip-path="url(#clip-path-2)">
        <path id="Path_451" data-name="Path 451" d="M223.751,39.976a.363.363,0,0,1,.02-.06c5.088,31.529-37.121,47.821-64.281,46.939-10.459,0-29.889-1.2-43.339-8.369-.039-.117.3-9.285-.3-8.97v8.97L89.671,93.7c.55,3.34.2,8.916-2.82,10.2l-70.26,38.56a4.559,4.559,0,0,0,1.8-1.62C7.6,150.734-9.352,117.881,6.4,116.816a5.117,5.117,0,0,0-3.56.53C5.771,117.2,77.7,72.692,79.421,79.1l19.62-11.948c-7.7-6.97-7.1-19.753-7.71-29.93,5.956-50.234,129.621-49.93,132.42,2.75" fill="#212121"/>
      </g>
      <g id="Group_110121" data-name="Group 110121" transform="translate(-4 -95.689)">
        <g id="Group_110087" data-name="Group 110087" clip-path="url(#clip-path-3)">
          <path id="Path_398" data-name="Path 398" d="M201.642,88.392c-23.909-12.408-62.343-12.408-86.252,0-13.921,7.566-19.671,17.553-16.948,27.237,1.816-6.658,7.566-13.013,16.948-18.158,23.606-12.409,62.343-12.409,86.252,0a35.107,35.107,0,0,1,16.948,20.882c2.723-9.685-2.724-22.4-16.948-29.961" transform="translate(1.204 0.973)" fill="#ececec"/>
          <path id="Path_399" data-name="Path 399" d="M201.642,88.392c-23.909-12.408-62.343-12.408-86.252,0-13.921,7.566-19.671,17.553-16.948,27.237,1.816-6.658,7.566-13.013,16.948-18.158,23.606-12.409,62.343-12.409,86.252,0a35.107,35.107,0,0,1,16.948,20.882C221.313,108.669,215.866,95.958,201.642,88.392Z" transform="translate(1.204 0.973)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_400" data-name="Path 400" d="M190.433,89.829c-28.145-9.079-65.067-4.237-82.62,11.2s-8.777,35.41,19.368,44.488,65.067,4.238,82.62-11.2c17.251-15.435,8.777-35.409-19.369-44.488" transform="translate(1.216 1.05)" fill="#ececec"/>
          <path id="Path_401" data-name="Path 401" d="M190.433,89.829c-28.145-9.079-65.067-4.237-82.62,11.2s-8.777,35.41,19.368,44.488,65.067,4.238,82.62-11.2C227.052,118.883,218.578,98.909,190.433,89.829Z" transform="translate(1.216 1.05)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_402" data-name="Path 402" d="M92.156,114.289v9.382A31.976,31.976,0,0,0,95.788,138.2c2.118,3.328,4.539,6.053,10.894,10.894l1.513-9.986c-.3,0-14.224-16.645-16.04-24.817" transform="translate(1.134 1.407)" fill="#ececec"/>
          <path id="Path_403" data-name="Path 403" d="M92.156,114.289v9.382A31.976,31.976,0,0,0,95.788,138.2c2.118,3.328,4.539,6.053,10.894,10.894l1.513-9.986C107.893,139.1,93.972,122.46,92.156,114.289Z" transform="translate(1.134 1.407)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_404" data-name="Path 404" d="M116.073,140.91c7.869,3.027,10.593,3.934,13.619,4.842a115.539,115.539,0,0,0,17.251,3.329,114.6,114.6,0,0,0,30.566-.908c9.987-1.816,19.672-4.842,28.449-10.29,13.013-7.868,19.368-22.7,19.368-22.7v7.263c0,10.289-6.355,20.579-19.368,28.448a78.117,78.117,0,0,1-15.435,6.961,118.022,118.022,0,0,1-14.829,3.631,88.437,88.437,0,0,1-15.435,1.211c-10.593,0-30.264-1.211-43.883-8.474V140.91Z" transform="translate(1.429 1.418)" fill="#ececec"/>
          <path id="Path_405" data-name="Path 405" d="M116.073,140.91c7.869,3.027,10.593,3.934,13.619,4.842a115.539,115.539,0,0,0,17.251,3.329,114.6,114.6,0,0,0,30.566-.908c9.987-1.816,19.672-4.842,28.449-10.29,13.013-7.868,19.368-22.7,19.368-22.7v7.263c0,10.289-6.355,20.579-19.368,28.448a78.117,78.117,0,0,1-15.435,6.961,118.022,118.022,0,0,1-14.829,3.631,88.437,88.437,0,0,1-15.435,1.211c-10.593,0-30.264-1.211-43.883-8.474V140.91Z" transform="translate(1.429 1.418)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_406" data-name="Path 406" d="M116.706,153.861,64.652,184.125v-9.079l52.054-30.264Z" transform="translate(0.796 1.782)" fill="#ececec"/>
          <path id="Path_407" data-name="Path 407" d="M116.706,153.861,64.652,184.125v-9.079l52.054-30.264Z" transform="translate(0.796 1.782)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_408" data-name="Path 408" d="M52.694,171.967l9.381,5.448,54.778-31.777c34.8,13.316,66.883,9.685,89.883-3.632,12.712-7.263,19.067-16.947,19.37-26.632a26.046,26.046,0,0,0-3.935-13.619,25.174,25.174,0,0,0-2.42-3.631c-.605-.605-.908-1.211-1.513-1.816s-.908-1.211-1.513-1.816c-1.211-1.211-2.42-2.119-3.631-3.329a12.813,12.813,0,0,0-2.119-1.512,4.179,4.179,0,0,0-1.211-.908,16.9,16.9,0,0,0-2.421-1.513,2.288,2.288,0,0,0-1.211-.605c-26.026-15.132-68.4-15.132-94.422,0a2.3,2.3,0,0,1-1.211.605c-.605.3-1.513.908-2.119,1.211-1.816,1.211-3.632,2.723-5.145,3.934-.605.605-1.211.908-1.816,1.513L99.905,95.4c-.3.3-3.026,3.631-3.026,3.631-.3.3-.605.605-.605.908a17.362,17.362,0,0,1-1.211,2.119c-.3.3-.3.605-.605.908,0,.3-.3.605-.3.908-.3.3-.3.907-.605,1.21a1.057,1.057,0,0,1-.3.605,7.7,7.7,0,0,0-.605,2.119c0,.605-.3,1.211-.3,1.816v.908c0,.908-.3,1.815-.3,2.42v.908c0,7.566,2.724,18.159,14.829,25.119Zm55.988-40.553c-6.053-5.145-9.079-10.894-9.382-16.645v-3.026c0-.3.3-.908.3-1.211s.3-1.211.3-1.513a.937.937,0,0,1,.3-.605c0-.3.3-.605.3-.908s.3-.3.3-.605.3-.3.3-.605a5.11,5.11,0,0,0,.908-1.815c0-.3.3-.3.3-.605a23.1,23.1,0,0,0,2.421-3.329c.3-.3.605-.908,1.21-1.211a5.314,5.314,0,0,1,1.513-1.211l4.54-3.631c.605-.3,1.21-.908,1.815-1.211a.937.937,0,0,1,.605-.3l.605-.3c11.5-6.658,26.936-10.289,43.883-10.289,16.645,0,32.383,3.631,43.883,10.289a2.3,2.3,0,0,0,1.211.605c.605.3,1.513.908,2.119,1.211.3,0,.908.605.908.605.6.3,1.21.908,1.815,1.211a27.286,27.286,0,0,1,3.027,2.723,6.6,6.6,0,0,1,1.211,1.513c.3.605.908.908,1.211,1.513q.908,1.362,1.815,2.723a18.253,18.253,0,0,1,3.027,9.987c-.3,7.869-6.053,15.132-16.04,21.185-11.5,6.658-27.237,10.29-43.883,10.29a108.024,108.024,0,0,1-35.106-6.054c-5.145-1.512-14.224-7.565-15.435-8.776" transform="translate(0.649 0.927)" fill="#ececec"/>
          <path id="Path_409" data-name="Path 409" d="M52.694,171.967l9.381,5.448,54.778-31.777c34.8,13.316,66.883,9.685,89.883-3.632,12.712-7.263,19.067-16.947,19.37-26.632a26.046,26.046,0,0,0-3.935-13.619,25.174,25.174,0,0,0-2.42-3.631c-.605-.605-.908-1.211-1.513-1.816s-.908-1.211-1.513-1.816c-1.211-1.211-2.42-2.119-3.631-3.329a12.813,12.813,0,0,0-2.119-1.512,4.179,4.179,0,0,0-1.211-.908,16.9,16.9,0,0,0-2.421-1.513,2.288,2.288,0,0,0-1.211-.605c-26.026-15.132-68.4-15.132-94.422,0a2.3,2.3,0,0,1-1.211.605c-.605.3-1.513.908-2.119,1.211-1.816,1.211-3.632,2.723-5.145,3.934-.605.605-1.211.908-1.816,1.513L99.905,95.4c-.3.3-3.026,3.631-3.026,3.631-.3.3-.605.605-.605.908a17.362,17.362,0,0,1-1.211,2.119c-.3.3-.3.605-.605.908,0,.3-.3.605-.3.908-.3.3-.3.907-.605,1.21a1.057,1.057,0,0,1-.3.605,7.7,7.7,0,0,0-.605,2.119c0,.605-.3,1.211-.3,1.816v.908c0,.908-.3,1.815-.3,2.42v.908c0,7.566,2.724,18.159,14.829,25.119Zm55.988-40.553c-6.053-5.145-9.079-10.894-9.382-16.645v-3.026c0-.3.3-.908.3-1.211s.3-1.211.3-1.513a.937.937,0,0,1,.3-.605c0-.3.3-.605.3-.908s.3-.3.3-.605.3-.3.3-.605a5.11,5.11,0,0,0,.908-1.815c0-.3.3-.3.3-.605a23.1,23.1,0,0,0,2.421-3.329c.3-.3.605-.908,1.21-1.211a5.314,5.314,0,0,1,1.513-1.211l4.54-3.631c.605-.3,1.21-.908,1.815-1.211a.937.937,0,0,1,.605-.3l.605-.3c11.5-6.658,26.936-10.289,43.883-10.289,16.645,0,32.383,3.631,43.883,10.289a2.3,2.3,0,0,0,1.211.605c.605.3,1.513.908,2.119,1.211.3,0,.908.605.908.605.6.3,1.21.908,1.815,1.211a27.286,27.286,0,0,1,3.027,2.723,6.6,6.6,0,0,1,1.211,1.513c.3.605.908.908,1.211,1.513q.908,1.362,1.815,2.723a18.253,18.253,0,0,1,3.027,9.987c-.3,7.869-6.053,15.132-16.04,21.185-11.5,6.658-27.237,10.29-43.883,10.29a108.024,108.024,0,0,1-35.106-6.054C118.972,138.677,109.892,132.624,108.682,131.414Z" transform="translate(0.649 0.927)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_410" data-name="Path 410" d="M85.989,158.9c-2.421-3.027-7.263-6.961-11.5-4.842L3.067,193.1c.6-.3.907-.3,1.512-.605a8.5,8.5,0,0,1,6.053,1.816,18.435,18.435,0,0,1,4.843,4.842,21.875,21.875,0,0,1,4.539,13.014c0,3.328-1.211,5.447-3.026,6.355l71.12-39.041c1.816-.908,3.026-3.328,3.026-6.355V170.7a18.392,18.392,0,0,0-5.145-11.8" transform="translate(0.038 1.889)" fill="#ececec"/>
          <path id="Path_411" data-name="Path 411" d="M85.989,158.9c-2.421-3.027-7.263-6.961-11.5-4.842L3.067,193.1c.6-.3.907-.3,1.512-.605a8.5,8.5,0,0,1,6.053,1.816,18.435,18.435,0,0,1,4.843,4.842,21.875,21.875,0,0,1,4.539,13.014c0,3.328-1.211,5.447-3.026,6.355l71.12-39.041c1.816-.908,3.026-3.328,3.026-6.355V170.7A18.392,18.392,0,0,0,85.989,158.9Z" transform="translate(0.038 1.889)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_412" data-name="Path 412" d="M10.666,193.531C5.219,190.2.376,192.623.376,198.978c-.3,6.355,4.238,14.527,9.685,17.856s10.29.908,10.29-5.447c.3-6.356-4.238-14.527-9.685-17.856" transform="translate(0.004 2.364)" fill="#ececec"/>
          <path id="Path_413" data-name="Path 413" d="M10.666,193.531C5.219,190.2.376,192.623.376,198.978c-.3,6.355,4.238,14.527,9.685,17.856s10.29.908,10.29-5.447C20.654,205.031,16.114,196.861,10.666,193.531Z" transform="translate(0.004 2.364)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_414" data-name="Path 414" d="M173.631,120.525c9.707-5.606,9.707-14.73,0-20.336s-25.511-5.606-35.223,0-9.711,14.728,0,20.336,25.508,5.606,35.223,0m-3.312-1.915a31.636,31.636,0,0,1-28.586,0c-7.882-4.554-7.882-11.955,0-16.506a31.636,31.636,0,0,1,28.586,0c7.875,4.55,7.875,11.955-.006,16.506Z" transform="translate(1.614 1.181)" fill="#ececec"/>
          <path id="Path_415" data-name="Path 415" d="M173.631,120.525c9.707-5.606,9.707-14.73,0-20.336s-25.511-5.606-35.223,0-9.711,14.728,0,20.336S163.916,126.132,173.631,120.525Zm-3.312-1.915a31.636,31.636,0,0,1-28.586,0c-7.882-4.554-7.882-11.955,0-16.506a31.636,31.636,0,0,1,28.586,0c7.875,4.55,7.875,11.955-.006,16.506Z" transform="translate(1.614 1.181)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_416" data-name="Path 416" d="M166.233,104.313a22.88,22.88,0,0,0-20.677,0c-5.7,3.294-5.7,8.645,0,11.939a22.906,22.906,0,0,0,20.677,0c5.7-3.292,5.7-8.645,0-11.939" transform="translate(1.739 1.254)" fill="#ececec"/>
          <path id="Path_417" data-name="Path 417" d="M166.233,104.313a22.88,22.88,0,0,0-20.677,0c-5.7,3.294-5.7,8.645,0,11.939a22.906,22.906,0,0,0,20.677,0C171.935,112.96,171.935,107.607,166.233,104.313Z" transform="translate(1.739 1.254)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_418" data-name="Path 418" d="M180.291,22.351a30.678,30.678,0,0,0-1.864-11.135l.828-4.056L172.2,2.991C166.565-.844,158.483-.62,149.5,4.566c-17.013,9.823-30.8,33.7-30.8,53.342s30.8,50.721,30.8,50.721l8.213,4.875,9.315-46.016c6.919-17.647,13.26-36.224,13.26-45.137" transform="translate(1.461 0.004)" fill="#ececec"/>
          <path id="Path_422" data-name="Path 422" d="M176.041,34.24c0,11.339-7.958,25.127-17.777,30.8s-17.781,1.073-17.781-10.268,7.963-25.122,17.781-30.793S176.041,22.9,176.041,34.24" transform="translate(-3.793 7.265)" fill="#212121"/>
          <path id="Path_419" data-name="Path 419" d="M180.291,22.351a30.678,30.678,0,0,0-1.864-11.135l.828-4.056L172.2,2.991C166.565-.844,158.483-.62,149.5,4.566c-17.013,9.823-30.8,33.7-30.8,53.342s30.8,50.721,30.8,50.721l8.213,4.875,9.315-46.016C173.95,49.842,180.291,31.264,180.291,22.351Z" transform="translate(1.461 0.004)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_420" data-name="Path 420" d="M188.4,27.169c0,19.64-30.8,86.273-30.8,86.273s-30.8-31.073-30.8-50.721S140.6,19.2,157.607,9.38s30.8-1.848,30.8,17.789" transform="translate(1.561 0.064)" fill="#ececec"/>
          <path id="Path_421" data-name="Path 421" d="M188.4,27.169c0,19.64-30.8,86.273-30.8,86.273s-30.8-31.073-30.8-50.721S140.6,19.2,157.607,9.38,188.4,7.532,188.4,27.169Z" transform="translate(1.561 0.064)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_423" data-name="Path 423" d="M176.041,34.24c0,11.339-7.958,25.127-17.777,30.8s-17.781,1.073-17.781-10.268,7.963-25.122,17.781-30.793S176.041,22.9,176.041,34.24Z" transform="translate(1.729 0.265)" fill="none" stroke="#141414" stroke-width="0.724"/>
        </g>
      </g>
    </g>
  </g>
</svg>
