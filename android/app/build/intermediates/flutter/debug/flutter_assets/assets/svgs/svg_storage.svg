<svg xmlns="http://www.w3.org/2000/svg" width="510" height="563" viewBox="0 0 510 563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_96" data-name="Rectangle 96" width="510" height="563" rx="50" transform="translate(0 -0.025)" fill="#212121" stroke="#5e5e5e" stroke-width="1"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_75" data-name="Rectangle 75" width="146.787" height="197.616" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_110271" data-name="Group 110271" transform="translate(0 0.025)">
    <g id="Rectangle_47" data-name="Rectangle 47" transform="translate(0 -0.025)" fill="#212121" stroke="#3a3a3a" stroke-width="1">
      <rect width="510" height="563" rx="50" stroke="none"/>
      <rect x="0.5" y="0.5" width="509" height="562" rx="49.5" fill="none"/>
    </g>
    <g id="Group_110270" data-name="Group 110270">
      <g id="Mask_Group_1" data-name="Mask Group 1" clip-path="url(#clip-path)">
        <ellipse id="Ellipse_11" data-name="Ellipse 11" cx="181" cy="103" rx="181" ry="103" transform="translate(169 384.975)" fill="#4a4a4a" opacity="0.52"/>
      </g>
      <g id="Group_110191" data-name="Group 110191" transform="translate(2634.034 -8818.525)">
        <path id="Path_347" data-name="Path 347" d="M1.231,52.925l98.34,52.928,97.9-53.344L101.8.413Z" transform="translate(-2380.265 9224.582)" fill="#212121"/>
        <g id="Group_110085" data-name="Group 110085" transform="translate(-2354.306 9112)">
          <line id="Line_18" data-name="Line 18" x2="4.126" y2="2.019" transform="translate(74.844 155.777)" fill="#ececec"/>
          <line id="Line_19" data-name="Line 19" x2="4.126" y2="2.019" transform="translate(74.844 155.777)" fill="none" stroke="#141414" stroke-width="0.724"/>
          <path id="Path_275" data-name="Path 275" d="M.724,80.827l71.939,40.629L144.6,80.827,72.66,40.2Z" transform="translate(0.732 38.888)" fill="#ececec"/>
          <g id="Group_110075" data-name="Group 110075">
            <g id="Group_110074" data-name="Group 110074" clip-path="url(#clip-path-2)">
              <path id="Path_276" data-name="Path 276" d="M72.662,121.458.724,80.826,72.66,40.2,144.6,80.828Z" transform="translate(0.732 38.888)" fill="none" stroke="#141414" stroke-width="0.724"/>
            </g>
          </g>
          <path id="Path_277" data-name="Path 277" d="M.724,90.1,72.66,130.733V101.478L.724,60.848Z" transform="translate(0.732 58.866)" fill="#ececec"/>
          <g id="Group_110077" data-name="Group 110077">
            <g id="Group_110076" data-name="Group 110076" clip-path="url(#clip-path-2)">
              <path id="Path_278" data-name="Path 278" d="M72.662,130.733.723,90.1V60.849L72.662,101.48Z" transform="translate(0.732 58.866)" fill="none" stroke="#141414" stroke-width="0.724"/>
            </g>
          </g>
          <path id="Path_279" data-name="Path 279" d="M36.463,101.466v29.256L108.4,90.088V60.836Z" transform="translate(36.898 58.854)" fill="#ececec"/>
          <g id="Group_110079" data-name="Group 110079">
            <g id="Group_110078" data-name="Group 110078" clip-path="url(#clip-path-2)">
              <path id="Path_280" data-name="Path 280" d="M108.4,90.089,36.462,130.721V101.467L108.4,60.836Z" transform="translate(36.897 58.854)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_281" data-name="Path 281" d="M19.255,78.4c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(15.28 72.167)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_282" data-name="Path 282" d="M19.255,78.4c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(15.28 72.167)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_283" data-name="Path 283" d="M21.679,82.6c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(17.733 76.232)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_284" data-name="Path 284" d="M21.679,82.6c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(17.733 76.232)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_285" data-name="Path 285" d="M24.193,81.249c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(20.277 74.927)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_286" data-name="Path 286" d="M24.193,81.249c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(20.277 74.927)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_287" data-name="Path 287" d="M26.617,85.448c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(22.73 78.991)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_288" data-name="Path 288" d="M26.617,85.448c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(22.73 78.991)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_289" data-name="Path 289" d="M29.131,84.1c0,1.295-.93,1.822-2.076,1.173a4.472,4.472,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(25.274 77.686)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_290" data-name="Path 290" d="M29.131,84.1c0,1.295-.93,1.822-2.076,1.173a4.472,4.472,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(25.274 77.686)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_291" data-name="Path 291" d="M31.555,88.3c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(27.727 81.75)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_292" data-name="Path 292" d="M31.555,88.3c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(27.727 81.75)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_293" data-name="Path 293" d="M34.069,86.953c0,1.295-.93,1.822-2.076,1.173a4.472,4.472,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(30.271 80.445)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_294" data-name="Path 294" d="M34.069,86.953c0,1.295-.93,1.822-2.076,1.173a4.472,4.472,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(30.271 80.445)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_295" data-name="Path 295" d="M14.855,78.174c0,3.394-2.436,4.769-5.44,3.073a11.722,11.722,0,0,1-5.44-9.219c0-3.394,2.436-4.769,5.44-3.071a11.715,11.715,0,0,1,5.44,9.217" transform="translate(4.021 66.006)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_296" data-name="Path 296" d="M14.855,78.174c0,3.394-2.436,4.769-5.44,3.073a11.722,11.722,0,0,1-5.44-9.219c0-3.394,2.436-4.769,5.44-3.071a11.715,11.715,0,0,1,5.44,9.217" transform="translate(4.021 66.006)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_297" data-name="Path 297" d="M69.706,114.721,3.611,77.391v7.2l66.094,37.33L135.8,84.594v-7.2Z" transform="translate(3.655 74.869)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_298" data-name="Path 298" d="M69.706,114.721,3.611,77.391v7.2l66.094,37.33L135.8,84.594v-7.2Z" transform="translate(3.655 74.869)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_299" data-name="Path 299" d="M72.662,101.57.724,60.938,72.66,20.307,144.6,60.94Z" transform="translate(0.732 19.646)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_300" data-name="Path 300" d="M72.662,101.57.724,60.938,72.66,20.307,144.6,60.94Z" transform="translate(0.732 19.646)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_301" data-name="Path 301" d="M72.662,110.844.723,70.213V40.96L72.662,81.591Z" transform="translate(0.732 39.625)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_302" data-name="Path 302" d="M72.662,110.844.723,70.213V40.96L72.662,81.591Z" transform="translate(0.732 39.625)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_303" data-name="Path 303" d="M108.4,70.2,36.462,110.831V81.578L108.4,40.947Z" transform="translate(36.897 39.613)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_304" data-name="Path 304" d="M108.4,70.2,36.462,110.831V81.578L108.4,40.947Z" transform="translate(36.897 39.613)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_305" data-name="Path 305" d="M19.255,58.506c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(15.28 52.927)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_306" data-name="Path 306" d="M19.255,58.506c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(15.28 52.927)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_307" data-name="Path 307" d="M21.679,62.708c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(17.733 56.99)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_308" data-name="Path 308" d="M21.679,62.708c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(17.733 56.99)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_309" data-name="Path 309" d="M24.193,61.358c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(20.277 55.686)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_310" data-name="Path 310" d="M24.193,61.358c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(20.277 55.686)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_311" data-name="Path 311" d="M26.617,65.56c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(22.73 59.749)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_312" data-name="Path 312" d="M26.617,65.56c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(22.73 59.749)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_313" data-name="Path 313" d="M29.131,64.211c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(25.274 58.444)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_314" data-name="Path 314" d="M29.131,64.211c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(25.274 58.444)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_315" data-name="Path 315" d="M31.555,68.411c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(27.727 62.509)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_316" data-name="Path 316" d="M31.555,68.411c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(27.727 62.509)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_317" data-name="Path 317" d="M34.069,67.064c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(30.271 61.204)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_318" data-name="Path 318" d="M34.069,67.064c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(30.271 61.204)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_319" data-name="Path 319" d="M14.855,58.285c0,3.394-2.436,4.769-5.44,3.073a11.718,11.718,0,0,1-5.44-9.217c0-3.4,2.436-4.771,5.44-3.073a11.715,11.715,0,0,1,5.44,9.217" transform="translate(4.021 46.765)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_320" data-name="Path 320" d="M14.855,58.285c0,3.394-2.436,4.769-5.44,3.073a11.718,11.718,0,0,1-5.44-9.217c0-3.4,2.436-4.771,5.44-3.073a11.715,11.715,0,0,1,5.44,9.217" transform="translate(4.021 46.765)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_321" data-name="Path 321" d="M69.706,94.832,3.611,57.5v7.2l66.094,37.332L135.8,64.7V57.5Z" transform="translate(3.655 55.628)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_322" data-name="Path 322" d="M69.706,94.832,3.611,57.5v7.2l66.094,37.332L135.8,64.7V57.5Z" transform="translate(3.655 55.628)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_323" data-name="Path 323" d="M72.662,81.68.724,41.049,72.66.418,144.6,41.051Z" transform="translate(0.732 0.404)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_324" data-name="Path 324" d="M72.662,81.68.724,41.049,72.66.418,144.6,41.051Z" transform="translate(0.732 0.404)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_325" data-name="Path 325" d="M72.662,90.955.723,50.324V21.07L72.662,61.7Z" transform="translate(0.732 20.384)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_326" data-name="Path 326" d="M72.662,90.955.723,50.324V21.07L72.662,61.7Z" transform="translate(0.732 20.384)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_327" data-name="Path 327" d="M108.4,50.311,36.462,90.94V61.689L108.4,21.058Z" transform="translate(36.897 20.371)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_328" data-name="Path 328" d="M108.4,50.311,36.462,90.94V61.689L108.4,21.058Z" transform="translate(36.897 20.371)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_329" data-name="Path 329" d="M19.255,38.618c0,1.295-.93,1.822-2.076,1.173A4.472,4.472,0,0,1,15.1,36.27c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(15.28 33.685)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_330" data-name="Path 330" d="M19.255,38.618c0,1.295-.93,1.822-2.076,1.173A4.472,4.472,0,0,1,15.1,36.27c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(15.28 33.685)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_331" data-name="Path 331" d="M21.679,42.818c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(17.733 37.748)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_332" data-name="Path 332" d="M21.679,42.818c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(17.733 37.748)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_333" data-name="Path 333" d="M24.193,41.47c0,1.295-.93,1.822-2.076,1.173a4.472,4.472,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(20.277 36.444)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_334" data-name="Path 334" d="M24.193,41.47c0,1.295-.93,1.822-2.076,1.173a4.472,4.472,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(20.277 36.444)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_335" data-name="Path 335" d="M26.617,45.671c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(22.73 40.508)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_336" data-name="Path 336" d="M26.617,45.671c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(22.73 40.508)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_337" data-name="Path 337" d="M29.131,44.321c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(25.274 39.204)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_338" data-name="Path 338" d="M29.131,44.321c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(25.274 39.204)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_339" data-name="Path 339" d="M31.555,48.523c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(27.727 43.267)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_340" data-name="Path 340" d="M31.555,48.523c0,1.295-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.3.93-1.822,2.078-1.173a4.472,4.472,0,0,1,2.076,3.52" transform="translate(27.727 43.267)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_341" data-name="Path 341" d="M34.069,47.173c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(30.271 41.963)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_342" data-name="Path 342" d="M34.069,47.173c0,1.3-.93,1.822-2.076,1.173a4.469,4.469,0,0,1-2.078-3.52c0-1.295.93-1.82,2.078-1.173a4.475,4.475,0,0,1,2.076,3.52" transform="translate(30.271 41.963)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_343" data-name="Path 343" d="M14.855,38.4c0,3.394-2.436,4.769-5.44,3.073a11.718,11.718,0,0,1-5.44-9.217c0-3.394,2.436-4.771,5.44-3.073a11.715,11.715,0,0,1,5.44,9.217" transform="translate(4.021 27.524)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_344" data-name="Path 344" d="M14.855,38.4c0,3.394-2.436,4.769-5.44,3.073a11.718,11.718,0,0,1-5.44-9.217c0-3.394,2.436-4.771,5.44-3.073a11.715,11.715,0,0,1,5.44,9.217" transform="translate(4.021 27.524)" fill="none" stroke="#141414" stroke-width="0.724"/>
              <path id="Path_345" data-name="Path 345" d="M69.706,74.942,3.611,37.613v7.2l66.094,37.33L135.8,44.815v-7.2Z" transform="translate(3.655 36.387)" fill="#ececec" fill-rule="evenodd"/>
              <path id="Path_346" data-name="Path 346" d="M69.706,74.942,3.611,37.613v7.2l66.094,37.33L135.8,44.815v-7.2Z" transform="translate(3.655 36.387)" fill="none" stroke="#141414" stroke-width="0.724"/>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
