<svg id="Map" xmlns="http://www.w3.org/2000/svg" width="510" height="563" viewBox="0 0 510 563">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_3" data-name="Rectangle 3" width="510" height="563" rx="50" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_3" data-name="Group 3">
    <g id="Group_110273" data-name="Group 110273">
      <g id="Rectangle_47" data-name="Rectangle 47">
        <rect id="Rectangle_1" data-name="Rectangle 1" width="510" height="563" rx="50" fill="#212121"/>
        <rect id="Rectangle_2" data-name="Rectangle 2" width="509" height="562" rx="49.5" transform="translate(0.5 0.5)" fill="none" stroke="#3a3a3a" stroke-width="1"/>
      </g>
      <g id="Group_1" data-name="Group 1" clip-path="url(#clip-path)">
        <g id="Mask_Group_1" data-name="Mask Group 1">
          <ellipse id="Ellipse_11" data-name="Ellipse 11" cx="181" cy="103" rx="181" ry="103" transform="translate(169 385)" fill="#4a4a4a" opacity="0.52"/>
        </g>
      </g>
    </g>
    <path id="Path_1" data-name="Path 1" d="M462.88,447.53l-30.03,32.96-38.71,5.32-42.6,41.58-32.65,2.98-73.86-42.92-.01-.01v.03l-6.79-3.96v.02l-27.77-16.15v-.02l-.02-8.98h.01l32.89-3.01,42.47-41.45,38.36-5.27,30.26-33.21,20.68,12.02.02-.02,11.16,6.51,2.71,1.58,73.85,42.91Z" fill="#212121"/>
    <g id="Group_2" data-name="Group 2">
      <path id="Path_2" data-name="Path 2" d="M309.27,505l-87.73-50.99-.03-8.98,87.73,50.98Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_3" data-name="Path 3" d="M342.15,493.01,233.73,430l42.47-41.45,108.42,63.01Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_4" data-name="Path 4" d="M384.62,451.56,276.2,388.55l38.36-5.27,108.42,63.01Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_5" data-name="Path 5" d="M343.27,297.7c.05,14.36-10.05,31.87-22.56,39.08-6.26,3.63-11.94,3.94-16.09,1.58a1.4,1.4,0,0,0-.26-.16l-4.84-2.79c-4.1-2.37-6.63-7.36-6.63-14.52a35.99,35.99,0,0,1,.95-7.68c2.68-12.41,11.31-25.4,21.62-31.4,6.31-3.63,11.99-3.94,16.09-1.58l4.26,2.52a2.671,2.671,0,0,1,.84.47c4.05,2.31,6.57,7.31,6.63,14.46Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_6" data-name="Path 6" d="M354.52,283.71c-.79-8.33-3.43-15.98-10.62-20.09a4,4,0,0,0-.63-.37l-13.99-8.2c-5.68-3.26-13.15-3.42-21.46.21a50.619,50.619,0,0,0-4.52,2.31c-8.26,4.79-15.73,12.36-21.83,21.14-8.84,12.78-14.62,28.24-14.57,41.97,0,9.68,2.84,16.88,7.63,21.19l.32.32c2.79,2.79,24.14,24.72,28.29,45.39,0,.11.05.21.16.21l14.62,8.57v-.05c.11.11.26.05.42,0a1.414,1.414,0,0,0,.53-.84c4-25.3,25.19-71.84,27.93-77.84l.32-.68c5.31-10.94,8.2-22.72,7.42-33.24ZM318.34,342.5c-15.94,9.2-28.98,1.79-29.03-16.51s12.89-40.71,28.82-49.91,28.93-1.79,28.98,16.57c.05,18.25-12.83,40.65-28.77,49.86Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_7" data-name="Path 7" d="M422.98,446.29,314.56,383.28l30.26-33.21,108.42,63.01L422.98,446.3Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_8" data-name="Path 8" d="M443.05,414.65,421.7,438.06l-44.91-26.14,21.3-23.4,44.97,26.13Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_9" data-name="Path 9" d="M379.4,370.17l-30.29,33.19-38.34,5.26-42.49,41.5-32.87,3-13.88-8.1,32.92-3,42.44-41.44,38.34-5.26,30.29-33.24,13.88,8.1Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_10" data-name="Path 10" d="M349.12,403.37l-13.88-8.07,30.26-33.21,13.88,8.06Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_11" data-name="Path 11" d="M310.77,408.64l-13.88-8.07,38.36-5.27,13.88,8.07-38.36,5.27Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_12" data-name="Path 12" d="M364.69,471l-9.68,9.46L269.5,430.77l9.68-9.45,85.51,49.69Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_13" data-name="Path 13" d="M262.64,446.79c-9.78-.63-19.62.68-27.61,6.1l-6.42-3.73v8.99L200.84,442v-8.99l32.87-3Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_14" data-name="Path 14" d="M262.63,446.79,233.73,430l-32.9,3.01,34.24,19.9c7.94-5.45,17.81-6.72,27.57-6.1h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_15" data-name="Path 15" d="M309.25,496.02l-87.73-50.98,32.9-3.01,87.73,50.99-32.9,3.01Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_16" data-name="Path 16" d="M268.27,450.11l-32.87,3v8.99L221.52,454v-8.99l32.92-3Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_17" data-name="Path 17" d="M235.39,453.1l-13.88-8.06,32.9-3.01,13.88,8.07-32.9,3.01Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_18" data-name="Path 18" d="M228.62,449.16,200.83,433l.03,8.99,27.77,16.14v-8.97Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_19" data-name="Path 19" d="M268.29,450.09l-13.88-8.07,42.47-41.45,13.88,8.07Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_20" data-name="Path 20" d="M235.42,462.08l-13.88-8.07-.03-8.98,13.88,8.06.03,8.99Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_21" data-name="Path 21" d="M384.62,451.56l38.36-5.27,30.26-33.22.03,9.08-30.04,32.96-38.7,5.32-42.6,41.58-32.65,2.98-.03-8.99,32.9-3.01,42.47-41.45Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_22" data-name="Path 22" d="M289.48,350.74l-14.64-8.54-.31-.33,14.64,8.55Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_23" data-name="Path 23" d="M317.78,396.08a.978.978,0,0,0,.16.26l-14.62-8.57c-.11,0-.16-.11-.16-.21-4.15-20.67-25.51-42.6-28.29-45.39l14.62,8.57c2.79,2.79,24.19,24.72,28.29,45.33ZM343.92,263.6c-5.68-3.31-9.25,3.16-17.5,6.79a31.7,31.7,0,0,0-4.58,2.31c-8.26,4.73-15.73,12.31-21.83,21.09-8.84,12.83-14.62,28.24-14.57,41.97,0,9.68-1.06,10.36,3.72,14.67l-14.62-8.57c-4.79-4.31-7.63-11.52-7.63-21.19-.05-13.73,5.73-29.19,14.57-41.97,6.1-8.78,13.57-16.36,21.83-21.14,1.47-.84,3-1.63,4.52-2.31,8.31-3.63,15.78-3.47,21.46-.21l14.62,8.57h0Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_24" data-name="Path 24" d="M354.52,283.71c-.79-8.33-3.43-15.98-10.62-20.09a4,4,0,0,0-.63-.37c-5.57-2.95-12.78-3-20.77.53-1.53.68-3.05,1.47-4.58,2.31-20.2,11.68-36.45,39.92-36.39,63.11.05,9.68,2.89,16.93,7.63,21.19l.32.32c2.84,2.79,24.19,24.72,28.35,45.39a.245.245,0,0,0,.11.21c.11.11.26.05.42,0a1.414,1.414,0,0,0,.53-.84c4-25.3,25.19-71.84,27.93-77.84l.32-.68c5.31-10.94,8.2-22.72,7.42-33.24ZM318.34,342.5c-15.94,9.2-28.98,1.79-29.03-16.51s12.89-40.71,28.82-49.91,28.93-1.79,28.98,16.57c.05,18.25-12.83,40.65-28.77,49.86Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_25" data-name="Path 25" d="M336.66,283.22c-4.1-2.42-5.67,4.58-11.99,8.21-10.31,5.94-17.02,13.64-19.7,26.11-.58,2.58,12.96,4.46,12.96,6.93,0,7.15-17.41,11.5-13.31,13.87l-5.1-2.95c-4.1-2.37-6.63-7.36-6.63-14.52a35.99,35.99,0,0,1,.95-7.68c2.68-12.41,11.31-25.4,21.62-31.4,6.31-3.63,11.99-3.94,16.09-1.58l5.1,3Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_26" data-name="Path 26" d="M320.58,284.78c12.49-7.21,22.65-1.44,22.69,12.93.04,14.35-10.05,31.85-22.54,39.07-12.51,7.22-22.68,1.42-22.72-12.93C297.96,309.48,308.08,292.01,320.58,284.78Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
      <path id="Path_27" data-name="Path 27" d="M329.82,317.78l-9.21-.7-9.17,11.33,9.13-31.39,9.25,20.76Z" fill="#ececec" stroke="#000" stroke-miterlimit="10" stroke-width="0.72"/>
    </g>
  </g>
</svg>
