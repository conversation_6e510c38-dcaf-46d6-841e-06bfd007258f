<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":open_file_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_player_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/device_info_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":fluttertoast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/fluttertoast/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/package_info_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":volume_controller" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/volume_controller/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_archive" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_archive/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":wakelock_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/wakelock_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":video_thumbnail" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_thumbnail/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/shared_preferences_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":kiosk_mode" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/kiosk_mode/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_fgbg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_fgbg/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":alarm" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_downloader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/connectivity_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/path_provider_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":sqflite" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/sqflite/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":objectbox_flutter_libs" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/objectbox_flutter_libs/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":network_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets"><file name="audio/female/voice_emergency_stop_switch_restored_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_emergency_stop_switch_restored_hi.wav"/><file name="audio/female/voice_emergency_stop_switch_restored_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_emergency_stop_switch_restored_en.wav"/><file name="audio/female/voice_set_recycle_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_set_recycle_point_en.wav"/><file name="audio/female/voice_add_delivery_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_add_delivery_point_hi.wav"/><file name="audio/female/voice_add_delivery_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_add_delivery_point_en.wav"/><file name="audio/female/voice_set_recycle_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_set_recycle_point_hi.wav"/><file name="audio/female/voice_set_production_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_set_production_point_hi.wav"/><file name="audio/female/voice_set_production_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_set_production_point_en.wav"/><file name="audio/female/voice_rebuild_map_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_rebuild_map_hi.wav"/><file name="audio/female/voice_rebuild_map_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_rebuild_map_en.wav"/><file name="audio/female/voice_welcome_to_odigo_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_welcome_to_odigo_en.wav"/><file name="audio/female/voice_welcome_to_odigo_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_welcome_to_odigo_hi.wav"/><file name="audio/female/voice_please_select_map_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_please_select_map_en.wav"/><file name="audio/female/voice_please_select_map_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_please_select_map_hi.wav"/><file name="audio/female/voice_elevator_point_hi.mp3" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_elevator_point_hi.mp3"/><file name="audio/female/voice_elevator_point_en.mp3" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_elevator_point_en.mp3"/><file name="audio/female/voice_please_select_my_lanugage_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_please_select_my_lanugage_en.wav"/><file name="audio/female/voice_please_select_my_lanugage_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_please_select_my_lanugage_hi.wav"/><file name="audio/female/voice_heading_towards_production_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_heading_towards_production_point_hi.wav"/><file name="audio/female/voice_heading_towards_production_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_heading_towards_production_point_en.wav"/><file name="audio/female/voice_navigate_to_production_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_navigate_to_production_point_en.wav"/><file name="audio/female/voice_successfully_connected_to_network_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_successfully_connected_to_network_en.wav"/><file name="audio/female/voice_navigate_to_production_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_navigate_to_production_point_hi.wav"/><file name="audio/female/voice_successfully_connected_to_network_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_successfully_connected_to_network_hi.wav"/><file name="audio/female/voice_volume_increased_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_volume_increased_en.wav"/><file name="audio/female/voice_volume_increased_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_volume_increased_hi.wav"/><file name="audio/female/voice_cruise_mode_has_been_started_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_cruise_mode_has_been_started_en.wav"/><file name="audio/female/voice_battery_level_of_dasher_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_battery_level_of_dasher_hi.wav"/><file name="audio/female/voice_battery_level_of_dasher_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_battery_level_of_dasher_en.wav"/><file name="audio/female/voice_cruise_mode_has_been_started_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_cruise_mode_has_been_started_hi.wav"/><file name="audio/female/voice_exit_deployement_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_exit_deployement_en.wav"/><file name="audio/female/voice_exit_deployement_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_exit_deployement_hi.wav"/><file name="audio/female/voice_obstacle_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_obstacle_hi.wav"/><file name="audio/female/voice_speed_increased_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_speed_increased_en.wav"/><file name="audio/female/voice_speed_increased_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_speed_increased_hi.wav"/><file name="audio/female/voice_obstacle_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_obstacle_en.wav"/><file name="audio/female/voice_how_may_i_help_you_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_how_may_i_help_you_en.wav"/><file name="audio/female/voice_charge_reached_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_charge_reached_hi.wav"/><file name="audio/female/voice_charge_reached_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_charge_reached_en.wav"/><file name="audio/female/voice_how_may_i_help_you_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_how_may_i_help_you_hi.wav"/><file name="audio/female/voice_establishing_network_connection_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_establishing_network_connection_hi.wav"/><file name="audio/female/voice_establishing_network_connection_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_establishing_network_connection_en.wav"/><file name="audio/female/voice_would_you_like_to_play_again_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_would_you_like_to_play_again_en.wav"/><file name="audio/female/voice_would_you_like_to_play_again_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_would_you_like_to_play_again_hi.wav"/><file name="audio/female/voice_store_reached_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_store_reached_en.wav"/><file name="audio/female/voice_store_reached_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_store_reached_hi.wav"/><file name="audio/female/voice_speed_decreased_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_speed_decreased_en.wav"/><file name="audio/female/voice_speed_decreased_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_speed_decreased_hi.wav"/><file name="audio/female/voice_already_at_the_store_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_already_at_the_store_en.wav"/><file name="audio/female/voice_already_at_the_store_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_already_at_the_store_hi.wav"/><file name="audio/female/voice_volume_decreased_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_volume_decreased_en.wav"/><file name="audio/female/voice_volume_decreased_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_volume_decreased_hi.wav"/><file name="audio/female/voice_follow_me_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_follow_me_hi.wav"/><file name="audio/female/voice_these_are_different_modes_of_dasher_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_these_are_different_modes_of_dasher_hi.wav"/><file name="audio/female/voice_follow_me_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_follow_me_en.wav"/><file name="audio/female/voice_these_are_different_modes_of_dasher_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_these_are_different_modes_of_dasher_en.wav"/><file name="audio/female/voice_navigate_to_charging_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_navigate_to_charging_point_en.wav"/><file name="audio/female/voice_location_not_found_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_location_not_found_en.wav"/><file name="audio/female/voice_location_not_found_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_location_not_found_hi.wav"/><file name="audio/female/voice_navigate_to_charging_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_navigate_to_charging_point_hi.wav"/><file name="audio/female/voice_let_me_connect_with_your_wifi_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_let_me_connect_with_your_wifi_hi.wav"/><file name="audio/female/voice_let_me_connect_with_your_wifi_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_let_me_connect_with_your_wifi_en.wav"/><file name="audio/female/voice_open_map_screen_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_open_map_screen_hi.wav"/><file name="audio/female/voice_open_map_screen_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_open_map_screen_en.wav"/><file name="audio/female/voice_nearest_elevator_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_nearest_elevator_hi.wav"/><file name="audio/female/voice_nearest_elevator_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_nearest_elevator_en.wav"/><file name="audio/female/voice_take_the_stairs_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_take_the_stairs_en.wav"/><file name="audio/female/voice_take_the_stairs_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_take_the_stairs_hi.wav"/><file name="audio/female/voice_make_route_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_make_route_en.wav"/><file name="audio/female/voice_make_route_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_make_route_hi.wav"/><file name="audio/female/voice_i_have_arrived_at_production_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_i_have_arrived_at_production_point_hi.wav"/><file name="audio/female/voice_emergency_stop_switch_pressed_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_emergency_stop_switch_pressed_en.wav"/><file name="audio/female/voice_waypaths_not_found_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_waypaths_not_found_hi.wav"/><file name="audio/female/voice_emergency_stop_switch_pressed_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_emergency_stop_switch_pressed_hi.wav"/><file name="audio/female/voice_i_have_arrived_at_production_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_i_have_arrived_at_production_point_en.wav"/><file name="audio/female/voice_waypaths_not_found_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_waypaths_not_found_en.wav"/><file name="audio/female/voice_do_you_want_to_continue_with_your_existing_configuration_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_do_you_want_to_continue_with_your_existing_configuration_hi.wav"/><file name="audio/female/voice_do_you_want_to_continue_with_your_existing_configuration_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_do_you_want_to_continue_with_your_existing_configuration_en.wav"/><file name="audio/female/voice_set_charging_point_en.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_set_charging_point_en.wav"/><file name="audio/female/voice_set_charging_point_hi.wav" path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/assets/audio/female/voice_set_charging_point_hi.wav"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/app/intermediates/shader_assets/debug/out"/></dataSet></merger>