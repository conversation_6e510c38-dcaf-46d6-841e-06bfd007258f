-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:43:5-120:19
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:12:5-14:19
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:12:5-14:19
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:19:5-27:19
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:19:5-27:19
MERGED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-20:19
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.material:material:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/2e2682419faf6859a3d9bc8eb52e0cf1/transformed/material-1.4.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/2e2682419faf6859a3d9bc8eb52e0cf1/transformed/material-1.4.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/2637d919d8eed3aaa805faafbb2537f4/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/2637d919d8eed3aaa805faafbb2537f4/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/eaf56c4e4c07df08588f7b7b3fb16f95/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/eaf56c4e4c07df08588f7b7b3fb16f95/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:16:5-30:19
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:16:5-30:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml
provider#vn.hunghd.flutterdownloader.DownloadedFileProvider
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:77:9-88:20
	android:authorities
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml
provider#androidx.core.content.FileProvider
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:90:9-98:20
	android:authorities
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:1:1-132:12
MERGED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:1:1-132:12
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:2:1-16:12
MERGED from [indicatorseekbar-2.1.2.aar] /Users/<USER>/.gradle/caches/transforms-3/98434d62cc67ccb1275a23c67d1e43c7/transformed/jetified-indicatorseekbar-2.1.2/AndroidManifest.xml:2:1-9:12
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-10:12
MERGED from [:objectbox_flutter_libs] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/objectbox_flutter_libs/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/sqflite/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-16:12
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-29:12
MERGED from [:flutter_fgbg] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_fgbg/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:kiosk_mode] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/kiosk_mode/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:video_thumbnail] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_thumbnail/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:wakelock_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/wakelock_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_archive] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_archive/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:volume_controller] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/volume_controller/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/package_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:fluttertoast] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/fluttertoast/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:device_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/fc5b2cae012f2559c9684c84722b799d/transformed/jetified-room-ktx-2.5.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/14dc22056818dae615dd14c862bc7d6c/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:17:1-33:12
MERGED from [com.google.android.material:material:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/2e2682419faf6859a3d9bc8eb52e0cf1/transformed/material-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/2637d919d8eed3aaa805faafbb2537f4/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/33bc7102a431c0fe1b5c94951fc81946/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/2dfa8744decd574890c33564c01e8b30/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1862051904f8cba788c1896a6d8cc837/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/a5227fc991a15b041c136c5b3639b963/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/transforms-3/f3db52f643e33b18f27468977011198f/transformed/jetified-activity-1.6.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/c041b9423c42ce63675ab7d6ce185eae/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/802bda1dfd5448bf778fc1f0dae104f9/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b85e2914be9e3dd1bfb7bccec3b9e3fb/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/8ef06b9f31cab72d0759d11e8f9917d1/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/ddf26bf1fd7a270a654384e91f8377a6/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/e8d22d3649457966f81be21d40c503d3/transformed/jetified-lifecycle-service-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/08b6b099b3939411501631c3aaaa50cc/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/01a2800dd9333c9d320c1e4ceb50c784/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/953539cc20991de1b64147a21539ddae/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/44ceb12a60d520e1fb326fc3bde13886/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/b703bd1babcce4f27b236942b70255cc/transformed/jetified-exoplayer-hls-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/231f555a4a442fcdc05643bc6d9acde5/transformed/jetified-exoplayer-dash-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/783f69406c8e0d0049d894380ba952a0/transformed/jetified-exoplayer-smoothstreaming-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/949735e2a2b20568ac96df306f3887bb/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/65d24e9fe208503a4f3acfc4bae30e69/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/fcdae33591abe8b703e202397671a79e/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b70df9c1c9334ce8fb51bda0a30f88b/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2a114bd343226b1f94105cc4bda573af/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/df6dbe94894f6dae1cff25512e810d18/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a268af926209307f8850dbb0f1ef5cd0/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/6d9502824fd4895f5a258d5ca50f3b02/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/177e744fb2420d906d87496c85747524/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/73a520f2d8b626831339a0f861c8528e/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/4d114431d4ded44f4b6b473a50332f92/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f37ac14c653fc07a98e04c3ebc822f34/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/5049a19867c2f246be02b8c007a64912/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/824308286f09072fd85aabf49908ee7b/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/491750dc09e38d5cb180f8b0cbfdd2ee/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/e3cccb73cb00e79962016ce2c55ab98b/transformed/jetified-exoplayer-datasource-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/450469e5bf34ad4ce6b925a980d716e8/transformed/jetified-exoplayer-extractor-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/d83e41109996b27808026b89202088e1/transformed/jetified-exoplayer-decoder-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1444b435feac6f6400b30c3565c4a091/transformed/jetified-exoplayer-database-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1e9af294279f20f927c0ac60464de3eb/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/eaf56c4e4c07df08588f7b7b3fb16f95/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0aa9eee59e568053a8ad8485e4390062/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/1b97e916642b0ecf2593a6e7f1a43d7e/transformed/sqlite-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/566038c31beaba912b9cadb9b18aa589/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/471b413d3882bfabd940aa5ad25880e2/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/69af76e38016d8bdf9b90fd8e9c7975e/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0d612f98df287ba441f9b0393d01ea3e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/3209d650327df2caaf6b4ff34eb70c1a/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:2:1-32:12
MERGED from [androidx.multidex:multidex:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/e31b1ab9eb24dfcdc79056ef66ea21a7/transformed/multidex-2.0.0/AndroidManifest.xml:17:1-22:12
	package
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:3:5-37
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:2:5-51
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:9:5-67
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:9:5-67
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.SET_TIME
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:5:5-8:47
	android:protectionLevel
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:7:9-51
	tools:ignore
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:8:9-44
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:6:9-51
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:9:5-81
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:10:5-81
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:10:5-81
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:10:5-80
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:9:5-80
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:9:5-80
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:14:5-80
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:14:5-80
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:10:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:11:5-82
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:11:22-79
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:12:5-76
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:13:5-75
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:13:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:14:5-75
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:14:22-72
uses-permission#android.permission.NEARBY_WIFI_DEVICES
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:15:5-78
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:15:22-75
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:16:5-76
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:16:22-73
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:17:5-76
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-76
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-76
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:17:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:18:5-77
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-77
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-77
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:5-77
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:11:5-77
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:11:5-77
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:18:22-74
uses-permission#android.permission.USE_FINGERPRINT
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:19:5-74
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:19:22-71
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:20:5-77
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:14:5-77
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:14:5-77
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-77
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-77
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:13:5-77
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:13:5-77
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:20:22-74
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:21:5-79
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:17:5-79
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:17:5-79
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:21:22-76
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:22:5-82
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:22:22-80
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:24:5-26:37
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:16:5-74
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:16:5-74
	tools:ignore
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:26:9-34
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:25:9-58
uses-permission#android.permission.READ_LOGS
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:31:5-33:47
	tools:ignore
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:33:9-44
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:32:9-52
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:34:5-79
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/949735e2a2b20568ac96df306f3887bb/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/949735e2a2b20568ac96df306f3887bb/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1e9af294279f20f927c0ac60464de3eb/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1e9af294279f20f927c0ac60464de3eb/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:24:5-79
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:34:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:35:5-81
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:35:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:36:5-79
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:36:22-76
uses-permission#android.permission.SERIAL_PORT
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:38:5-39:47
	tools:ignore
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:39:9-44
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:38:22-67
uses-permission#android.permission.ACCESS_USB
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:40:5-41:47
	tools:ignore
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:41:9-44
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:40:22-66
queries
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:126:5-131:15
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-12:15
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-12:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:127:9-130:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:128:13-73
	android:name
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:128:21-70
data
ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:129:13-51
	android:mimeType
		ADDED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/main/AndroidManifest.xml:129:19-48
uses-sdk
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:5:5-7:41
MERGED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:5:5-7:41
MERGED from [indicatorseekbar-2.1.2.aar] /Users/<USER>/.gradle/caches/transforms-3/98434d62cc67ccb1275a23c67d1e43c7/transformed/jetified-indicatorseekbar-2.1.2/AndroidManifest.xml:5:5-7:41
MERGED from [indicatorseekbar-2.1.2.aar] /Users/<USER>/.gradle/caches/transforms-3/98434d62cc67ccb1275a23c67d1e43c7/transformed/jetified-indicatorseekbar-2.1.2/AndroidManifest.xml:5:5-7:41
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/network_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:objectbox_flutter_libs] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/objectbox_flutter_libs/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:objectbox_flutter_libs] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/objectbox_flutter_libs/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sqflite] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/sqflite/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sqflite] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/sqflite/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_fgbg] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_fgbg/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_fgbg] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_fgbg/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:kiosk_mode] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/kiosk_mode/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:kiosk_mode] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/kiosk_mode/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_thumbnail] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_thumbnail/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_thumbnail] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_thumbnail/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/wakelock_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/wakelock_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_archive] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_archive/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_archive] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_archive/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:volume_controller] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/volume_controller/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:volume_controller] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/volume_controller/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/package_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/package_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/fluttertoast/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/fluttertoast/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/fc5b2cae012f2559c9684c84722b799d/transformed/jetified-room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/fc5b2cae012f2559c9684c84722b799d/transformed/jetified-room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/14dc22056818dae615dd14c862bc7d6c/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window-java:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/14dc22056818dae615dd14c862bc7d6c/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/2e2682419faf6859a3d9bc8eb52e0cf1/transformed/material-1.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/2e2682419faf6859a3d9bc8eb52e0cf1/transformed/material-1.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/2637d919d8eed3aaa805faafbb2537f4/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/2637d919d8eed3aaa805faafbb2537f4/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/33bc7102a431c0fe1b5c94951fc81946/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/33bc7102a431c0fe1b5c94951fc81946/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/2dfa8744decd574890c33564c01e8b30/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/2dfa8744decd574890c33564c01e8b30/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1862051904f8cba788c1896a6d8cc837/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1862051904f8cba788c1896a6d8cc837/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/a5227fc991a15b041c136c5b3639b963/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/a5227fc991a15b041c136c5b3639b963/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/transforms-3/f3db52f643e33b18f27468977011198f/transformed/jetified-activity-1.6.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/transforms-3/f3db52f643e33b18f27468977011198f/transformed/jetified-activity-1.6.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/c041b9423c42ce63675ab7d6ce185eae/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/c041b9423c42ce63675ab7d6ce185eae/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/802bda1dfd5448bf778fc1f0dae104f9/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/802bda1dfd5448bf778fc1f0dae104f9/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b85e2914be9e3dd1bfb7bccec3b9e3fb/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b85e2914be9e3dd1bfb7bccec3b9e3fb/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/8ef06b9f31cab72d0759d11e8f9917d1/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/8ef06b9f31cab72d0759d11e8f9917d1/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/ddf26bf1fd7a270a654384e91f8377a6/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/ddf26bf1fd7a270a654384e91f8377a6/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/e8d22d3649457966f81be21d40c503d3/transformed/jetified-lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/e8d22d3649457966f81be21d40c503d3/transformed/jetified-lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/08b6b099b3939411501631c3aaaa50cc/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/08b6b099b3939411501631c3aaaa50cc/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/01a2800dd9333c9d320c1e4ceb50c784/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/01a2800dd9333c9d320c1e4ceb50c784/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/953539cc20991de1b64147a21539ddae/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/953539cc20991de1b64147a21539ddae/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/44ceb12a60d520e1fb326fc3bde13886/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/44ceb12a60d520e1fb326fc3bde13886/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/b703bd1babcce4f27b236942b70255cc/transformed/jetified-exoplayer-hls-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/b703bd1babcce4f27b236942b70255cc/transformed/jetified-exoplayer-hls-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/231f555a4a442fcdc05643bc6d9acde5/transformed/jetified-exoplayer-dash-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/231f555a4a442fcdc05643bc6d9acde5/transformed/jetified-exoplayer-dash-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/783f69406c8e0d0049d894380ba952a0/transformed/jetified-exoplayer-smoothstreaming-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/783f69406c8e0d0049d894380ba952a0/transformed/jetified-exoplayer-smoothstreaming-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/949735e2a2b20568ac96df306f3887bb/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/949735e2a2b20568ac96df306f3887bb/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/65d24e9fe208503a4f3acfc4bae30e69/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/65d24e9fe208503a4f3acfc4bae30e69/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/fcdae33591abe8b703e202397671a79e/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/fcdae33591abe8b703e202397671a79e/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b70df9c1c9334ce8fb51bda0a30f88b/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b70df9c1c9334ce8fb51bda0a30f88b/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2a114bd343226b1f94105cc4bda573af/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2a114bd343226b1f94105cc4bda573af/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/df6dbe94894f6dae1cff25512e810d18/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/df6dbe94894f6dae1cff25512e810d18/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a268af926209307f8850dbb0f1ef5cd0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a268af926209307f8850dbb0f1ef5cd0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/6d9502824fd4895f5a258d5ca50f3b02/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/6d9502824fd4895f5a258d5ca50f3b02/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/177e744fb2420d906d87496c85747524/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/177e744fb2420d906d87496c85747524/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/73a520f2d8b626831339a0f861c8528e/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/73a520f2d8b626831339a0f861c8528e/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/4d114431d4ded44f4b6b473a50332f92/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/4d114431d4ded44f4b6b473a50332f92/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f37ac14c653fc07a98e04c3ebc822f34/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f37ac14c653fc07a98e04c3ebc822f34/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/5049a19867c2f246be02b8c007a64912/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/5049a19867c2f246be02b8c007a64912/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/824308286f09072fd85aabf49908ee7b/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/824308286f09072fd85aabf49908ee7b/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/491750dc09e38d5cb180f8b0cbfdd2ee/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/491750dc09e38d5cb180f8b0cbfdd2ee/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/e3cccb73cb00e79962016ce2c55ab98b/transformed/jetified-exoplayer-datasource-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/e3cccb73cb00e79962016ce2c55ab98b/transformed/jetified-exoplayer-datasource-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/450469e5bf34ad4ce6b925a980d716e8/transformed/jetified-exoplayer-extractor-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/450469e5bf34ad4ce6b925a980d716e8/transformed/jetified-exoplayer-extractor-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/d83e41109996b27808026b89202088e1/transformed/jetified-exoplayer-decoder-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/d83e41109996b27808026b89202088e1/transformed/jetified-exoplayer-decoder-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1444b435feac6f6400b30c3565c4a091/transformed/jetified-exoplayer-database-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1444b435feac6f6400b30c3565c4a091/transformed/jetified-exoplayer-database-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1e9af294279f20f927c0ac60464de3eb/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/1e9af294279f20f927c0ac60464de3eb/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/eaf56c4e4c07df08588f7b7b3fb16f95/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/eaf56c4e4c07df08588f7b7b3fb16f95/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0aa9eee59e568053a8ad8485e4390062/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0aa9eee59e568053a8ad8485e4390062/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/1b97e916642b0ecf2593a6e7f1a43d7e/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/transforms-3/1b97e916642b0ecf2593a6e7f1a43d7e/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/566038c31beaba912b9cadb9b18aa589/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/566038c31beaba912b9cadb9b18aa589/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/471b413d3882bfabd940aa5ad25880e2/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/471b413d3882bfabd940aa5ad25880e2/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/69af76e38016d8bdf9b90fd8e9c7975e/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/69af76e38016d8bdf9b90fd8e9c7975e/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0d612f98df287ba441f9b0393d01ea3e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0d612f98df287ba441f9b0393d01ea3e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/3209d650327df2caaf6b4ff34eb70c1a/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/3209d650327df2caaf6b4ff34eb70c1a/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:5:5-44
MERGED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/e31b1ab9eb24dfcdc79056ef66ea21a7/transformed/multidex-2.0.0/AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/e31b1ab9eb24dfcdc79056ef66ea21a7/transformed/multidex-2.0.0/AndroidManifest.xml:20:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/android/app/src/debug/AndroidManifest.xml
service#com.reeman.ros.service.BackService
ADDED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:13:9-70
	android:name
		ADDED from [REEMAN-ROS-1.0-202304181455.aar] /Users/<USER>/.gradle/caches/transforms-3/d546988bcc00195ed889a2321337e303/transformed/jetified-REEMAN-ROS-1.0-202304181455/AndroidManifest.xml:13:18-67
intent#action:name:android.intent.action.VIEW
ADDED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-11:18
action#android.intent.action.VIEW
ADDED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-65
	android:name
		ADDED from [:flutter_downloader] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/flutter_downloader/intermediates/merged_manifest/debug/AndroidManifest.xml:10:21-62
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:5-81
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:7:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-68
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:23:5-68
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:8:22-65
uses-permission#android.permission.VIBRATE
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-66
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:9:22-63
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:10:5-81
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:10:22-78
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:12:5-85
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:12:22-82
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:15:5-92
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:15:22-89
receiver#com.gdelataillade.alarm.alarm.AlarmReceiver
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:20:9-80
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:20:19-77
service#com.gdelataillade.alarm.alarm.AlarmService
ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:22:9-26:19
	android:exported
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-37
	android:foregroundServiceType
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:25:13-58
	android:name
		ADDED from [:alarm] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/alarm/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-70
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:53
	android:resource
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:18:17-50
	android:name
		ADDED from [:open_file_android] /Users/<USER>/Documents/FlutterWorkspace/Odigo-Robot-App-for-Ad-Display/build/open_file_android/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-67
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/de84c8ede94ceae54480b8f6cde7bcd8/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/transforms-3/319c94e464626b01fceb6617b4e196d8/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/431cb7f3063307518bb63fe3792d29cb/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:29:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/20296b0a289218f49c113ce47b516c40/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/transforms-3/a553ce6bc840aee31fbf2755f324a69d/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.kody.odigo_display.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.kody.odigo_display.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/34c07c5c676946953474911572e17754/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/75b33a55ece238cf5c84e25da5347169/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a30d5aef55a78a4846d55473eeacc3dc/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
service#io.objectbox.android.AdminKeepAliveService
ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:20:13-70
receiver#io.objectbox.android.AdminNotificationReceiver
ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:23:9-29:20
	android:exported
		ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:24:13-74
intent-filter#action:name:io.objectbox.action.KEEP_ALIVE
ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:26:13-28:29
action#io.objectbox.action.KEEP_ALIVE
ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:27:17-73
	android:name
		ADDED from [io.objectbox:objectbox-android-objectbrowser:4.0.3] /Users/<USER>/.gradle/caches/transforms-3/643c80bef70843f763e42d11aeba0343/transformed/jetified-objectbox-android-objectbrowser-4.0.3/AndroidManifest.xml:27:25-70
