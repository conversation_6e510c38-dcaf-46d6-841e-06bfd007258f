package com.kody.odigo_display

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.kody.advertisement.utils.Constants

class AlarmReceiver : BroadcastReceiver() {
    private val constants = Constants.getInstance()
    var TAG = "AlarmReceiver"
    override fun onReceive(context: Context?, intent: Intent?) {
        val message: String = intent?.getStringExtra("MESSAGE") ?: ""
        Log.d(TAG, "onReceive: $message")
        constants.alarmEventSink?.success(message)
    }
}