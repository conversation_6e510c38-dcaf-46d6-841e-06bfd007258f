import android.util.Base64
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

class BaseEncryptionException(message: String) : Exception(message)

object EncryptionUtils {

    private var key: SecretKey? = null
    private var iv: IvParameterSpec? = null

    fun initialize(base64Key: String, base64Salt: String): Boolean {
        return try {
            // Decode Base64 inputs
            val decodedKey = Base64.decode(base64Key, Base64.DEFAULT)
            val decodedSalt = Base64.decode(base64Salt, Base64.DEFAULT)

            // Initialize key and IV
            key = SecretKeySpec(decodedKey, "AES")
            iv = IvParameterSpec(decodedSalt)

            // Initialize cipher for decryption
            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher?.init(Cipher.DECRYPT_MODE, key, iv)
            true
        } catch (e: Exception) {
            Log.d("Err", "Error initializing encryption : ${e.message}")
            false
        }
    }


    fun decryptFile(inputFilePath: String, aesKey: String, aesSalt: String): Boolean {
        val inputFile = File(inputFilePath)
        val parentDir = inputFile.parent
        val fileNameWithoutExtension = inputFile.nameWithoutExtension
        val fileExtension = inputFile.extension
        val outputFileName = "${fileNameWithoutExtension}_decrypted.$fileExtension"
        val outputFilePath = "$parentDir/$outputFileName"
        val outputFile = File(outputFilePath)
        try {
            if (!inputFile.exists() || inputFile.length() == 0L) {
                throw BaseEncryptionException("Input file does not exist or is empty")
            }
            // Delete if exists
            if (outputFile.exists()) {
                outputFile.delete()
            }
            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher?.init(Cipher.DECRYPT_MODE, key, iv)
            println("Cipher: $cipher, Key: $key, IV: $iv")
            println("Decoded Key: ${Base64.decode(aesKey, Base64.DEFAULT).joinToString(",")}")
            println("Decoded IV: ${Base64.decode(aesSalt, Base64.DEFAULT).joinToString(",")}")
            FileInputStream(inputFile).use { fis ->
                CipherInputStream(fis, cipher).use { cis ->
                    FileOutputStream(outputFile).use { fos ->
                        val buffer = ByteArray(8192)
                        var bytesRead: Int
                        while (cis.read(buffer).also { bytesRead = it } != -1) {
                            fos.write(buffer, 0, bytesRead)
                        }
                    }
                }
            }
            return true
        } catch (e: Exception) {
            File(outputFilePath).delete()
            Log.d("Err", "Decryption error: ${e.message}")
            return false
        }
    }

}
