package com.kody.advertisement.base

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import com.kody.advertisement.navigation.NavigationController
import com.kody.advertisement.lights.LightController
import com.reeman.ros.controller.RobotActionController

abstract class BaseApplication : AppCompatActivity() {

    /// MAC address of LAN
    var hostIpAddress: String? = null

    var batteryPercentage: Float? = null

    var batteryLevel: Int? = null
    val mHandler = Handler(Looper.getMainLooper())

    /// RobotActionController main navigation controller


    companion object {
        var hostIpAddress: String? = null
        var batteryPercentage: Int = 0
        var batteryLevel: Int? = null
        var controller: RobotActionController? = null
        var lightController: LightController? = LightController.instance
        var navigationController: NavigationController? = null
        val mHandler = Handler(Looper.getMainLooper())
    }

    protected abstract fun layoutRes(): Int?

    /// General DTO class

    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(layoutRes()!!)
        super.onCreate(savedInstanceState)

    }


//    override fun onCreate() {
//        super.onCreate()
//        ///When implementing Lights
//        /*try {
//            LightController.getInstance().start()
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }*/
//
//        /*///Some Receiver for Event
//        val robotReceiver = RobotReceiver()
//        val robotIntentFilter = RobotReceiver.RobotIntentFilter()
//        registerReceiver(robotReceiver, robotIntentFilter)*/
//
//    }

    /*open fun onCustomPositionObtained(position: DoubleArray) {}*/

    /*@Subscribe(threadMode = ThreadMode.MAIN)
    open fun onPositionObtainedEvent(event: Event.OnPositionEvent) {
        Log.e("ROBOT MAP", ".......onPositionObtainedEvent.....${event.position}")
        navigationController?.currentPosition = event.position
        if (DispatchManager.isStarted) {
            val timeMillis = System.currentTimeMillis()
            val iterator: MutableIterator<Map.Entry<String, MsgCreator.RobotMsg>> =
                DispatchManager.robotInfo.entries.iterator() as MutableIterator<Map.Entry<String, MsgCreator.RobotMsg>>
            while (iterator.hasNext()) {
                val (_, value) = iterator.next()
                if (timeMillis - value.ti > 5 * 1000) {
                    iterator.remove()
                }
            }
            val hostname = Event.getOnHostnameEvent().hostname
            DispatchManager.publishMessage(
                MsgCreator.createRobotMsg(
                    hostname,
                    event.position,
                    Event.getOnBaseValEvent().lineSpeed
                )
            )
        }
        onCustomPositionObtained(event.position)
    }*/

}