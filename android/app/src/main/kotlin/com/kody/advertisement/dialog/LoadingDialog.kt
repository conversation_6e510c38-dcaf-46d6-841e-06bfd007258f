package com.kody.advertisement.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.widget.TextView
import com.kody.odigo_display.R


class LoadingDialog(context: Context):Dialog(context) {

    lateinit var tvTitle :TextView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setContentView(R.layout.loading_dialog)
        initViews()
    }

    private fun initViews() {
        tvTitle = findViewById(R.id.loading_dialog_tvTitle)
    }

    override fun dismiss() {
        tvTitle.text = ""
        super.dismiss()
    }

    init {
        setCancelable(false)
    }
}