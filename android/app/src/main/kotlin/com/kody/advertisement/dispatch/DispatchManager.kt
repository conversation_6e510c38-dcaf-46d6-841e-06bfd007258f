package com.kody.advertisement.dispatch


import com.kody.advertisement.dispatch.msg.MsgCreator

object DispatchManager {
    var isStarted = false
        private set

    @Throws(Exception::class)
    fun start() {
        DispatchServer.instance?.start()
        isStarted = true
    }

    fun stop() {
        DispatchServer.instance?.stop()
        isStarted = false
    }

    fun publishMessage(robotMsg: MsgCreator.RobotMsg) {
        val instance: DispatchServer? = DispatchServer.instance
        if (!instance?.isReady!!) return
        instance.publishMessage(robotMsg)
    }

    val robotInfo: Map<String, MsgCreator.RobotMsg>
        get() = DispatchServer.instance!!.robotInfo as Map<String, MsgCreator.RobotMsg>
}