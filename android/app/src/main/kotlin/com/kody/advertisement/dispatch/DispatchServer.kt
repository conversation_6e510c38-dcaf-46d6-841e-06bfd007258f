package com.kody.advertisement.dispatch

import android.os.Handler
import android.os.Looper
import com.kody.advertisement.dispatch.communication.EspHelper
import com.kody.advertisement.dispatch.msg.MsgCreator
import com.kody.advertisement.dispatch.processor.DispatchProcessor
import com.kody.advertisement.dispatch.task.DispatchTask
import com.reeman.ros.event.Event
import java.util.concurrent.ConcurrentLinkedDeque
import java.util.concurrent.locks.LockSupport

class DispatchServer private constructor() {
    private val espHelper: EspHelper?
    private val processorThread: Thread
    private val processor: DispatchProcessor
    private val task: DispatchTask
    private val handler = Handler(Looper.getMainLooper())

    init {
        val messages = ConcurrentLinkedDeque<String>()
        processor = DispatchProcessor(Event.getOnHostnameEvent().hostname)
        task = DispatchTask(processor, messages)
        processorThread = Thread(task, "server-process-thread")
        espHelper = EspHelper(processorThread, messages)
    }

    @Throws(Exception::class)
    fun start() {
        espHelper?.start()
        processorThread.start()
        espHelper!!::exitTransmission?.let { handler?.postDelayed(it, 500) }
        handler.postDelayed({
            espHelper.enterTransmission()
            espHelper.ready=true
        }, 2000)
    }

    fun stop() {
        espHelper?.exitTransmission()
        espHelper?.stop()
        task.setFinished(true)
        LockSupport.unpark(processorThread)
        sINSTANCE = null
    }

    val isReady: Boolean
        get() = espHelper!!.ready

    fun publishMessage(msg: MsgCreator.Msg?) {
        if (espHelper == null) return
        espHelper.send(msg)
    }

    val robotInfo: Map<String, Any>
        get() = processor.getRobotMsgMap()

    companion object {
        private var sINSTANCE: DispatchServer? = null
        val instance: DispatchServer?
            get() {
                if (sINSTANCE == null) {
                    sINSTANCE = DispatchServer()
                }
                return sINSTANCE
            }
    }
}