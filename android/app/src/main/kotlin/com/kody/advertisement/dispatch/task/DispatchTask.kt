package com.kody.advertisement.dispatch.task

import com.kody.advertisement.dispatch.processor.DispatchProcessor
import java.util.concurrent.ConcurrentLinkedDeque
import java.util.concurrent.locks.LockSupport

class DispatchTask(processor: DispatchProcessor, messages: ConcurrentLinkedDeque<String>) :
    Runnable {
    private val messages: ConcurrentLinkedDeque<String>
    private val processor: DispatchProcessor

    @Volatile
    private var finished = false
    fun setFinished(finished: <PERSON><PERSON>an) {
        this.finished = finished
        messages.clear()
    }

    init {
        this.processor = processor
        this.messages = messages
    }

    override fun run() {
        while (!finished) {
            try {
                if (messages.isEmpty()) LockSupport.park()
                if (finished) break
                val msg = messages.poll() ?: continue
                processor.processMsg(msg)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}