package com.kody.advertisement.lights

import android.util.Log
import com.aill.androidserialport.ByteUtil

import com.aill.androidserialport.SerialPort

import java.io.File
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

class SerialPortParser(file: File?, baudrate: Int, listener: OnDataResultListener?) {
    private var serialPortParser: SerialPort?
    private var bytes: ByteArray?
    private val thread: Thread
    private val inputStream: InputStream?
    private val outputStream: OutputStream?

    @Volatile
    private var stopped = false
    private val listener: OnDataResultListener?

    init {
        Log.d("File Path", file?.exists().toString())
        serialPortParser = SerialPort(file, baudrate, 0)
        inputStream = serialPortParser!!.inputStream
        outputStream = serialPortParser!!.outputStream
        this.listener = listener
        bytes = ByteArray(1024)
        thread = Thread(ReadRunnable(), "serial-port-read-thread")
    }

    fun start() {
        thread.start()
    }

    fun stop() {
        stopped = true
    }

    @Throws(IOException::class)
    fun sendCommand(bytes: ByteArray?) {
        outputStream?.write(bytes)
    }

    @Throws(IOException::class)
    fun sendStr(str: String?) {
        outputStream?.write(ByteUtil.hexStringToBytes(str))
    }

    interface OnDataResultListener {
        fun onDataResult(bytes: ByteArray?, len: Int)
    }

    private inner class ReadRunnable : Runnable {
        override fun run() {
            while (!stopped) {
                try {
                    if (inputStream!!.available() <= 0) {
                        continue
                    }
                    var len: Int
                    if (inputStream.read(bytes).also { len = it } > 0) {
                        if (listener != null) {
                            listener.onDataResult(bytes, len)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            serialPortParser!!.close()
            serialPortParser = null
            bytes = null
        }
    }
}
