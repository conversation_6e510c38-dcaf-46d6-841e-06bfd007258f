package com.kody.advertisement.map_platformview

import android.content.Context
import android.view.View
import com.kody.advertisement.map_platformview.MapWebView
import com.kody.advertisement.map.MapManager
import io.flutter.plugin.platform.PlatformView

class NativeView(context: Context?, viewId: Int, creationParams: Map<String?, Any?>?, var mapManager: MapManager) :
    PlatformView {

    private var view : MapWebView? = MapWebView(context!!, mapManager)

    override fun getView(): View? {
        return view!!
    }

    override fun dispose() {
        view = null
    }

}
