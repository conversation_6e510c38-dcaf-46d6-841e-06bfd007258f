package com.kody.advertisement.obstacle

import com.kody.advertisement.base.BaseSetting


class ObstacleSetting(var enableObstaclePrompt: <PERSON>olean, var targetObstaclePrompts: List<Int>, var obstaclePrompts: List<String>, var obstaclePromptAudioList: List<String>) :
    BaseSetting() {
    override fun toString(): String {
        return "ObstacleSetting{" +
                "enableObstaclePrompt=" + enableObstaclePrompt +
                ", targetObstaclePrompts=" + targetObstaclePrompts +
                ", obstaclePrompts=" + obstaclePrompts +
                ", obstaclePromptAudioList=" + obstaclePromptAudioList +
                '}'
    }

    companion object {
        val default: ObstacleSetting
            get() = ObstacleSetting(true, ArrayList(), ArrayList(), ArrayList())
    }
}
