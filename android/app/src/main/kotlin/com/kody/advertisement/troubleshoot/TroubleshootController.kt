package com.kody.advertisement.troubleshoot

import android.content.Context
import android.net.wifi.WifiManager
import android.text.format.Formatter
import android.util.Log
import com.kody.advertisement.base.BaseApplication.Companion.controller
import com.kody.advertisement.utils.Constants
import com.reeman.ros.controller.RobotActionController
import com.reeman.ros.event.Event
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MethodChannel

@Suppress("DEPRECATION")
class TroubleshootController(applicationContext: Context) {

    var wifiManager: WifiManager? = null
    var connectedWifiResult: MethodChannel.Result? = null
    var navigationModeResult: MethodChannel.Result? = null
    var hostNameResult: MethodChannel.Result? = null
    var coreDataResult: MethodChannel.Result? = null
    var sensorResult: MethodChannel.Result? = null
    var currentPositionResult: MethodChannel.Result? = null
    private var applicationContext: Context? = null
    var wifiName = ""
    var wifiIpAddress = ""

    init {
        wifiManager = applicationContext.applicationContext.getSystemService(FlutterActivity.WIFI_SERVICE) as WifiManager
        this.applicationContext = applicationContext
    }

    private val constants = Constants.getInstance()

    private fun getConnectedWifi() {
        Log.e("Winfo","Get connected wifi")
        val wInfo = wifiManager!!.connectionInfo
        Log.e("Winfo","Get connected wifi $wInfo")
        wifiIpAddress = Formatter.formatIpAddress(wInfo.ipAddress)
        wifiName = constants.removeDoubleQuotes(wInfo.ssid)
        controller?.getHostIp()
    }

    fun initializeWifiMethodChannel(binaryMessenger: BinaryMessenger, robotActionController: RobotActionController?) {
        Log.d("Start Method Channel", "This is for testing")

        return MethodChannel(binaryMessenger, constants.dasherTroubleshootChannel).setMethodCallHandler { call, result ->
            when (call.method) {

                constants.getConnectedWifi -> {
                    try {
                        connectedWifiResult = result
                        getConnectedWifi()
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                constants.getHostName -> {
                    try {
                        hostNameResult = result
                        controller?.getHostName()
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                constants.getNavigationMode -> {
                    try {
                        navigationModeResult = result
                        controller?.modelRequest()
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                constants.getCurrentPosition -> {
                    try {
                        currentPositionResult = result
                        controller?.getCurrentPosition()
//                        controller?.sendCommand("check_sensors")
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                constants.getCoreData -> {
                    try {
                        coreDataResult = result
                        var event = Event.getCoreData()
                        val map = hashMapOf<String, Any?>()
                        map["battery_level"] = event.battery
                        map["is_charging"] = event.charger == 2
                        map["is_emergency_down"] = event.button == 0
                        result.success(map)
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                constants.getSensorStatus -> {
                    try {
                        sensorResult = result
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}