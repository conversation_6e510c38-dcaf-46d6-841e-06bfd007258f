package com.kody.advertisement.utils

import java.util.*

object ByteUtils {
    fun checksum(data: String): String {
        var checkData = 0
        var i = 0
        while (i < data.length) {
            val start = data.substring(i, i + 2).toInt(16)
            checkData = start xor checkData
            i = i + 2
        }
        return String.format("%02X", checkData).uppercase(Locale.getDefault())
    }

    fun byteArr2HexString(inBytArr: ByteArray, len: Int): String {
        val strBuilder = StringBuilder()
        for (i in 0 until len) {
            strBuilder.append(String.format("%02X", inBytArr[i]).uppercase(Locale.getDefault()))
        }
        return strBuilder.toString()
    }
}