package com.kody.advertisement.voice

import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import java.io.IOException

class MediaPlayerHelper(private var applicationContext: Context) {
    private var mediaPlayer: MediaPlayer? = null
    fun play(name: String, volume: Int, loop: Boolean = true, listener: VoiceHelper.OnCompleteListener? = null) {
        val assetFileDescriptor: AssetFileDescriptor
        try {
            assetFileDescriptor = applicationContext.assets.openFd(name)
            playAssetsFile(assetFileDescriptor, volume, loop, listener)
        } catch (e: IOException) {
            e.printStackTrace()
            Handler(Looper.getMainLooper()).postDelayed({
                if (listener == null) return@postDelayed
                listener.onComplete()
            }, 1500)
        }
    }

    val isPlaying: Boolean
        get() = try {
            mediaPlayer != null && mediaPlayer!!.isPlaying
        } catch (e: Exception) {
            false
        }

    fun stop() {
        try {
            mediaPlayer?.stop()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun pause() {
        try {
            mediaPlayer?.pause()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun resume() {
        try {
            mediaPlayer?.start()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    @JvmOverloads
    fun playAssetsFile(assetFileDescriptor: AssetFileDescriptor, volume: Int, loop: Boolean = true, listener: VoiceHelper.OnCompleteListener? = null) {
        try {
            if (mediaPlayer == null) {
                mediaPlayer = MediaPlayer()
                mediaPlayer?.setAudioStreamType(AudioManager.STREAM_MUSIC)
            }
            mediaPlayer?.setVolume(volume / 10.0f, volume / 10.0f)
            mediaPlayer?.reset()
            mediaPlayer?.setDataSource(assetFileDescriptor.fileDescriptor, assetFileDescriptor.startOffset, assetFileDescriptor.length)
            mediaPlayer?.isLooping = loop
            mediaPlayer?.setOnCompletionListener { mp: MediaPlayer? ->
                if (listener == null) return@setOnCompletionListener
                listener.onComplete()
            }
            mediaPlayer?.prepare()
            mediaPlayer?.start()
        } catch (e: IOException) {
            e.printStackTrace()
            Handler(Looper.getMainLooper()).postDelayed({
                if (listener == null) return@postDelayed
                listener.onComplete()
            }, 1500)
        }
    }

    fun playDefaultMusic(assetFileDescriptor: AssetFileDescriptor, volume: Int, loop: Boolean = true, listener: VoiceHelper.OnCompleteListener? = null) {
        try {
            playAssetsFile(assetFileDescriptor, volume, loop, listener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @JvmOverloads
    fun playFile(path: String?, volume: Int, loop: Boolean = true, listener: OnCompleteListener? = null) {
        try {
            if (mediaPlayer == null) {
                mediaPlayer = MediaPlayer()
                mediaPlayer?.setAudioStreamType(AudioManager.STREAM_MUSIC)
            }
            mediaPlayer?.setVolume(volume / 10.0f, volume / 10.0f)
            mediaPlayer?.reset()
            mediaPlayer?.setDataSource(path)
            mediaPlayer?.isLooping = loop
            mediaPlayer?.setOnCompletionListener { listener?.onComplete() }
            mediaPlayer?.prepare()
            mediaPlayer?.start()
        } catch (e: IOException) {
            e.printStackTrace()
            Handler(Looper.getMainLooper()).postDelayed({
                if (listener == null) return@postDelayed
                listener.onComplete()
            }, 1500)
        }
    }

    fun decreaseVolume(volume: Int) {
        var decreaseVolume = volume - 6
        if (volume < 2) {
            decreaseVolume = 3
        }
        mediaPlayer?.setVolume(decreaseVolume / 10.0f, decreaseVolume / 10.0f)
    }

    fun resumeVolume(volume: Int) {
        mediaPlayer?.setVolume(volume / 10.0f, volume / 10.0f)
    }

    fun interface OnCompleteListener {
        fun onComplete()
    }
}
