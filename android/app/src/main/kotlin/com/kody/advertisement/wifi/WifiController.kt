@file:Suppress("DEPRECATION")

package com.kody.advertisement.wifi

import android.annotation.SuppressLint
import android.content.Context
import android.content.IntentFilter
import android.net.wifi.WifiManager
import android.text.format.Formatter
import android.util.Log
import com.kody.advertisement.base.BaseApplication.Companion.controller
import com.kody.advertisement.base.BaseApplication.Companion.mHandler
import com.kody.advertisement.utils.Constants
import com.reeman.ros.controller.RobotActionController
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import java.util.Arrays


@Suppress("DEPRECATION")
class WifiController(applicationContext: Context, var intentFilter: IntentFilter) {

    var wifiManager: WifiManager? = null
    var wifiReceiver: WifiReceiver? = null
    var wifiDevicesEvents: EventChannel.EventSink? = null
    var wifiResult: EventChannel.EventSink? = null
    var connectedWifiResult: MethodChannel.Result? = null
    private var applicationContext: Context? = null
    var wifiConnectionFlutterMethodChannel: MethodChannel? = null
    var wifiName = ""
    var wifiPassword = ""
    var wifiIpAddress = ""
    var currentPhase = 0
    val PHASE_INIT = 0
    val PHASE_ANDROID_CONNECTING = 1
    val PHASE_ROS_CONNECTING = 2
    var androidConnectSuccessCount = 0

    /// Init
    init {
        wifiManager =
            applicationContext.applicationContext.getSystemService(FlutterActivity.WIFI_SERVICE) as WifiManager
        wifiReceiver = WifiReceiver(controller, this)
        this.applicationContext = applicationContext
    }

    private val constants = Constants.getInstance()

    ///Will return if wifi is enabled or not
    fun getIsWifiEnabled(): Boolean {
        return wifiManager!!.isWifiEnabled
    }

    /// Enable wifi
    private fun enableWifi() {
        wifiManager!!.isWifiEnabled = true
    }

    /// Disable Wifi
    private fun disableWifi() {
        wifiManager!!.isWifiEnabled = false
    }

    /// Get Connected Wifi DTO
    private fun getConnectedWifi() {
        /// Get Connected Wifi DTO
        val wInfo = wifiManager!!.connectionInfo
        wifiIpAddress = Formatter.formatIpAddress(wInfo.ipAddress)
        wifiName = wInfo.ssid
        Log.e("Wifi Controller", "wifiIpAddress $wifiIpAddress")
        controller?.getHostIp()
    }

    /// Connect To Network
    @SuppressLint("MissingPermission")
    fun connectToNetwork() {
        return try {
//            /// TODO What is Connect Phase
//            currentPhase = PHASE_ANDROID_CONNECTING
//            /// Connect Wifi
//            WIFIUtils.connect(applicationContext!!.applicationContext, wifiName, wifiPassword)
//            androidConnectSuccessCount = 0
        } catch (ex: Exception) {
            println(Arrays.toString(ex.stackTrace))
        }
    }


    /// Connect wifi to ROS sdk
    fun connectWifiToRosSdk(robotActionController: RobotActionController?) {
        try {
            wifiIpAddress = WIFIUtils.getIpAddress(applicationContext!!.applicationContext);
            Log.d("Connected Ip-OutsideROS", wifiIpAddress)
            Log.d("Connected Ip-OutsideROS", currentPhase.toString())
            Log.d("Connected Ip-OutsideROS", androidConnectSuccessCount.toString())
            if (currentPhase != PHASE_ANDROID_CONNECTING) return
            if (wifiIpAddress != "0.0.0.0") {
                androidConnectSuccessCount++
            }
            if (androidConnectSuccessCount >= 2) {
                if (wifiName.isNotBlank() && wifiPassword.isNotBlank()) {
                    if (wifiIpAddress != "0.0.0.0") {
                        Log.d("Connected Ip-Inside ROS", wifiIpAddress)
                        Log.d("Connected Ip-Inside ROS", wifiName)
                        Log.d("Connected Ip-Inside ROS", wifiPassword)
                        robotActionController?.connectROSWifi(wifiName, wifiPassword)
                        mHandler.postDelayed({
                            Log.d("Host IP", "Host IP Called")
                            robotActionController?.getHostIp()
                        }, 45000)
                    }
                }
            }
        } catch (e: Exception) {
            wifiResult?.error("0", "Connection Failed", "")
        }
    }

    /// Method Channels
    fun initializeWifiFlutterChannel(binaryMessenger: BinaryMessenger) {
        wifiConnectionFlutterMethodChannel =
            MethodChannel(binaryMessenger, constants.syncMapOnConnectionChannel)
    }

    fun initializeWifiMethodChannel(
        binaryMessenger: BinaryMessenger,
        robotActionController: RobotActionController?
    ) {
        Log.d("Start Method Channel", "This is for testing")
        return MethodChannel(
            binaryMessenger,
            constants.dasherWifiChannel
        ).setMethodCallHandler { call, result ->
            when (call.method) {
                /// Check Wifi Status
                constants.isWifiEnable -> {
                    try {
                        val successResult = getIsWifiEnabled()
                        result.success(successResult)
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                /// Enable Wifi
                constants.enableWifi -> {
                    try {
                        enableWifi()
                        result.success(true)
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                /// Disable Wifi
                constants.disableWifi -> {
                    try {
                        disableWifi()
                        result.success(true)
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                /// Get Connected Wifi
                constants.getConnectedWifi -> {
                    try {
                        Log.d("Controller", "" + controller)
                        connectedWifiResult = result
                        getConnectedWifi()
                    } catch (e: Exception) {
                        result.error("0", e.message, e.cause?.message)
                    }
                }

                /// Connect To Network
                constants.connectToNetwork -> {
                    val argumentsMap = call.arguments as HashMap<*, *>
                    wifiName = argumentsMap["networkSSID"] as String
                    wifiPassword = argumentsMap["password"] as String
                    result.success(true)
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    /// Wifi Scanning
    fun listenForWifiDevices(dartExecutor: DartExecutor) {
        return EventChannel(dartExecutor, constants.scanForDevices).setStreamHandler(object :
            EventChannel.StreamHandler {
            @SuppressLint("UnspecifiedRegisterReceiverFlag")
            override fun onListen(args: Any?, events: EventChannel.EventSink) {
                wifiDevicesEvents = events
                if (getIsWifiEnabled()) {
                    applicationContext!!.registerReceiver(wifiReceiver, intentFilter)
                    val map = hashMapOf<String, Any?>()
                    map["device_name"] = null
                    map["is_scanning"] = true
                    events.success(map)
                    wifiManager!!.startScan()
                } else {
                    events.error("0", "Connection Error", "Please enable Wifi")
                }
            }

            override fun onCancel(args: Any?) {
                wifiDevicesEvents = null
            }
        })
    }

    /// Connect Network Event
    fun connectToNetworkEvent(dartExecutor: DartExecutor, robotActionController: RobotActionController?) {
        return EventChannel(dartExecutor, constants.connectToNetwork).setStreamHandler(object :
            EventChannel.StreamHandler {
            @SuppressLint("UnspecifiedRegisterReceiverFlag")
            override fun onListen(args: Any?, events: EventChannel.EventSink) {
                wifiResult = events
                robotActionController?.connectROSWifi(wifiName, wifiPassword)
            }

            override fun onCancel(args: Any?) {
                wifiResult = null
            }
        })
    }

}