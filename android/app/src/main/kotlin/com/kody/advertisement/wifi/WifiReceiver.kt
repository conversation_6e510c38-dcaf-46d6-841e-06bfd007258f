package com.kody.advertisement.wifi

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.NetworkInfo
import android.net.wifi.ScanResult
import android.net.wifi.WifiManager
import android.util.Log
import com.kody.advertisement.base.BaseApplication
import com.kody.advertisement.utils.Constants
import com.reeman.ros.controller.RobotActionController


class WifiReceiver(var robotActionController: RobotActionController?, var wifiController: WifiController) : BroadcastReceiver() {


    @SuppressLint("MissingPermission")
    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent!!.action
        Log.d("Intent Action", "" + action)
        if (WifiManager.SCAN_RESULTS_AVAILABLE_ACTION == action || WifiManager.ACTION_PICK_WIFI_NETWORK == action) {
            val wifiList: List<ScanResult> = wifiController.wifiManager!!.scanResults
            for (scanResult in wifiList) {
                val capabilities: String = scanResult.capabilities
                val isSecured: Boolean = if (capabilities.uppercase().contains("WEP")) {
                    true
                } else (capabilities.uppercase().contains("WPA") || capabilities.uppercase().contains("WPA2") || capabilities.uppercase().contains("WPA3"))
                val wifiStrength: Int = WifiManager.calculateSignalLevel(scanResult.level, 3)
                val map = hashMapOf<String, Any?>()
                map["device_name"] = scanResult.SSID
                map["mac_address"] = scanResult.BSSID
                map["is_scanning"] = true
                map["wifi_strength"] = wifiStrength
                map["is_secured"] = isSecured

                /// Passing data to Flutter App
                if (wifiController.wifiDevicesEvents != null) {
                    wifiController.wifiDevicesEvents?.success(map)
                }
            }

            /// Thread for 15 seconds
            Constants.getInstance().mHandler.postDelayed(Runnable {
                val map = hashMapOf<String, Any?>()
                map["device_name"] = null
                map["isScanning"] = false
                if (wifiController.wifiDevicesEvents != null) {
                    wifiController.wifiDevicesEvents?.success(map)
                }
                /*eventSink.endOfStream()*/
            }, 15000)

        } else if (WifiManager.NETWORK_STATE_CHANGED_ACTION == action) {
            val info = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
            if (info!!.state == NetworkInfo.State.DISCONNECTED) {
                Log.w("network", "DISCONNECTED")
                wifiController.wifiConnectionFlutterMethodChannel?.invokeMethod(Constants.getInstance().changeInternetConnectionStatus, false)
            } else if (info.state == NetworkInfo.State.CONNECTED) {
                if (wifiController.connectedWifiResult != null) {
                    BaseApplication.mHandler.postDelayed({
                        BaseApplication.controller?.getHostIp()
                        wifiController.connectedWifiResult?.success(hashMapOf<String, Any?>())
                        wifiController.connectedWifiResult = null
                    }, 5000)
                }

                /// ROS SDK Connection
                wifiController.connectWifiToRosSdk(robotActionController)
                Log.w("network", "CONNECTED")
                wifiController.wifiConnectionFlutterMethodChannel?.invokeMethod(Constants.getInstance().changeInternetConnectionStatus, true)
                wifiController.wifiConnectionFlutterMethodChannel?.invokeMethod(Constants.getInstance().syncMapOnConnection, null)
            } else if (info.state == NetworkInfo.State.CONNECTING) {
                Log.w("network", "CONNECTING")
            }
        }
    }
}