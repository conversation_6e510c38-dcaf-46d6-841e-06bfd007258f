<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="730dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_common_dialog"
    android:minWidth="580dp">

    <ImageView
        android:id="@+id/confirmation_dialog_ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_blue_cross_icon"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="30dp"
        android:paddingBottom="50dp"
        app:layout_constraintTop_toBottomOf="@id/confirmation_dialog_ivClose">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_location_dialog" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="40dp"
            android:background="@color/white"
            android:orientation="vertical">

            <TextView
                android:id="@+id/confirmation_dialog_tvTips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:fontFamily="@font/outfit_regular"
                android:text="@string/text_tips"
                android:textColor="@color/black"
                android:textSize="30sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/confirmation_dialog_tvMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/outfit_regular"
                android:gravity="start"
                android:textColor="@color/light_grey"
                android:textSize="22sp"
                app:layout_constraintTop_toBottomOf="@id/confirmation_dialog_tvTips" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="15dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent">

                <TextView
                    android:id="@+id/confirmation_dialog_tvConfirm"
                    android:layout_width="0dp"
                    android:layout_height="52dp"
                    android:layout_marginEnd="20dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_common_button_confirm"
                    android:fontFamily="@font/outfit_regular"
                    android:gravity="center"
                    android:text="@string/text_confirm"
                    android:textColor="@android:color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/confirmation_dialog_tvCancel"
                    android:layout_width="0dp"
                    android:layout_height="52dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_common_button_cancel"
                    android:fontFamily="@font/outfit_regular"
                    android:gravity="center"
                    android:text="@string/text_cancel"
                    android:textColor="@color/blue"
                    android:textSize="18sp" />

            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>