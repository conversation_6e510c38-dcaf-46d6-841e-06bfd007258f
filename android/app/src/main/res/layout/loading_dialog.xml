<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rlParentProgress"
    android:layout_width="730dp"
    android:layout_height="300dp"
    android:clickable="true"
    android:focusable="true"
    android:elevation="10dp"
    android:orientation="vertical"
    android:background="@drawable/bg_common_dialog"
    android:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">
        <ProgressBar
            android:id="@+id/layout_pbProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:indeterminate="true"
            android:indeterminateTint="@color/blue" />

        <TextView
            android:id="@+id/loading_dialog_tvTitle"
            android:layout_marginTop="20dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Loading"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:fontFamily="@font/outfit_regular"
            />
    </LinearLayout>



</RelativeLayout>