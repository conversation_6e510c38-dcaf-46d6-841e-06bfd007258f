<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="fontFamily">@font/outfit_regular</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>

    <style name="common_dialog_style" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/bg_common_dialog</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>


    <style name="Common_TextView" parent="android:Theme">
        <item name="android:textViewStyle">@style/MyTextViewStyle</item>
    </style>

    <style name="MyTextViewStyle" parent="android:Widget.TextView">
        <item name="android:textColor">@color/light_grey</item>
        <item name="android:background">@drawable/bg_common_button_pressed</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="fontFamily">@font/outfit_regular</item>
        <item name="android:textSize">18sp</item>
        <item name="height">70dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>

</resources>
