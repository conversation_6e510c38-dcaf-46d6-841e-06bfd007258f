<?xml version="1.0" encoding="utf-8"?>
<network-security-config>

    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>

    </base-config>
<!--    <domain-config cleartextTrafficPermitted="true">-->
<!--        <domain includeSubdomains="true">49.13.228.207</domain>-->
<!--    </domain-config>-->

<!--    <domain-config cleartextTrafficPermitted="true">-->
<!--        <domain includeSubdomains="true">192.168.0.101</domain>-->
<!--    </domain-config>-->

<!--    <domain-config cleartextTrafficPermitted="true">-->
<!--        <domain includeSubdomains="true">radiojar.com</domain>-->
<!--        &lt;!&ndash;        <domain includeSubdomains="true">finopaymentbank.in</domain>&ndash;&gt;-->
<!--        &lt;!&ndash;        <pin-set>&ndash;&gt;-->
<!--        &lt;!&ndash;            <pin digest="SHA-256">SZ8SxEqdq/MWv9kUqrWD5iWb9CUbRDsllKOzU8ttVHc=</pin>&ndash;&gt;-->
<!--        &lt;!&ndash;            <pin digest="SHA-256">8DYVmQpYS2su5qsL8x3j//ikp1vTrxCCcpphpJbZLHg=</pin>&ndash;&gt;-->
<!--        &lt;!&ndash;        </pin-setsh; TrustKit Android API &ndash;&gt;&ndash;&gt;-->
<!--        &lt;!&ndash;        &lt;!&ndash; Do not enforce pinning validation &ndash;&gt;&ndash;&gt;-->
<!--        &lt;!&ndash;        <trustkit-config enforcePinning="true">&ndash;&gt;-->
<!--        &lt;!&ndash;            &lt;!&ndash; Add a reporting URL for pin validation reports &ndash;&gt;&ndash;&gt;-->
<!--        &lt;!&ndash;            <report-uri>http://bpay.finopaymentbank.in/log_report</report-uri>&ndash;&gt;-->

<!--        &lt;!&ndash;        </trustkit-config>&ndash;&gt;-->
<!--    </domain-config>-->
</network-security-config>



