// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		457A8C6F3BD01A7C28D507CD /* libPods-Host.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 56715A205CEC5CEFA6932FF2 /* libPods-Host.a */; };
		F724F2C22329B8CC0012DB29 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F724F2C12329B8CC0012DB29 /* AppDelegate.swift */; };
		F724F2C42329B8CC0012DB29 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F724F2C32329B8CC0012DB29 /* SceneDelegate.swift */; };
		F724F2C62329B8CC0012DB29 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F724F2C52329B8CC0012DB29 /* ViewController.swift */; };
		F724F2C92329B8CC0012DB29 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F724F2C72329B8CC0012DB29 /* Main.storyboard */; };
		F724F2CB2329B8CD0012DB29 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F724F2CA2329B8CD0012DB29 /* Assets.xcassets */; };
		F724F2CE2329B8CD0012DB29 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F724F2CC2329B8CD0012DB29 /* LaunchScreen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		56715A205CEC5CEFA6932FF2 /* libPods-Host.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Host.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5EF266650049BC10ECFD8C86 /* Pods-Host.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Host.debug.xcconfig"; path = "Target Support Files/Pods-Host/Pods-Host.debug.xcconfig"; sourceTree = "<group>"; };
		A412A4193ADC80C963762A82 /* Pods-Host.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Host.release.xcconfig"; path = "Target Support Files/Pods-Host/Pods-Host.release.xcconfig"; sourceTree = "<group>"; };
		F724F2BE2329B8CC0012DB29 /* Host.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Host.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F724F2C12329B8CC0012DB29 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		F724F2C32329B8CC0012DB29 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		F724F2C52329B8CC0012DB29 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		F724F2C82329B8CC0012DB29 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		F724F2CA2329B8CD0012DB29 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F724F2CD2329B8CD0012DB29 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		F724F2CF2329B8CD0012DB29 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		F724F2BB2329B8CC0012DB29 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				457A8C6F3BD01A7C28D507CD /* libPods-Host.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4A17ED70CE5479900A9A99BB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				56715A205CEC5CEFA6932FF2 /* libPods-Host.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		794F1975CCFC17A524ECE744 /* Pods */ = {
			isa = PBXGroup;
			children = (
				5EF266650049BC10ECFD8C86 /* Pods-Host.debug.xcconfig */,
				A412A4193ADC80C963762A82 /* Pods-Host.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F724F2B52329B8CC0012DB29 = {
			isa = PBXGroup;
			children = (
				F724F2C02329B8CC0012DB29 /* Host */,
				F724F2BF2329B8CC0012DB29 /* Products */,
				794F1975CCFC17A524ECE744 /* Pods */,
				4A17ED70CE5479900A9A99BB /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		F724F2BF2329B8CC0012DB29 /* Products */ = {
			isa = PBXGroup;
			children = (
				F724F2BE2329B8CC0012DB29 /* Host.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F724F2C02329B8CC0012DB29 /* Host */ = {
			isa = PBXGroup;
			children = (
				F724F2C12329B8CC0012DB29 /* AppDelegate.swift */,
				F724F2C32329B8CC0012DB29 /* SceneDelegate.swift */,
				F724F2C52329B8CC0012DB29 /* ViewController.swift */,
				F724F2C72329B8CC0012DB29 /* Main.storyboard */,
				F724F2CA2329B8CD0012DB29 /* Assets.xcassets */,
				F724F2CC2329B8CD0012DB29 /* LaunchScreen.storyboard */,
				F724F2CF2329B8CD0012DB29 /* Info.plist */,
			);
			path = Host;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F724F2BD2329B8CC0012DB29 /* Host */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F724F2D22329B8CD0012DB29 /* Build configuration list for PBXNativeTarget "Host" */;
			buildPhases = (
				057F850E20C20171224A8BD8 /* [CP] Check Pods Manifest.lock */,
				5D4384B6DD186AC159B9ACB3 /* [CP-User] Run Flutter Build hello Script */,
				F724F2BA2329B8CC0012DB29 /* Sources */,
				F724F2BB2329B8CC0012DB29 /* Frameworks */,
				F724F2BC2329B8CC0012DB29 /* Resources */,
				D7D46336FAE5656C7ADAAD08 /* [CP] Embed Pods Frameworks */,
				96286B727046BA8457A788D0 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Host;
			productName = Host;
			productReference = F724F2BE2329B8CC0012DB29 /* Host.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F724F2B62329B8CC0012DB29 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1100;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = flutter.io;
				TargetAttributes = {
					F724F2BD2329B8CC0012DB29 = {
						CreatedOnToolsVersion = 11.0;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = F724F2B92329B8CC0012DB29 /* Build configuration list for PBXProject "Host" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F724F2B52329B8CC0012DB29;
			productRefGroup = F724F2BF2329B8CC0012DB29 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F724F2BD2329B8CC0012DB29 /* Host */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F724F2BC2329B8CC0012DB29 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F724F2CE2329B8CD0012DB29 /* LaunchScreen.storyboard in Resources */,
				F724F2CB2329B8CD0012DB29 /* Assets.xcassets in Resources */,
				F724F2C92329B8CC0012DB29 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		057F850E20C20171224A8BD8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Host-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5D4384B6DD186AC159B9ACB3 /* [CP-User] Run Flutter Build hello Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${SRCROOT}/../hello/.metadata",
				"${SRCROOT}/../hello/.ios/Flutter/App.framework/App",
				"${SRCROOT}/../hello/.ios/Flutter/engine/Flutter.framework/Flutter",
				"${SRCROOT}/../hello/.ios/Flutter/flutter_export_environment.sh",
			);
			name = "[CP-User] Run Flutter Build hello Script";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nset -u\nsource \"${SRCROOT}/../hello/.ios/Flutter/flutter_export_environment.sh\"\n\"$FLUTTER_ROOT\"/packages/flutter_tools/bin/xcode_backend.sh build\n";
		};
		96286B727046BA8457A788D0 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Host/Pods-Host-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Host/Pods-Host-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Host/Pods-Host-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D7D46336FAE5656C7ADAAD08 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Host/Pods-Host-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Host/Pods-Host-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Host/Pods-Host-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F724F2BA2329B8CC0012DB29 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F724F2C62329B8CC0012DB29 /* ViewController.swift in Sources */,
				F724F2C22329B8CC0012DB29 /* AppDelegate.swift in Sources */,
				F724F2C42329B8CC0012DB29 /* SceneDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		F724F2C72329B8CC0012DB29 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F724F2C82329B8CC0012DB29 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		F724F2CC2329B8CD0012DB29 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F724F2CD2329B8CD0012DB29 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		F724F2D02329B8CD0012DB29 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F724F2D12329B8CD0012DB29 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F724F2D32329B8CD0012DB29 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5EF266650049BC10ECFD8C86 /* Pods-Host.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = Host/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = io.flutter.add2app.Host;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F724F2D42329B8CD0012DB29 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A412A4193ADC80C963762A82 /* Pods-Host.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = Host/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = io.flutter.add2app.Host;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F724F2B92329B8CC0012DB29 /* Build configuration list for PBXProject "Host" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F724F2D02329B8CD0012DB29 /* Debug */,
				F724F2D12329B8CD0012DB29 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F724F2D22329B8CD0012DB29 /* Build configuration list for PBXNativeTarget "Host" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F724F2D32329B8CD0012DB29 /* Debug */,
				F724F2D42329B8CD0012DB29 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F724F2B62329B8CC0012DB29 /* Project object */;
}
