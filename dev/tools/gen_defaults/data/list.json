{"version": "v0_162", "md.comp.list.list-item.container.color": "surface", "md.comp.list.list-item.container.elevation": "md.sys.elevation.level0", "md.comp.list.list-item.container.shape": "md.sys.shape.corner.none", "md.comp.list.list-item.disabled.label-text.color": "onSurface", "md.comp.list.list-item.disabled.label-text.opacity": 0.38, "md.comp.list.list-item.disabled.leading-icon.color": "onSurface", "md.comp.list.list-item.disabled.leading-icon.opacity": 0.38, "md.comp.list.list-item.disabled.state-layer.color": "onSurface", "md.comp.list.list-item.disabled.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.list.list-item.disabled.trailing-icon.color": "onSurface", "md.comp.list.list-item.disabled.trailing-icon.opacity": 0.38, "md.comp.list.list-item.dragged.container.elevation": "md.sys.elevation.level4", "md.comp.list.list-item.dragged.label-text.color": "onSurface", "md.comp.list.list-item.dragged.leading-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.dragged.state-layer.color": "onSurface", "md.comp.list.list-item.dragged.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.list.list-item.dragged.trailing-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.focus.label-text.color": "onSurface", "md.comp.list.list-item.focus.leading-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.focus.state-layer.color": "onSurface", "md.comp.list.list-item.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.list.list-item.focus.trailing-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.hover.label-text.color": "onSurface", "md.comp.list.list-item.hover.leading-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.hover.state-layer.color": "onSurface", "md.comp.list.list-item.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.list.list-item.hover.trailing-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.label-text.color": "onSurface", "md.comp.list.list-item.label-text.text-style": "bodyLarge", "md.comp.list.list-item.large.leading-video.height": 69.0, "md.comp.list.list-item.leading-avatar.color": "primaryContainer", "md.comp.list.list-item.leading-avatar-label.color": "onPrimaryContainer", "md.comp.list.list-item.leading-avatar-label.text-style": "titleMedium", "md.comp.list.list-item.leading-avatar.shape": "md.sys.shape.corner.full", "md.comp.list.list-item.leading-avatar.size": 40.0, "md.comp.list.list-item.leading-icon.color": "onSurfaceVariant", "md.comp.list.list-item.leading-icon.size": 18.0, "md.comp.list.list-item.leading-image.height": 56.0, "md.comp.list.list-item.leading-image.shape": "md.sys.shape.corner.none", "md.comp.list.list-item.leading-image.width": 56.0, "md.comp.list.list-item.leading-video.shape": "md.sys.shape.corner.none", "md.comp.list.list-item.leading-video.width": 100.0, "md.comp.list.list-item.one-line.container.height": 56.0, "md.comp.list.list-item.overline.color": "onSurfaceVariant", "md.comp.list.list-item.overline.text-style": "labelSmall", "md.comp.list.list-item.pressed.label-text.color": "onSurface", "md.comp.list.list-item.pressed.leading-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.pressed.state-layer.color": "onSurface", "md.comp.list.list-item.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.list.list-item.pressed.trailing-icon.icon.color": "onSurfaceVariant", "md.comp.list.list-item.selected.trailing-icon.color": "primary", "md.comp.list.list-item.small.leading-video.height": 56.0, "md.comp.list.list-item.supporting-text.color": "onSurfaceVariant", "md.comp.list.list-item.supporting-text.text-style": "bodyMedium", "md.comp.list.list-item.three-line.container.height": 88.0, "md.comp.list.list-item.trailing-icon.color": "onSurfaceVariant", "md.comp.list.list-item.trailing-icon.size": 24.0, "md.comp.list.list-item.trailing-supporting-text.color": "onSurfaceVariant", "md.comp.list.list-item.trailing-supporting-text.text-style": "labelSmall", "md.comp.list.list-item.two-line.container.height": 72.0, "md.comp.list.list-item.unselected.trailing-icon.color": "onSurface"}