{"version": "v0_162", "md.comp.filled-text-field.active-indicator.color": "onSurfaceVariant", "md.comp.filled-text-field.active-indicator.height": 1.0, "md.comp.filled-text-field.caret.color": "primary", "md.comp.filled-text-field.container.color": "surfaceVariant", "md.comp.filled-text-field.container.shape": "md.sys.shape.corner.extra-small.top", "md.comp.filled-text-field.disabled.active-indicator.color": "onSurface", "md.comp.filled-text-field.disabled.active-indicator.height": 1.0, "md.comp.filled-text-field.disabled.active-indicator.opacity": 0.38, "md.comp.filled-text-field.disabled.container.color": "onSurface", "md.comp.filled-text-field.disabled.container.opacity": 0.04, "md.comp.filled-text-field.disabled.input-text.color": "onSurface", "md.comp.filled-text-field.disabled.input-text.opacity": 0.38, "md.comp.filled-text-field.disabled.label-text.color": "onSurface", "md.comp.filled-text-field.disabled.label-text.opacity": 0.38, "md.comp.filled-text-field.disabled.leading-icon.color": "onSurface", "md.comp.filled-text-field.disabled.leading-icon.opacity": 0.38, "md.comp.filled-text-field.disabled.supporting-text.color": "onSurface", "md.comp.filled-text-field.disabled.supporting-text.opacity": 0.38, "md.comp.filled-text-field.disabled.trailing-icon.color": "onSurface", "md.comp.filled-text-field.disabled.trailing-icon.opacity": 0.38, "md.comp.filled-text-field.error.active-indicator.color": "error", "md.comp.filled-text-field.error.focus.active-indicator.color": "error", "md.comp.filled-text-field.error.focus.caret.color": "error", "md.comp.filled-text-field.error.focus.input-text.color": "onSurface", "md.comp.filled-text-field.error.focus.label-text.color": "error", "md.comp.filled-text-field.error.focus.leading-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.error.focus.supporting-text.color": "error", "md.comp.filled-text-field.error.focus.trailing-icon.color": "error", "md.comp.filled-text-field.error.hover.active-indicator.color": "onError<PERSON><PERSON>r", "md.comp.filled-text-field.error.hover.input-text.color": "onSurface", "md.comp.filled-text-field.error.hover.label-text.color": "onError<PERSON><PERSON>r", "md.comp.filled-text-field.error.hover.leading-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.error.hover.state-layer.color": "onSurface", "md.comp.filled-text-field.error.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-text-field.error.hover.supporting-text.color": "error", "md.comp.filled-text-field.error.hover.trailing-icon.color": "onError<PERSON><PERSON>r", "md.comp.filled-text-field.error.input-text.color": "onSurface", "md.comp.filled-text-field.error.label-text.color": "error", "md.comp.filled-text-field.error.leading-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.error.supporting-text.color": "error", "md.comp.filled-text-field.error.trailing-icon.color": "error", "md.comp.filled-text-field.focus.active-indicator.color": "primary", "md.comp.filled-text-field.focus.active-indicator.height": 2.0, "md.comp.filled-text-field.focus.input-text.color": "onSurface", "md.comp.filled-text-field.focus.label-text.color": "primary", "md.comp.filled-text-field.focus.leading-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.focus.supporting-text.color": "onSurfaceVariant", "md.comp.filled-text-field.focus.trailing-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.hover.active-indicator.color": "onSurface", "md.comp.filled-text-field.hover.active-indicator.height": 1.0, "md.comp.filled-text-field.hover.input-text.color": "onSurface", "md.comp.filled-text-field.hover.label-text.color": "onSurfaceVariant", "md.comp.filled-text-field.hover.leading-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.hover.state-layer.color": "onSurface", "md.comp.filled-text-field.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-text-field.hover.supporting-text.color": "onSurfaceVariant", "md.comp.filled-text-field.hover.trailing-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.input-text.color": "onSurface", "md.comp.filled-text-field.input-text.text-style": "bodyLarge", "md.comp.filled-text-field.input-text.placeholder.color": "onSurfaceVariant", "md.comp.filled-text-field.input-text.prefix.color": "onSurfaceVariant", "md.comp.filled-text-field.input-text.suffix.color": "onSurfaceVariant", "md.comp.filled-text-field.label-text.color": "onSurfaceVariant", "md.comp.filled-text-field.label-text.text-style": "bodyLarge", "md.comp.filled-text-field.leading-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.leading-icon.size": 24.0, "md.comp.filled-text-field.supporting-text.color": "onSurfaceVariant", "md.comp.filled-text-field.supporting-text.text-style": "bodySmall", "md.comp.filled-text-field.trailing-icon.color": "onSurfaceVariant", "md.comp.filled-text-field.trailing-icon.size": 24.0}