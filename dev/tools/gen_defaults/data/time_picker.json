{"version": "v0_162", "md.comp.time-picker.clock-dial.color": "surfaceVariant", "md.comp.time-picker.clock-dial.container.size": 256.0, "md.comp.time-picker.clock-dial.label-text.text-style": "bodyLarge", "md.comp.time-picker.clock-dial.selected.label-text.color": "onPrimary", "md.comp.time-picker.clock-dial.selector.center.container.color": "primary", "md.comp.time-picker.clock-dial.selector.center.container.shape": "md.sys.shape.corner.full", "md.comp.time-picker.clock-dial.selector.center.container.size": 8.0, "md.comp.time-picker.clock-dial.selector.handle.container.color": "primary", "md.comp.time-picker.clock-dial.selector.handle.container.shape": "md.sys.shape.corner.full", "md.comp.time-picker.clock-dial.selector.handle.container.size": 48.0, "md.comp.time-picker.clock-dial.selector.track.container.color": "primary", "md.comp.time-picker.clock-dial.selector.track.container.width": 2.0, "md.comp.time-picker.clock-dial.shape": "md.sys.shape.corner.full", "md.comp.time-picker.clock-dial.unselected.label-text.color": "onSurface", "md.comp.time-picker.container.color": "surface", "md.comp.time-picker.container.elevation": "md.sys.elevation.level3", "md.comp.time-picker.container.shape": "md.sys.shape.corner.extra-large", "md.comp.time-picker.headline.color": "onSurfaceVariant", "md.comp.time-picker.headline.text-style": "labelMedium", "md.comp.time-picker.period-selector.container.shape": "md.sys.shape.corner.small", "md.comp.time-picker.period-selector.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.time-picker.period-selector.horizontal.container.height": 38.0, "md.comp.time-picker.period-selector.horizontal.container.width": 216.0, "md.comp.time-picker.period-selector.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.time-picker.period-selector.label-text.text-style": "titleMedium", "md.comp.time-picker.period-selector.outline.color": "outline", "md.comp.time-picker.period-selector.outline.width": 1.0, "md.comp.time-picker.period-selector.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.time-picker.period-selector.selected.container.color": "tertiaryContainer", "md.comp.time-picker.period-selector.selected.focus.label-text.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.selected.focus.state-layer.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.selected.hover.label-text.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.selected.hover.state-layer.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.selected.label-text.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.selected.pressed.label-text.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.selected.pressed.state-layer.color": "onTertiaryContainer", "md.comp.time-picker.period-selector.unselected.focus.label-text.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.unselected.hover.label-text.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.unselected.label-text.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.unselected.pressed.label-text.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.unselected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.time-picker.period-selector.vertical.container.height": 80.0, "md.comp.time-picker.period-selector.vertical.container.width": 52.0, "md.comp.time-picker.surface-tint-layer.color": "surfaceTint", "md.comp.time-picker.time-selector.24h-vertical.container.width": 114.0, "md.comp.time-picker.time-selector.container.height": 80.0, "md.comp.time-picker.time-selector.container.shape": "md.sys.shape.corner.small", "md.comp.time-picker.time-selector.container.width": 96.0, "md.comp.time-picker.time-selector.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.time-picker.time-selector.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.time-picker.time-selector.label-text.text-style": "displayLarge", "md.comp.time-picker.time-selector.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.time-picker.time-selector.selected.container.color": "primaryContainer", "md.comp.time-picker.time-selector.selected.focus.label-text.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.selected.focus.state-layer.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.selected.hover.label-text.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.selected.hover.state-layer.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.selected.label-text.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.selected.pressed.label-text.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.selected.pressed.state-layer.color": "onPrimaryContainer", "md.comp.time-picker.time-selector.separator.color": "onSurface", "md.comp.time-picker.time-selector.separator.text-style": "displayLarge", "md.comp.time-picker.time-selector.unselected.container.color": "surfaceVariant", "md.comp.time-picker.time-selector.unselected.focus.label-text.color": "onSurface", "md.comp.time-picker.time-selector.unselected.focus.state-layer.color": "onSurface", "md.comp.time-picker.time-selector.unselected.hover.label-text.color": "onSurface", "md.comp.time-picker.time-selector.unselected.hover.state-layer.color": "onSurface", "md.comp.time-picker.time-selector.unselected.label-text.color": "onSurface", "md.comp.time-picker.time-selector.unselected.pressed.label-text.color": "onSurface", "md.comp.time-picker.time-selector.unselected.pressed.state-layer.color": "onSurface"}