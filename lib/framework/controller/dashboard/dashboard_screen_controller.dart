// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'dart:isolate';
// import 'dart:ui';
//
// import 'package:odigo_display/framework/provider/network/api_end_points.dart';
// import 'package:odigo_display/framework/provider/network/network.dart';
// import 'package:odigo_display/framework/provider/network/network_exceptions.dart';
// import 'package:odigo_display/framework/repository/robot_meta_data_repository/contract/robot_meta_data_repository.dart';
// import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/get_assign_sync_response_model.dart';
// import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/store_list_model.dart';
// import 'package:odigo_display/framework/repository/socket/repository/socket_api_repository.dart';
// import 'package:odigo_display/framework/utils/extension/map_extension.dart';
// import 'package:odigo_display/framework/utils/helper/objectbox_client.dart';
// import 'package:odigo_display/framework/utils/helper/objectbox_response_model.dart';
//
// import 'package:odigo_display/framework/utils/ui_state.dart';
// import 'package:odigo_display/ui/utils/const/app_constants.dart';
// import 'package:odigo_display/ui/utils/widgets/common_dialogs.dart';
// import 'package:chewie/chewie.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_archive/flutter_archive.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:injectable/injectable.dart';
// import 'package:odigo_display/framework/dependency_injection/inject.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:flutter_downloader/flutter_downloader.dart';
//
// final dashboardScreenController = ChangeNotifierProvider((ref) => getIt<DashboardScreenController>());
//
// @injectable
// class DashboardScreenController extends ChangeNotifier {
//   RobotMetaDataRepository robotMetaDataRepository;
//
//   /// Socket Code
//   SocketManager socketManager;
//
//   bool isScreenTap = false;
//
//   DashboardScreenController(this.socketManager, this.robotMetaDataRepository);
//
//   ObjectBoxClient<RobotMetaDataModel> objectBoxodigo_display = ObjectBoxClient<RobotMetaDataModel>();
//   ObjectBoxResponseModel<RobotMetaDataModel?>? fetchLocalData;
//
//   CruiseMode? selectedMode;
//   Timer? searchTimer;
//
//   /// Next Play Index
//   int nextPlayIndex = 1;
//
//   /// Current Ad Index
//   int currentAdIndex = 0;
//
//   final ReceivePort downloadPort = ReceivePort();
//
//   /// Videos & Images List
//   List<odigo_displayModel> videoImagesList = [];
//
//   /// Search Stores List
//   List<StoreModel> searchStoresList = [];
//
//   /// Robot Meta Data
//   RobotMetaDataModel? robotMetaDataModel;
//
//   /// Video Player Controller
//   ChewieController? chewieController;
//
//   /// Search Mode
//   bool isSearchModeOn = false;
//
//   /// Settings Mode
//   bool isSettingsModeOn = false;
//
//   /// Light status
//   bool isLightOn = false;
//
//   /// route name list
//   List<String> routeList = [];
//
//   /// route wise route list
//   Map<String, List<List<double>>> routesMap = ({});
//
//   /// selected route
//   String? selectedRoute;
//
//   /// floor number
//   String? floorNumber;
//
//   /// sync api response
//   GetAssignSyncResponseModel? syncResponseModel;
//
//   void updateSelectedRoute(int index) {
//     selectedMode = dashboardScreenState.success?.data.target?.cruiseModes[index];
//     notifyListeners();
//   }
//
//   /// Update Screen Tap Status
//   void updateScreenTapStatus(bool status) {
//     isScreenTap = status;
//     notifyListeners();
//   }
//
//   /// Update Current Index
//   Future<void> updateCurrentIndex() async {
//     /// Increasing Index Until Src's Last Element is reached
//     if (currentAdIndex < (videoImagesList.length - 1)) {
//       currentAdIndex++;
//     } else {
//       currentAdIndex = 0;
//     }
//     showLog('videoImagesList ${videoImagesList[currentAdIndex].localPath}');
//     nextPlayIndex = currentAdIndex + 1;
//     showLog('updateCurrentIndexCalled After $currentAdIndex');
//     notifyListeners();
//   }
//
//   ///Dispose Controller
//   void disposeController({bool isNotify = false}) {
//     dashboardScreenState.isLoading = false;
//     dashboardScreenState.success = null;
//     isSearchModeOn = false;
//     robotMetaDataModel = null;
//     searchStoresList.clear();
//     isSettingsModeOn = false;
//     currentAdIndex = 0;
//     fetchLocalData = null;
//     if (isNotify) {
//       notifyListeners();
//     }
//   }
//
//   /// Update Search View
//   void updateSettingsView(bool status) {
//     isSettingsModeOn = status;
//     isSearchModeOn = false;
//     notifyListeners();
//   }
//
//   /// Update Light Status
//   updateIsLightOn() {
//     isLightOn = !isLightOn;
//     notifyListeners();
//   }
//
//   /// Update Widget
//   void updateWidget() {
//     notifyListeners();
//   }
//
//   /// On Init Called
//   void initCall() {
//     /// Binding Isolates
//     bindBackgroundIsolate();
//
//     FlutterDownloader.registerCallback(downloadCallback, step: 1);
//   }
//
//   /// Binding Isolates
//   void bindBackgroundIsolate() {
//     final isSuccess = IsolateNameServer.registerPortWithName(
//       downloadPort.sendPort,
//       'downloader_send_port',
//     );
//     if (!isSuccess) {
//       unbindBackgroundIsolate();
//       bindBackgroundIsolate();
//       return;
//     }
//     downloadPort.listen((dynamic data) {
//       final taskId = (data as List<dynamic>)[0] as String;
//       final status = DownloadTaskStatus.fromInt(data[1] as int);
//       final progress = data[2] as int;
//
//       showLog(
//         'Callback on UI isolate: ' 'task ($taskId) is in status ($status) and process ($progress)',
//       );
//
//       // if (_tasks != null && _tasks!.isNotEmpty) {
//       //   final task = _tasks!.firstWhere((task) => task.taskId == taskId);
//       //   setState(() {
//       //     task
//       //       ..status = status
//       //       ..progress = progress;
//       //   });
//       // }
//     });
//   }
//
//   void unbindBackgroundIsolate() {
//     IsolateNameServer.removePortNameMapping('downloader_send_port');
//   }
//
//   @pragma('vm:entry-point')
//   static void downloadCallback(
//     String id,
//     int status,
//     int progress,
//   ) {
//     showLog(
//       'Callback on background isolate: '
//       'task ($id) is in status ($status) and process ($progress)',
//     );
//
//     IsolateNameServer.lookupPortByName('downloader_send_port')?.send([id, status, progress]);
//   }
//
//   /// Dashboard Screen State
//   UIState<RobotMetaDataModel> dashboardScreenState = UIState<RobotMetaDataModel>();
//
//   /// update loading
//   void updateLoadingStatus(value) {
//     dashboardScreenState.isLoading = value;
//     notifyListeners();
//   }
//
//   /// Update Data From Object Box From Json
//   Future<void> updateDataFromObjectBox(BuildContext context) async {
//     videoImagesList.clear();
//     searchStoresList.clear();
//     // String data = await DefaultAssetBundle.of(context).loadString(Assets.json.storeJson);
//     // dashboardScreenState.success = robotMetaDataModelFromJson(data);
//
//     Directory? getFilesDirectory = await getExternalStorageDirectory();
//     String localPath = getFilesDirectory?.path ?? '';
//     Directory? savedVideosDir;
//     Directory? savedImageDir;
//
//     if (!(getFilesDirectory?.existsSync() ?? false)) {
//       await getFilesDirectory?.create();
//       showLog('savedDir path  ${getFilesDirectory?.path}');
//     } else {
//       showLog('savedDir already exist  ${getFilesDirectory?.path}');
//     }
//
//     List<String> localPathList = [];
//     List<String> mimeTypeList = [];
//     List<String> localMimeTypeList = [];
//
//     List<FileSystemEntity> videosList = [];
//     List<FileSystemEntity> imagesList = [];
//
//     showLog('videosList length ${videosList.length}');
//     showLog('imagesList length ${imagesList.length}');
//
//     // RobotMetaDataModel? _robotModel;
//     // if (fetchLocalData != null) {
//     //   _robotModel = fetchLocalData!.data;
//     // } else {
//     //   _robotModel = robotMetaDataModel;
//     // }
//
//     List<StoreModel> storesList = [];
//     for (StoreModel store in dashboardScreenState.success?.data.target?.stores ?? []) {
//       showLog('robotMetaDataModel?.data?.stores ${dashboardScreenState.success?.data.target?.stores}');
//
//       /// Store wise directory
//       final savedStoreDirectory = Directory('$localPath/${store.storeName?.replaceAll(' ', '')}');
//
//       if (!savedStoreDirectory.existsSync()) {
//         await savedStoreDirectory.create();
//         // showLog('savedStoreDirectory path  ${savedStoreDirectory.path}');
//       } else {
//         // showLog('savedStoreDirectory already exist  ${savedStoreDirectory.path}');
//       }
//
//       for (int i = 0; i < store.content.length; i++) {
//         /// Check File Type
//         // showLog('store.content[i].fileType ${store.content[i].fileType}');
//         if (store.content[i].fileType == 'Video') {
//           savedVideosDir = Directory('$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName');
//           if (!savedVideosDir.existsSync()) {
//             await savedVideosDir.create();
//             // showLog('savedVideosDir path  ${savedVideosDir.path}');
//           } else {
//             // showLog('savedVideosDir already exist  ${savedVideosDir.path}');
//           }
//         } else {
//           savedImageDir = Directory('$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName');
//           if (!savedImageDir.existsSync()) {
//             await savedImageDir.create();
//             // showLog('savedImageDir path  ${savedImageDir.path}');
//           } else {
//             // showLog('savedImageDir already exist  ${savedImageDir.path}');
//           }
//         }
//
//         /// Get Mime Type
//         String? mimeType = store.content[i].url!.contains('?') ? store.content[i].url?.split('?').first.split('.').last : store.content[i].url?.split('.').last;
//         // showLog('mimeTypeUrl $mimeType');
//         if (!mimeTypeList.contains(mimeType) && mimeType != null) {
//           mimeTypeList.add(mimeType);
//         }
//         // showLog('mimeType $mimeType');
//
//         /// Video List
//         if (savedVideosDir != null) {
//           videosList = savedVideosDir.listSync().where((entity) {
//             // showLog('entity videosList ${entity.path}');
//             if (localMimeTypeList.isEmpty) {
//               localMimeTypeList.add(entity.path.split('.').last);
//             } else if (!localMimeTypeList.contains(entity.path.split('.').last)) {
//               localMimeTypeList.add(entity.path.split('.').last);
//             }
//             // showLog('localMimeTypeList $localMimeTypeList');
//             localPathList.add(entity.path.split('/').last.split('.').first);
//             return localMimeTypeList.contains(entity.path.split('.').last);
//           }).toList();
//
//           // showLog('videos length ${videosList.length}');
//         }
//
//         /// Images List
//         if (savedImageDir != null) {
//           imagesList = savedImageDir.listSync().where((entity) {
//             // showLog('entity imagesList ${entity.path}');
//             if (localMimeTypeList.isEmpty) {
//               localMimeTypeList.add(entity.path.split('.').last);
//             } else if (!localMimeTypeList.contains(entity.path.split('.').last)) {
//               localMimeTypeList.add(entity.path.split('.').last);
//             }
//             // showLog('localMimeTypeList $localMimeTypeList');
//             localPathList.add(entity.path.split('/').last.split('.').first);
//             return localMimeTypeList.contains(entity.path.split('.').last);
//           }).toList();
//
//           // showLog('imagesList length ${imagesList.length}');
//         }
//
//         ///-------------------------------- VIDEO --------------------------------///
//         if (store.content[i].fileType == 'Video') {
//           /// When video list is empty
//
//           if (videosList.isEmpty) {
//             final taskId = await FlutterDownloader.enqueue(
//               url: store.content[i].url ?? '',
//               headers: {},
//               savedDir: store.content[i].fileType == 'Video' ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName' : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName',
//               showNotification: false,
//               saveInPublicStorage: false,
//               fileName: store.content[i].fileType == 'Video' ? '${store.content[i].odigo_displayId}.$mimeType' : '${store.content[i].odigo_displayId}.png',
//               openFileFromNotification: true, // click on notification to open downloaded file (for Android)
//             );
//
//             store.content[i].taskId = taskId;
//             store.content[i].localPath = store.content[i].fileType == 'Video'
//                 ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName/${'${store.content[i].odigo_displayId}.$mimeType'}'
//                 : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName/${'${store.content[i].odigo_displayId}.png'}';
//             // showLog('When video list is Empty downloading videos ${store.content[i].localPath}');
//           } else {
//             // showLog('When video list is NotEmpty ');
//
//             if (localPathList.contains(store.content[i].odigo_displayId)) {
//               store.content[i].localPath = store.content[i].fileType == 'Video'
//                   ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName/${'${store.content[i].odigo_displayId}.$mimeType'}'
//                   : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName/${'${store.content[i].odigo_displayId}.png'}';
//               // showLog('check here ${store.content[i].localPath}');
//             } else {
//               final taskId = await FlutterDownloader.enqueue(
//                 url: store.content[i].url ?? '',
//                 headers: {},
//                 savedDir: store.content[i].fileType == 'Video' ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName' : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName',
//                 showNotification: false,
//                 saveInPublicStorage: false,
//                 fileName: store.content[i].fileType == 'Video' ? '${store.content[i].odigo_displayId}.$mimeType' : '${store.content[i].odigo_displayId}.png',
//                 openFileFromNotification: true, // click on notification to open downloaded file (for Android)
//               );
//               store.content[i].taskId = taskId;
//               store.content[i].localPath = store.content[i].fileType == 'Video'
//                   ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName/${'${store.content[i].odigo_displayId}.$mimeType'}'
//                   : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName/${'${store.content[i].odigo_displayId}.png'}';
//             }
//             // showLog('store.content[i].localPath video ${store.content[i].localPath}');
//           }
//         }
//
//         ///-------------------------------- IMAGE --------------------------------///
//         if (store.content[i].fileType == 'image') {
//           if (imagesList.isEmpty) {
//             final taskId = await FlutterDownloader.enqueue(
//               url: store.content[i].url ?? '',
//               headers: {},
//               savedDir: store.content[i].fileType == 'Video' ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName' : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName',
//               showNotification: false,
//               saveInPublicStorage: false,
//               fileName: store.content[i].fileType == 'Video' ? '${store.content[i].odigo_displayId}.$mimeType' : '${store.content[i].odigo_displayId}.png',
//               openFileFromNotification: true, // click on notification to open downloaded file (for Android)
//             );
//             store.content[i].taskId = taskId;
//             store.content[i].localPath = store.content[i].fileType == 'Video'
//                 ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName/${'${store.content[i].odigo_displayId}.$mimeType'}'
//                 : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName/${'${store.content[i].odigo_displayId}.png'}';
//             // showLog('When image list is Empty downloading image $taskId');
//           } else {
//             // showLog('When image list is NotEmpty ');
//
//             if (localPathList.contains(store.content[i].odigo_displayId)) {
//               store.content[i].localPath = store.content[i].fileType == 'Video'
//                   ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName/${'${store.content[i].odigo_displayId}.$mimeType'}'
//                   : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName/${'${store.content[i].odigo_displayId}.png'}';
//             } else {
//               final taskId = await FlutterDownloader.enqueue(
//                 url: store.content[i].url ?? '',
//                 headers: {},
//                 savedDir: store.content[i].fileType == 'Video' ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName' : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName',
//                 showNotification: false,
//                 saveInPublicStorage: false,
//                 fileName: store.content[i].fileType == 'Video' ? '${store.content[i].odigo_displayId}.$mimeType' : '${store.content[i].odigo_displayId}.png',
//                 openFileFromNotification: true, // click on notification to open downloaded file (for Android)
//               );
//               store.content[i].taskId = taskId;
//               store.content[i].localPath = store.content[i].fileType == 'Video'
//                   ? '$localPath/${store.storeName?.replaceAll(' ', '')}/$videoFolderName/${'${store.content[i].odigo_displayId}.$mimeType'}'
//                   : '$localPath/${store.storeName?.replaceAll(' ', '')}/$imageFolderName/${'${store.content[i].odigo_displayId}.png'}';
//               // showLog('store.content[i] ${store.content[i].localPath}');
//             }
//           }
//           // showLog('store.content[i].localPath image ${store.content[i].localPath}');
//         }
//
//         videoImagesList.add(store.content[i]);
//         showLog('videoImagesList ${videoImagesList.length}');
//       }
//     }
//     storesList.addAll(dashboardScreenState.success!.data.target!.stores);
//
//     /// Saving Object Box Data Locally
//     ObjectBoxClient<RobotData> objectBoxRobotData = ObjectBoxClient<RobotData>();
//     ObjectBoxClient<RobotMetaData> oBRobotMetaData = ObjectBoxClient<RobotMetaData>();
//     ObjectBoxClient<SystemMetadata> obSystemMetaData = ObjectBoxClient<SystemMetadata>();
//     ObjectBoxClient<StoreModel> obStoreData = ObjectBoxClient<StoreModel>();
//     ObjectBoxClient<CruiseMode> oBCruiseModeData = ObjectBoxClient<CruiseMode>();
//
//     /// Robot Data
//     if (dashboardScreenState.success?.data.target != null) {
//       if (oBRobotMetaData.box.isEmpty()) {
//         if (dashboardScreenState.success?.data.target?.robotMetaData.target != null) {
//           await oBRobotMetaData.add(dashboardScreenState.success!.data.target!.robotMetaData.target!);
//         }
//       }
//       if (obSystemMetaData.box.isEmpty()) {
//         if (dashboardScreenState.success?.data.target?.systemMetadata.target != null) {
//           await obSystemMetaData.add(dashboardScreenState.success!.data.target!.systemMetadata.target!);
//         }
//       }
//
//       if (obStoreData.box.isEmpty()) {
//         if (dashboardScreenState.success?.data.target?.stores.isNotEmpty ?? false) {
//           await obStoreData.addList(dashboardScreenState.success!.data.target!.stores);
//         }
//       }
//       if (oBCruiseModeData.box.isEmpty()) {
//         if (dashboardScreenState.success?.data.target?.cruiseModes.isNotEmpty ?? false) {
//           await oBCruiseModeData.addList(dashboardScreenState.success!.data.target!.cruiseModes);
//         }
//       }
//     }
//
//     /// Get Response
//     ObjectBoxResponseModel<RobotMetaData?> robotMetaDataResponse = await oBRobotMetaData.getSingle(1);
//     ObjectBoxResponseModel<SystemMetadata?> systemDataResponse = await obSystemMetaData.getSingle(1);
//     ObjectBoxResponseModel<List<StoreModel>?> storeModelResponse = await obStoreData.getAll();
//     ObjectBoxResponseModel<List<CruiseMode>?> cruiseModelResponse = await oBCruiseModeData.getAll();
//
// // showLog('robotMetaDataResponse. ${robotMetaDataResponse.data?.id}');
// // showLog('robotMetaDataResponse. ${systemDataResponse.data?.lastSyncDateTime}');
// // showLog('store list. ${storeModelResponse.data?.first.content.first.url}');
// // showLog('cruise mode list. ${cruiseModelResponse.data?.length}');
//
//     RobotMetaDataModel robotMetaDataModel = RobotMetaDataModel(
//       status: dashboardScreenState.success?.status,
//       message: dashboardScreenState.success?.message,
//     );
//     RobotData robotData = RobotData();
//     robotData.robotMetaData.target = robotMetaDataResponse.data;
//     robotData.systemMetadata.target = systemDataResponse.data;
//     robotData.stores.addAll(storeModelResponse.data!);
//     robotData.cruiseModes.addAll(cruiseModelResponse.data!);
//
//     /// Robot Data Store into Main model
//     robotMetaDataModel.data.target = robotData;
// // showLog('robotMetaDataModel.robotMetaData.target ${robotMetaDataModel.data.target?.stores.first.storeName}');
//
//     if (objectBoxodigo_display.box.isEmpty()) {
//       objectBoxodigo_display.add(robotMetaDataModel);
//     }
//
//     await Future.delayed(const Duration(milliseconds: 100), () async {
//       fetchLocalData = await objectBoxodigo_display.getSingle(1);
//       showLog('res ${fetchLocalData?.data?.data.target?.robotMetaData.target?.speed}');
//       showLog('res ${fetchLocalData?.data?.data.target?.systemMetadata.target?.id}');
//       showLog('res ${fetchLocalData?.data?.data.target?.stores.first.storeName}');
//       showLog('res ${fetchLocalData?.data?.data.target?.cruiseModes.length}');
//       showLog('res check here ${fetchLocalData?.data?.data.target?.stores.first.content[4].localPath}');
//     });
//     searchStoresList.addAll(robotData.stores);
//     // for (int i = 0; i < storesList.length; i++) {
//     //   fetchLocalData?.data?.data.target?.stores[i] = StoreModel(
//     //     id: storesList[i].id,
//     //     createAt: storesList[i].createAt,
//     //     perAdCharge: storesList[i].perAdCharge,
//     //     storeDescription: storesList[i].storeDescription,
//     //     storeDescriptionArabic: storesList[i].storeDescriptionArabic,
//     //     storeName: storesList[i].storeName,
//     //     storeNameArabic: storesList[i].storeNameArabic,
//     //     storePointName: storesList[i].storePointName,
//     //     totalAdCount: storesList[i].totalAdCount,
//     //     uuid: storesList[i].uuid,
//     //     walletBalance: storesList[i].walletBalance,
//     //   );
//     //   objectBoxodigo_display.update(storesList[i].id, fetchLocalData!.data!);
//     // }
//     // showLog('dashboardScreenState.isLoading ${dashboardScreenState.isLoading}');
//     dashboardScreenState.isLoading = false;
//     notifyListeners();
//   }
//
//   ///-------------------------------- API --------------------------------///
//
//   UIState<GetAssignSyncResponseModel> getSyncDataAPIState = UIState<GetAssignSyncResponseModel>();
//
//   /// Object Box Store Table
//   ObjectBoxClient<GetAssignSyncResponseModel> objMainSyncData = ObjectBoxClient<GetAssignSyncResponseModel>();
//   ObjectBoxClient<AssignSyncData> objSyncData = ObjectBoxClient<AssignSyncData>();
//   ObjectBoxClient<AssignAdSequence> objAdSequence = ObjectBoxClient<AssignAdSequence>();
//   ObjectBoxClient<AssignAdDetail> objAdDetails = ObjectBoxClient<AssignAdDetail>();
//
//   /// Object Box Response
//   ObjectBoxResponseModel<GetAssignSyncResponseModel?>? objMainSyncDataResponse;
//
//   ObjectBoxResponseModel<AssignSyncData?>? objSyncDataRes;
//
//   ObjectBoxResponseModel<List<AssignAdSequence>?>? objAdSequenceRes;
//
//   ObjectBoxResponseModel<AssignAdDetail?>? objAdDetailsRes;
//
//   /// Unzip File
//   void unzipFiles({required String filePath, required String location}) {
//     final zipFile = File(filePath);
//     final destinationDir = Directory(location);
//     try {
//       ZipFile.extractToDirectory(zipFile: zipFile, destinationDir: destinationDir);
//     } catch (e) {
//       showLog('Zip error $e');
//     }
//   }
//
// // ///For animation of dialog
// // AnimationController? animationController;
// //
// // ///For error text in authenticate dialog otp
// // bool isErrorTextVisible = false;
// //
// // updateIsErrorTextVisible(bool isErrorTextVisible) {
// //   this.isErrorTextVisible = isErrorTextVisible;
// //   notifyListeners();
// // }
//
// // ///For authenticate Dialog
// // bool isAuthenticationDialogVisible = false;
// //
// // updateIsAuthenticationDialogVisible(bool isAuthenticationDialogVisible) {
// //   this.isAuthenticationDialogVisible = isAuthenticationDialogVisible;
// //   notifyListeners();
// // }
// }
