import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_display/framework/controller/navigation/navigation_controller.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/interruption/model/charging_interruption.dart';
import 'package:odigo_display/framework/repository/interruption/model/emergency_interruption.dart';
import 'package:odigo_display/framework/repository/interruption/model/interaction_charging_interruption.dart';
import 'package:odigo_display/framework/repository/interruption/model/interaction_emergency_interruption.dart';
import 'package:odigo_display/framework/repository/interruption/model/interaction_navigation_interruption.dart';
import 'package:odigo_display/framework/repository/interruption/model/navigation_interruption.dart';
import 'package:odigo_display/framework/repository/interruption/model/operator_interruption.dart';
import 'package:odigo_display/framework/repository/new_logic/model/category_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/repository/operator/contract/operator_repository.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/const/app_enums.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

final helpInfoDashboardController = ChangeNotifierProvider((ref) => getIt<HelpInfoDashboardController>());

@injectable
class HelpInfoDashboardController extends ChangeNotifier {
  /// Socket Code
  // SocketManager socketManager;
  OperatorRepository operatorRepository;

  HelpInfoDashboardController(this.operatorRepository);

  /// Text Editing Controller
  TextEditingController passwordCTR = TextEditingController();
  FocusNode focusNode = FocusNode();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  List<StoreData> storeList = [];
  List<CategoryData> categoriesList = [];
  CategoryData? selectedCategory;
  List<StoreData> searchStoresList = [];

  bool allowInteraction = true;

  bool isShowStickyNote = false;

  void showStickyNoteStatus(bool status) {
    isShowStickyNote = status;
    notifyListeners();
  }

  updateAllowInteraction(bool value) {
    allowInteraction = value;
    showLog('Update Allow Interaction $allowInteraction');
    notifyListeners();
  }

  void updateSelectedCategory(CategoryData? selectedCategory) {
    this.selectedCategory = selectedCategory;
    searchTxtCTR.clear();
    _onTextChanged(changedText: '');
    // _onTextChanged(changedText: selectedCategory?.name ?? '');

    getSearchStores();
    notifyListeners();
  }

  void notify() {
    notifyListeners();
  }

  GlobalKey confirmationDialogKey = GlobalKey();
  String elapsedSeconds = '';

  updateElapsedSeconds(int value) {
    elapsedSeconds = (stopRobotSeconds - value).formatTime;
    // elapsedSeconds = value;
    showLog("elapsedSeconds ${elapsedSeconds}");
    notifyListeners();
  }

  // String elapsedSecondsDialog = '';
  // updateElapsedSecondsDialog(int value){
  //   elapsedSecondsDialog = formatTime((stopRobotSeconds - value));
  //   // elapsedSeconds = value;
  //   showLog("elapsedSeconds Dialog${elapsedSecondsDialog}");
  //   notifyListeners();
  // }

  /// Listener handles text change event
  void _onTextChanged({required String changedText}) {
    String currentText = searchTxtCTR.text;
    int cursorPosition = searchTxtCTR.selection.extentOffset;
    int cursorStartPosition = searchTxtCTR.selection.baseOffset;
    if (cursorStartPosition >= 0 && cursorStartPosition != cursorPosition) {
      currentText = currentText.replaceRange(cursorStartPosition, cursorPosition, "");
      cursorPosition = cursorStartPosition;
      if (cursorPosition < 0) {
        cursorPosition = 0;
      }
    }
    String prefix = "";
    if (cursorPosition > 0) {
      prefix = currentText.substring(0, cursorPosition);
    }
    if (cursorPosition < 0) {
      cursorPosition = 0;
    }
    String suffix = currentText.substring(cursorPosition);
    String completeText = prefix + changedText + suffix;
    searchTxtCTR.text = completeText;

    if (currentText.length != searchTxtCTR.text.length) {
      cursorPosition += changedText.length;
    }
    if (cursorPosition > completeText.length) {
      cursorPosition = completeText.length;
    }
    searchTxtCTR.selection = TextSelection.fromPosition(TextPosition(offset: cursorPosition));
  }

  TextEditingController searchTxtCTR = TextEditingController();

  ///For authenticate Dialog
  bool isAuthenticationDialogVisible = false;

  /// Search Mode
  bool isSearchModeOn = false;

  /// Settings Mode
  bool isSettingsModeOn = false;

  bool isStartStoreNavigation = false;

  void updateStartStoreNavigation(bool status) {
    isStartStoreNavigation = status;
    notifyListeners();
  }

  /// Update Search View
  void updateSearchView(bool status) {
    isSearchModeOn = status;
    isSettingsModeOn = false;
    searchTxtCTR.text = '';
    getSearchStores();
    if (!status) {
      if (helpWidgetKey.currentContext != null) {
        ScaffoldMessenger.of(helpWidgetKey.currentContext!).clearSnackBars();
      }
    }
    notifyListeners();
  }

  /// Update Search View
  void updateSettingsView(bool status) {
    isSettingsModeOn = status;
    isSearchModeOn = false;
    notifyListeners();
  }

  updateIsAuthenticationDialogVisible(BuildContext context, bool isAuthenticationDialogVisible, {required WidgetRef ref}) {
    if (isAuthenticationDialogVisible) {
      // globalRef?.read(displayAdsScreenController).showingMediaContent(context, isInterrupt: true, ref: ref);
    }
    this.isAuthenticationDialogVisible = isAuthenticationDialogVisible;
    notifyListeners();
  }

  Future<void> getStoreList() async {
    storeList.clear();
    categoriesList.clear();
    List<StoreData> responseModel = await operatorRepository.getStoreList();
    categoriesList = await operatorRepository.getCategoriesList();

    categoriesList.insert(
      0,
      CategoryData(
        uuid: 'all_categories',
        isDeleted: false,
        categoryId: -1,
        categoryImageUrl: '',
        iconPath: '',
        name: 'All',
      ),
    );

    storeList = responseModel;
    getSearchStores();
    notifyListeners();
  }

  /// Get Search Stores
  void getSearchStores() {
    var text = searchTxtCTR.text.trim();
    List<StoreData> categoriesSearchedStoreList = [];
    searchStoresList = storeList.where((store) => ((store.name?.toLowerCase().contains(text.toLowerCase()) ?? false) && (selectedCategory?.name?.toLowerCase() == 'all' ? true : store.categoryData.any((element) => element.name?.toLowerCase().startsWith(selectedCategory?.name?.toLowerCase() ?? '') ?? false)))).toList();
    showLog('searchStoresList searchStoresList ${searchStoresList.length}');
    categoriesSearchedStoreList = storeList.where((store) => ((store.categoryData.any((element) => element.name?.toLowerCase().contains(text.toLowerCase()) ?? false) && (selectedCategory?.name?.toLowerCase() == 'all' ? true : store.categoryData.any((element) => element.name?.toLowerCase().startsWith(selectedCategory?.name?.toLowerCase() ?? '') ?? false))))).toList();

    searchStoresList.addAll(categoriesSearchedStoreList);
    var defaultStoreSet = <String>{};
    searchStoresList = searchStoresList.where((adDetails) => defaultStoreSet.add(adDetails.uuid ?? '')).toList();
    showLog("searchStoresList >>>> ${searchStoresList.length}");
    showLog("searchStoresList >>>> ${searchStoresList.map((e) => e.name).toList()}");
    notifyListeners();
  }

  int getStoreCountForCategory(String category) {
    List<StoreData> categoriesSearchedStoreList = [];
    categoriesSearchedStoreList = storeList.where((store) => store.categoryData.any((element) => element.name?.toLowerCase().contains(category.toLowerCase()) ?? false)).toList();
    return categoriesSearchedStoreList.length;
  }

  ///For error text in authenticate dialog otp
  bool isErrorTextVisible = false;

  updateIsErrorTextVisible(bool isErrorTextVisible) {
    this.isErrorTextVisible = isErrorTextVisible;
    notifyListeners();
  }

  ///For animation of dialog
  AnimationController? animationController;

  ///------------- Timer ------------------------///
  Timer? searchTimer;
  bool isNavigationDialogShown = false;

  updateDialogShownCondition(value) {
    isNavigationDialogShown = value;
    notifyListeners();
  }

  GlobalKey helpWidgetKey = GlobalKey();

  Timer? robotStopTimer;

  // Timer? robotStopTimerDialog;
  bool isStop = false;

  cancelRobotStopTimer() {
    robotStopTimer?.cancel();
    robotStopTimer = null;
    elapsedSeconds = '';
    notifyListeners();
  }

  Future<void> stopRobotForFewSeconds(BuildContext context, {required WidgetRef ref, bool isFromWaiting = false, bool isFromMore = false, isFromMoreDetails = false, bool isCloseButtonTap = false}) async {
    if (!isCloseButtonTap) {
      globalRef!.read(displayAdsController).pauseNavigation();

      if (robotStopTimer != null) {
        if (robotStopTimer?.isActive ?? false) {
          showLog('Cancel Timer On Every Tap');
          robotStopTimer?.cancel();
          robotStopTimer = null;
        }
      }
      notifyListeners();

      robotStopTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        showLog('Robot Stop Timer Calling ${timer.tick}');
        updateElapsedSeconds(timer.tick);
        if (timer.isActive && timer.tick == stopRobotSeconds) {
          updateSearchView(false);
          updateSearchMode(false);
          robotStopTimer?.cancel();
          robotStopTimer = null;
          timer.cancel();

          if (!isFromWaiting) {
            globalRef!.watch(displayAdsController).resumeNavigation();
          }
          if (isFromMore == true) {
            ref.watch(displayAdsController).resumeNavigation();
            Navigator.pop(context);
          }
          if (isFromMoreDetails == true) {
            globalRef?.watch(displayAdsController).resumeNavigation();
            Navigator.pop(context);
            Navigator.pop(context);
            Navigator.pop(context);
          }
          showLog('Key wise condition resume ${confirmationDialogKey.currentContext}');
          showLog('isNavigationDialogShown ${confirmationDialogKey.currentContext}');
          showLog('isNavigationDialogShown ${isNavigationDialogShown}');
          if (confirmationDialogKey.currentContext != null) {
            Navigator.pop(confirmationDialogKey.currentContext!);
            isNavigationDialogShown = false;
          }

          globalRef!.watch(displayAdsController).startCruiseMode(ref: ref);
          globalRef!.watch(helpInfoDashboardController).updateIsAuthenticationDialogVisible(context, false, ref: ref);
          notifyListeners();
        }
      });
    } else {
      updateSearchView(false);
      updateSearchMode(false);
      robotStopTimer?.cancel();
      robotStopTimer = null;

      // if (ref.read(operatorController).isOpenNewDashboardMobile == false) {
      //ref.read(displayAdsScreenController).resumeNavigation();
      // }
      if (!isFromWaiting) {
        globalRef!.watch(displayAdsController).resumeNavigation();
      }
      if (isFromMore == true) {
        ref.watch(displayAdsController).resumeNavigation();
        Navigator.pop(context);
      }
      if (isFromMoreDetails == true) {
        globalRef?.watch(displayAdsController).resumeNavigation();
        Navigator.pop(context);
        Navigator.pop(context);
        Navigator.pop(context);
      }
      showLog('Key wise condition start ${confirmationDialogKey.currentContext}');
      showLog('isNavigationDialogShown ${confirmationDialogKey.currentContext}');
      showLog('isNavigationDialogShown ${isNavigationDialogShown}');
      if (confirmationDialogKey.currentContext != null) {
        Navigator.pop(confirmationDialogKey.currentContext!);
        isNavigationDialogShown = false;
      }

      globalRef!.watch(displayAdsController).startCruiseMode(ref: ref);
      ref.watch(helpInfoDashboardController).updateIsAuthenticationDialogVisible(context, false, ref: ref);
      notifyListeners();
    }
    notifyListeners();
    // reStartTimer(context, ref: ref);
  }

  // Future<void> stopRobotForFewSecondsDialog(BuildContext context) async {
  //
  //   robotStopTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
  //     showLog('Robot Stop Timer Calling Dialog ${timer.tick}');
  //     updateElapsedSeconds(timer.tick);
  //
  //     if (timer.isActive && timer.tick == stopRobotSeconds) {
  //       robotStopTimer?.cancel();
  //       robotStopTimer = null;
  //       timer.cancel();
  //       if (confirmationDialogKey.currentContext != null) {
  //         Navigator.pop(confirmationDialogKey.currentContext!);
  //       }
  //     }
  //   });
  //   notifyListeners();
  //   // reStartTimer(context, ref: ref);
  // }

  bool searchEnable = false;

  updateSearchMode(value) {
    searchEnable = value;
    showLog("str >>>>>>>>>>>");
    notifyListeners();
  }

  int selectedExp = -1;

  updateSelectedExpansion(int value) {
    selectedExp = value;
    notifyListeners();
  }

  // Future<void> reStartTimer(BuildContext context, {required WidgetRef ref, bool resumeNavigation = true}) async {
  //   if (searchTimer?.isActive ?? false) {
  //     searchTimer?.cancel();
  //     searchTimer = null;
  //   }
  //
  //   /// TODO Migration
  //   // if (socketManager.waitingStoreTimer?.isActive ?? false) {
  //   //   socketManager.waitingStoreTimer?.cancel();
  //   // }
  //   // if (socketManager.waitingStoreTimer != null) {
  //   //   socketManager.waitingStoreTimer = Timer.periodic(Duration(seconds: waitingTimeSeconds), (timer) {
  //   //     if (timer.tick == waitingTimeSeconds || socketManager.isCruiseModeStarted) {
  //   //       if (socketManager.isCruiseModeStarted) {
  //   //         ///For two different modes Global changes
  //   //         ref.read(operatorController).displayMode == DisplayMode.CRUISE
  //   //             ? socketManager.startRouteNavigation(SessionRepository.session.selectedRoute ?? '')
  //   //             : socketManager.goToProduction(pointName: SessionRepository.session.selectedProduction ?? '');
  //   //       } else {
  //   //         socketManager.goToProduction();
  //   //       }
  //   //       socketManager.waitingStoreTimer?.cancel();
  //   //       socketManager.waitingStoreTimer = null;
  //   //     }
  //   //   });
  //   // }
  //   searchTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
  //     if (timer.tick == waitingTimeSeconds) {
  //       searchTimer?.cancel();
  //       searchTimer == null;
  //       updateSearchView(false);
  //       if (isStoreDialogOpen) {
  //         Navigator.pop(context);
  //       }
  //       if (helpWidgetKey.currentContext != null) {
  //         Navigator.pop(helpWidgetKey.currentContext!);
  //       }
  //       if (isAuthenticationDialogVisible) {
  //         animationController?.reverse(from: 0.3).then((value) {
  //           passwordCTR.clear();
  //           globalRef?.read(displayAdsScreenController).chewieController?.play();
  //           updateIsAuthenticationDialogVisible(context, false, ref: ref);
  //           if (searchTimer?.isActive ?? false) {
  //             searchTimer?.cancel();
  //             searchTimer = null;
  //           }
  //         });
  //       }
  //       if (resumeNavigation) {
  //         /// TODO Migration
  //         // socketManager.resumeNavigation();
  //       }
  //     }
  //   });
  // }

  OperatorInterruption? operatorInterruption;

  Future<void> operatorStartInterruptionTime({InterruptedAdsTypeEnum? type = InterruptedAdsTypeEnum.operator}) async {
    operatorInterruption = OperatorInterruption(startedInterruptionTime: DateTime.now(), type: type?.name);
    showLog("str >>>>>> ${operatorInterruption?.startedInterruptionTime.toString()}");
    operatorInterruption = (await operatorRepository.addOperatorInterruption(operatorInterruption!)).data;
  }

  Future<void> operatorEndInterruptionTime() async {
    operatorInterruption?.endedInterruption = DateTime.now();
    showLog("str >>>>>> ${operatorInterruption?.endedInterruption.toString()}");
    if (operatorInterruption != null) {
      await operatorRepository.addOperatorInterruption(operatorInterruption!);
    }
    operatorInterruption = null;
  }

  EmergencyInterruption? emergencyInterruption;
  InteractionEmergencyInterruption? interactionEmergencyInterruption;

  Future<void> emergencyStartInterruptionTime() async {
    emergencyInterruption = EmergencyInterruption(startedInterruptionTime: DateTime.now());
    interactionEmergencyInterruption = InteractionEmergencyInterruption(startedInterruptionTime: DateTime.now());
    emergencyInterruption = (await operatorRepository.addEmergencyInterruption(emergencyInterruption!)).data;
    interactionEmergencyInterruption = (await operatorRepository.addInterruptionEmergencyInterruption(interactionEmergencyInterruption!)).data;
  }

  Future<void> emergencyEndInterruptionTime() async {
    emergencyInterruption?.endedInterruption = DateTime.now();
    interactionEmergencyInterruption?.endedInterruption = DateTime.now();
    if (emergencyInterruption != null) {
      await operatorRepository.addEmergencyInterruption(emergencyInterruption!);
    }

    if (interactionEmergencyInterruption != null) {
      await operatorRepository.addInterruptionEmergencyInterruption(interactionEmergencyInterruption!);
    }

    emergencyInterruption = null;
    interactionEmergencyInterruption = null;
  }

  NavigationInterruption? navigationInterruption;
  InteractionNavigationInterruption? interactionNavigationInterruption;

  Future<void> navigationStartInterruptionTime({required String navigationType, StoreData? storeData, bool isRequestSubmitted = true}) async {
    navigationInterruption = NavigationInterruption(
      navigationType: navigationType.toString(),
      startedInterruptionTime: DateTime.now(),
      storeUuid: storeData?.storeUuid,
      storeName: storeData?.name,
      successfullyNavigated: false,
      isRequestSubmitted: isRequestSubmitted,
      vendorUuid: storeData?.vendorUuid,
    );

    /// For Interaction Table
    interactionNavigationInterruption = InteractionNavigationInterruption(
      navigationType: navigationType.toString(),
      startedInterruptionTime: DateTime.now(),
      storeUuid: storeData?.storeUuid,
      storeName: storeData?.name,
      successfullyNavigated: false,
      isRequestSubmitted: isRequestSubmitted,
      vendorUuid: storeData?.vendorUuid,
    );
    navigationInterruption = (await operatorRepository.addNavigationInterruption(navigationInterruption!)).data;
    interactionNavigationInterruption = (await operatorRepository.addInteractionNavigationInterruption(interactionNavigationInterruption!)).data;
  }

  Future<void> navigationEndInterruptionTime({bool? successfullyNavigated, bool isRequestSubmitted = true}) async {
    navigationInterruption?.endedInterruption = DateTime.now();
    interactionNavigationInterruption?.endedInterruption = DateTime.now();
    navigationInterruption?.successfullyNavigated = successfullyNavigated ?? true;
    interactionNavigationInterruption?.successfullyNavigated = successfullyNavigated ?? true;
    // navigationInterruption?.duration = (isRequestSubmitted && (navigationInterruption?.isRequestSubmitted ?? false))
    //     ? (navigationInterruption?.endedInterruption != null && navigationInterruption?.startedInterruptionTime != null)
    //         ? navigationInterruption!.endedInterruption?.difference(navigationInterruption!.startedInterruptionTime).inSeconds // or inSeconds based on requirement
    //         : 0
    //     : 0;
    navigationInterruption?.isRequestSubmitted = isRequestSubmitted;
    interactionNavigationInterruption?.isRequestSubmitted = isRequestSubmitted;
    if (navigationInterruption != null) {
      await operatorRepository.addNavigationInterruption(navigationInterruption!);
    }

    if (interactionNavigationInterruption != null) {
      await operatorRepository.addInteractionNavigationInterruption(interactionNavigationInterruption!);
    }

    navigationInterruption = null;
    interactionNavigationInterruption = null;
  }

  ChargingInterruption? chargingInterruption;
  InteractionChargingInterruption? interactionChargingInterruption;

  Future<void> chargingStartInterruptionTime(int startCharging) async {
    chargingInterruption = ChargingInterruption(startedInterruptionTime: DateTime.now(), startCharging: startCharging);
    interactionChargingInterruption = InteractionChargingInterruption(startedInterruptionTime: DateTime.now(), startCharging: startCharging);
    chargingInterruption = (await operatorRepository.addChargingInterruption(chargingInterruption!)).data;
    interactionChargingInterruption = (await operatorRepository.addInterruptionChargingInterruption(interactionChargingInterruption!)).data;
  }

  Future<void> chargingEndInterruptionTime(int endCharging) async {
    chargingInterruption?.endedInterruption = DateTime.now();
    interactionChargingInterruption?.endedInterruption = DateTime.now();
    chargingInterruption?.endCharging = endCharging;
    interactionChargingInterruption?.endCharging = endCharging;
    if (chargingInterruption != null) {
      await operatorRepository.addChargingInterruption(chargingInterruption!);
    }

    if (interactionChargingInterruption != null) {
      await operatorRepository.addInterruptionChargingInterruption(interactionChargingInterruption!);
    }
    chargingInterruption = null;
    interactionChargingInterruption = null;
  }

  bool isStoreDialogOpen = false;
}
