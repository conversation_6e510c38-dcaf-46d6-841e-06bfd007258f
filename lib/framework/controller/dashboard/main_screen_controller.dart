// import 'dart:io';
// import 'dart:isolate';
// import 'dart:ui';
//
// import 'package:odigo_display/framework/provider/network/api_end_points.dart';
// import 'package:odigo_display/framework/provider/network/network_exceptions.dart';
// import 'package:odigo_display/framework/repository/robot_meta_data_repository/contract/robot_meta_data_repository.dart';
// import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/get_fully_played_sync_response_model.dart';
// import 'package:odigo_display/framework/utils/extension/string_extension.dart';
// import 'package:odigo_display/framework/utils/helper/objectbox_client.dart';
// import 'package:odigo_display/framework/utils/helper/objectbox_response_model.dart';
// import 'package:odigo_display/framework/utils/ui_state.dart';
// import 'package:odigo_display/ui/utils/const/app_constants.dart';
// import 'package:odigo_display/ui/utils/theme/theme.dart';
// import 'package:odigo_display/ui/utils/widgets/common_dialogs.dart';
// import 'package:chewie/chewie.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_archive/flutter_archive.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:injectable/injectable.dart';
// import 'package:odigo_display/framework/dependency_injection/inject.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:flutter_downloader/flutter_downloader.dart';
// import 'package:video_player/video_player.dart';
//
// final mainScreenController = ChangeNotifierProvider(
//   (ref) => getIt<MainScreenController>(),
// );
//
// @injectable
// class MainScreenController extends ChangeNotifier {
//   RobotMetaDataRepository robotMetaDataRepository;
//
//   MainScreenController(this.robotMetaDataRepository);
//
//   final ReceivePort downloadPort = ReceivePort();
//
//   /// Text Editing Controller
//   TextEditingController passwordCTR = TextEditingController();
//   FocusNode focusNode = FocusNode();
//   GlobalKey<FormState> formKey = GlobalKey<FormState>();
//
//   bool isFrontDisplay = false;
//
//   List<String> downloadingTasksIds = [];
//
//   List<String> downloadingDefaultTaskId = [];
//
//   /// Video Player Controller
//   ChewieController? chewieController;
//
//   TextEditingController searchTxtCTR = TextEditingController();
//
//   /// Video Player Controller
//   VideoPlayerController? videoPlayerController;
//
//   // List<StorePointsOnDestination> storeList = [];
//   // List<StorePointsOnDestination> searchStoresList = [];
//
//   bool? isStoreClosed;
//
//   bool isDataSync = false;
//
//   /// Update Front Display Condition
//   void updateFrontDisplay() {
//     // String? display = Session.getDeviceId();
//     // isFrontDisplay = display == frontDisplayAndroidId;
//     notifyListeners();
//   }
//
//   /// Search Mode
//   bool isSearchModeOn = false;
//
//   /// Settings Mode
//   bool isSettingsModeOn = false;
//
//   bool isAdsRunning = false;
//
//   updateAdsRunning(bool value) {
//     showLog("ads running status $value");
//     isAdsRunning = value;
//     chewieController?.pause();
//     notifyListeners();
//   }
//
//   ///Dispose Controller
//   void disposeController({bool isNotify = false}) {
//     // storeList.clear();
//     chewieController?.dispose();
//     chewieController = null;
//     isFrontDisplay = false;
//     if (isNotify) {
//       notifyListeners();
//     }
//   }
//
//   Future<void> getStoreDetails() async {
//     /// TODO
//     // if (Session.getSyncAPIResponse() != null) {
//     //   String res = Session.getSyncAPIResponse() ?? '';
//     //   if (res != '') {
//     //     GetAssignSyncResponseModel syncResponseModel = getAssignSyncResponseModelFromJson(res);
//     //     storeList.addAll(syncResponseModel.data.target!.storePointsOnDestination!);
//     //     showLog('storeList length ${storeList.length}');
//     //   }
//     // }
//     notifyListeners();
//   }
//
//   /// Get Search Stores
//   // void getSearchStores(String text) {
//   //   showLog('text $text');
//   //   searchStoresList.clear();
//   //   List<StorePointsOnDestination> tempList = [];
//   //
//   //   tempList.addAll(storeList);
//   //   if (text.isNotEmpty) {
//   //     for (final item in tempList) {
//   //       showLog('item store name ${item.name}');
//   //       if (item.name?.toUpperCase().contains(text.toUpperCase()) ?? false) {
//   //         showLog('item store name 1 ${item.name}');
//   //         searchStoresList.add(item);
//   //       }
//   //     }
//   //   } else {
//   //     searchStoresList = tempList;
//   //   }
//   //   showLog('searchStoresList ${searchStoresList.length} ${tempList.length}');
//   //   notifyListeners();
//   // }
//
//   ///For animation of dialog
//   AnimationController? animationController;
//
//   ///For error text in authenticate dialog otp
//   bool isErrorTextVisible = false;
//
//   updateIsErrorTextVisible(bool isErrorTextVisible) {
//     this.isErrorTextVisible = isErrorTextVisible;
//     notifyListeners();
//   }
//
//   ///For authenticate Dialog
//   bool isAuthenticationDialogVisible = false;
//
//   updateIsAuthenticationDialogVisible(bool isAuthenticationDialogVisible) {
//     showLog('isAuthenticationDialogVisibleisAuthenticationDialogVisible $isAuthenticationDialogVisible');
//     // if (chewieController?.isPlaying ?? false) {
//     //   chewieController?.pause();
//     // }
//     //
//     // if (!isAuthenticationDialogVisible) {
//     //   if (chewieController?.isPlaying == false) {
//     //     chewieController?.play();
//     //   }
//     // }
//
//     this.isAuthenticationDialogVisible = isAuthenticationDialogVisible;
//     notifyListeners();
//   }
//
//   /// Update Search View
//   void updateSearchView(bool status) {
//     isSearchModeOn = status;
//     isSettingsModeOn = false;
//     searchTxtCTR.text = '';
//     getSearchStores('');
//     notifyListeners();
//   }
//
//   /// Get Data Locally
//   Future<void> getLocalData() async {
//     objMainSyncDataResponse = await objMainSyncData.getSingle(1);
//     showLog('objMainSyncDataResponse = await objMainSyncData.getSingle(1) ');
//     if (objMainSyncDataResponse?.data != null) {
//       showLog('objMainSyncDataResponse 1 ${objMainSyncDataResponse?.data?.data.target?.adSequences.length}');
//       showLog('objMainSyncDataResponse 2 ${objMainSyncDataResponse?.data?.data.target?.adSequences.first.adDetails.length}');
//       showLog('objMainSyncDataResponse 3 ${objMainSyncDataResponse?.data?.data.target?.adSequences.first.adDetails[3].mediaList?.length}');
//
//       if (!isInternetConnectionOn) {
//         getSyncDataAPIState.success = objMainSyncDataResponse!.data!;
//         await Future.delayed(const Duration(milliseconds: 500), () {
//           getCurrentIndexOfCurrentSlotAndTime();
//         });
//         getSyncDataAPIState.isLoading = false;
//         notifyListeners();
//       }
//     }
//     notifyListeners();
//   }
//
//   /// Maitrik
//   ///
//   ///
//   ///
//   ///
//   ///
//   ///
//   final currentDateTime = DateTime.now();
//   List<AssignAdSequence> timeSlotWiseList = [];
//   String startDestinationTime = '10:00:00';
//   String endDestinationTime = '20:00:00';
//
//   int getCurrentTimeSlotIndex = 0;
//   int getCurrentAdsTimeSlotIndex = 0;
//   int getCurrentAdsMediaIndex = 0;
//
//   /// for hours time slot
//   Duration timeSlotStep = const Duration(minutes: 60);
//
//   /// for ads time slot
//   Duration adsTimeSlotStep = const Duration(seconds: 30);
//
//   bool checkIfOnTheHour(DateTime time) {
//     // Check if the time is exactly on the hour (i.e., minutes and seconds are 0)
//     return time.minute == 0 && time.second == 0;
//   }
//
//   int getRemainingMinutesInHour(DateTime dateTime) {
//     // Calculate the minutes passed since the start of the hour
//     int minutesPassed = dateTime.minute;
//
//     // Calculate the remaining minutes in the hour
//     int remainingMinutes = 60 - minutesPassed;
//
//     return remainingMinutes;
//   }
//
//   Future<void> generateTimeSlotWiseList() async {
//     DateTime startHourTime = getDateFromStringHHMMSS(getSyncDataAPIState.success?.data.target?.destinationDetails?.destinationStartTime ?? '');
//     DateTime endHourTime = getDateFromStringHHMMSS(getSyncDataAPIState.success?.data.target?.destinationDetails?.destinationEndTime ?? '');
//     showLog('checkIfOnTheHour ${checkIfOnTheHour(startHourTime)}');
//     showLog('checkIfOnTheHour ${checkIfOnTheHour(endHourTime)}');
//
//     /// start adding hours slot
//     while (startHourTime.isBefore(endHourTime)) {
//       timeSlotStep = const Duration(minutes: 60);
//       if (!checkIfOnTheHour(startHourTime)) {
//         DateTime currentTime = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, startHourTime.hour, startHourTime.minute);
//         // Calculate the time to the next hour
//         DateTime nextHour = DateTime(currentTime.year, currentTime.month, currentTime.day, currentTime.hour + 1, 0);
//
//         int difference1 = nextHour.difference(currentTime).inMinutes;
//         showLog('difference1  $difference1');
//         timeSlotStep = Duration(minutes: difference1);
//       }
//
//       /// increase time slot steps = 1 hr add
//       DateTime hourIncrement = startHourTime.add(timeSlotStep);
//       if (hourIncrement.isAfter(endHourTime)) {
//         /// get time difference in minute
//
//         if (endHourTime.minute < 60) {
//           hourIncrement = startHourTime.add(Duration(minutes: endHourTime.minute));
//         }
//       }
//
//       String timeSlot = '${DateFormat.Hms().format(startHourTime)} - ${DateFormat.Hms().format(hourIncrement)}';
//
//       /// create a empty object
//       AssignAdSequence adSequence =
//           AssignAdSequence(id: 0, timeSlot: timeSlot, startTime: timeSlot.split(timeSlotSplit).first.trim(), endTime: timeSlot.split(timeSlotSplit).last.trim());
//
//       /// generate hours wise ads time slot and added in list
//       adSequence.adDetails.addAll(generateHoursWiseAdsTimeSlot(startHourTime, hourIncrement));
//       timeSlotWiseList.add(adSequence);
//
//       showLog("Hours wise ads time slot length : ${adSequence.adDetails.length}");
//
//       startHourTime = hourIncrement;
//     }
//
//     for (final slots in timeSlotWiseList) {
//       showLog('check slots Start ${slots.startTime} end ${slots.endTime} ${slots.adDetails.length}');
//     }
//
//     // showLog("Hours Time Slot Length : ${timeSlotWiseList.length}");
//   }
//
//   /// generate hours wise ads time slot
//   List<AssignAdDetail> generateHoursWiseAdsTimeSlot(DateTime startHourTime, DateTime endHourTime) {
//     List<AssignAdDetail> adDetailList = [];
//
//     /// generate time slot for ads
//     while (startHourTime.isBefore(endHourTime)) {
//       DateTime timeIncrement = startHourTime.add(adsTimeSlotStep);
//       AssignAdDetail adDetail = AssignAdDetail(timeSlot: "${DateFormat.Hms().format(startHourTime)} $timeSlotSplit ${DateFormat.Hms().format(timeIncrement)}");
//       adDetailList.add(adDetail);
//       startHourTime = timeIncrement;
//     }
//     return adDetailList;
//   }
//
//   /// get current index of current slot
//   void getCurrentIndexOfCurrentSlotAndTime({bool isFromAPI = false}) {
//     isAdsRunning = true;
//     notifyListeners();
//     if (isStoreClosed == false) {
//       /// getting current timeslot index
//       bool isInBetween = false;
//       showLog('getSyncDataAPIState.success?.data.target?.adSequences.length ${getSyncDataAPIState.success?.data.target?.adSequences.length}');
//       for (int i = 0; i < (getSyncDataAPIState.success?.data.target?.adSequences.length ?? 0); i++) {
//         /// get time slot of element
//         AssignAdSequence? adSequence = getSyncDataAPIState.success?.data.target?.adSequences[i];
//         showLog('adSequence!.timeSlot ${adSequence!.timeSlot}');
//         List<String>? timeSlotList = getSplitTimeSlot(adSequence.timeSlot!);
//         if (timeSlotList != null) {
//           DateTime starDateTime = getDateFromStringHHMMSS(timeSlotList[0]);
//           DateTime endDateTime = getDateFromStringHHMMSS(timeSlotList[1]);
//
//           /// split and get start and end time of slot
//           TimeOfDay startTime = TimeOfDay(hour: starDateTime.hour, minute: starDateTime.minute);
//           TimeOfDay endTime = TimeOfDay(hour: endDateTime.hour, minute: endDateTime.minute);
//
//           /// check current time is in slot
//           isInBetween = isInBetweenTimeSlot(startTime, endTime);
//           if (isInBetween) {
//             getCurrentTimeSlotIndex = i;
//             notifyListeners();
//             showLog("Current timeSlot index $i Current timeSlot ${getSyncDataAPIState.success?.data.target?.adSequences[i].timeSlot}");
//             break;
//           } else {
//             // closeStore();
//             notifyListeners();
//           }
//         }
//       }
//       showLog("Current Time Slot $isInBetween");
//       if (isInBetween) {
//         getNextAdsTimeSlotIndex();
//       } else {
//         notifyListeners();
//       }
//       if (isFromAPI && objMainSyncDataResponse?.data?.data.target != null) {
//         getSyncDataAPIState.isLoading = false;
//       }
//       notifyListeners();
//     }
//   }
//
//   startStoreAdsShowingScheduler() {
//     if (isStoreClosed == false) {
//       getCurrentIndexOfCurrentSlotAndTime();
//       // getCurrentAdsTimeSlotIndex = 0;
//       // getCurrentAdsTimeSlotIndex = 0;
//       // getCurrentAdsMediaIndex = 0;
//       showingMediaContent();
//       notifyListeners();
//
//       // if (getSyncDataAPIState.success?.data.target?.destinationDetails != null) {
//       //   DateTime str = DateFormat(dateFormatHHMMSS).parse(getSyncDataAPIState.success?.data.target?.destinationDetails?.destinationStartTime ?? '');
//       //   final scheduler = TimeScheduler();
//       //   DateTime dateTime = DateTime.now();
//       //   DateTime startTime = DateTime(dateTime.year, dateTime.month, dateTime.day, str.hour, str.minute, str.second).subtract(Duration(seconds: 2));
//       //   if (!dateTime.isAfter(startTime)) {
//       //     scheduler.run(() {
//       //
//       //     }, DateTime(dateTime.year, dateTime.month, dateTime.day, str.hour, str.minute, str.second).subtract(Duration(seconds: 2)));
//       //   }
//       // }
//     }
//   }
//
//   /// check current time with time slot
//   bool isInBetweenTimeSlot(TimeOfDay start, TimeOfDay end) {
//     final now = DateTime.now();
//     final startDateTime = DateTime(now.year, now.month, now.day, start.hour, start.minute);
//     final endDateTime = DateTime(now.year, now.month, now.day, end.hour, end.minute);
//     final currentDateTime = DateTime(now.year, now.month, now.day, now.hour, now.minute, now.second);
//     return currentDateTime.isAfter(startDateTime) && currentDateTime.isBefore(endDateTime);
//   }
//
//   /// get next time slot index
//   void getNextAdsTimeSlotIndex() {
//     for (int j = 0; j < (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails.length ?? 0); j++) {
//       showLog('test check played entry here ${getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[j].timeSlot}');
//       List<String>? timeSlotList = getSplitTimeSlot(getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[j].timeSlot! ?? '');
//       showLog('timeSlotList check here $timeSlotList');
//       if (timeSlotList != null) {
//         DateTime starDateTime = getDateFromStringHHMMSS(timeSlotList[0]);
//         // DateTime endDateTime = getDateFromStringHHMMSS(timeSlotList[1].replaceAll(" ", ''));
//
//         /// get start time of start time of ads time slot
//         DateTime adsStartTime = DateTime(currentDateTime.year, currentDateTime.month, currentDateTime.day, starDateTime.hour, starDateTime.minute, starDateTime.second);
//         final checkCurrentDateTime = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, DateTime.now().hour, DateTime.now().minute, DateTime.now().second);
//         showLog('startTimestartTimestartTimestartTime $adsStartTime');
//         showLog('checkDateTimecheckDateTimecheckDateTime $adsStartTime');
//
//         /// check current time with ads time slot is after
//         bool isAdsNextTimeSlotAvailable = (adsStartTime.isAfter(checkCurrentDateTime) || adsStartTime.isAtSameMomentAs(checkCurrentDateTime));
//
//         if (isAdsNextTimeSlotAvailable) {
//           showLog("Current Ads Time Slot of index $j and time slot ${getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[j].timeSlot}");
//           getCurrentAdsTimeSlotIndex = j;
//           getTimeDifference(checkCurrentDateTime, adsStartTime);
//           break;
//         }
//       } else {
//         notifyListeners();
//       }
//     }
//     // showAdsContent();
//     // startShowingAds();
//   }
//
//   /// get difference between two times
//   getTimeDifference(DateTime currentTime, DateTime startTime) {
//     if (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?.isNotEmpty ?? false) {
//       var diff = startTime.difference(currentTime);
//       showLog("Get Time Difference of $currentTime and $startTime difference of seconds ${diff.inSeconds}");
//       if (diff.inSeconds > 0) {
//         showDefaultAds(diff.inSeconds);
//       }
//       Future.delayed(Duration(seconds: diff.inSeconds), () {
//         showLog('=========================================== Showing Ads =========================================== test ${diff.inSeconds}');
//         showAdsContent();
//         notifyListeners();
//       });
//       return diff;
//     }
//   }
//
//   updateTimeSlotIndex() {
//     if (isStoreClosed == false && isAdsRunning) {
//       showLog("called startShowingAds ");
//       if (getCurrentAdsTimeSlotIndex < (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails.length ?? 0) - 1) {
//         showLog("updateTimeSlotIndex if called $getCurrentAdsTimeSlotIndex");
//         getCurrentAdsTimeSlotIndex++;
//         getCurrentAdsMediaIndex = 0;
//
//         showAdsContent();
//         notifyListeners();
//       } else {
//         if (getCurrentTimeSlotIndex < (getSyncDataAPIState.success?.data.target?.adSequences.length ?? 0) - 1) {
//           getCurrentAdsTimeSlotIndex = 0;
//           getCurrentAdsMediaIndex = 0;
//           getCurrentTimeSlotIndex++;
//           showAdsContent();
//           notifyListeners();
//         }
//       }
//     }
//   }
//
//   void showAdsContent() {
//     showLog("=========================================== Showing Ads ===========================================");
//     showLog("Current Time Slot ${getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].timeSlot} index $getCurrentAdsTimeSlotIndex");
//     showLog("Ads Time Slot ${getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].timeSlot}");
//     showLog("=========================================== Showing Ads ===========================================");
//     showingMediaContent();
//   }
//
//   void closeStore() {
//     chewieController = null;
//     videoPlayerController = null;
//     chewieController?.dispose();
//     videoPlayerController?.dispose();
//
//     notifyListeners();
//     // if (getSyncDataAPIState.success?.data.target?.destinationDetails != null) {
//     //   final scheduler = TimeScheduler();
//     //
//     //   DateTime str = DateFormat(dateFormatHHMMSS).parse(getSyncDataAPIState.success?.data.target?.destinationDetails?.destinationEndTime ?? '');
//     //
//     //   // run the function on January 1st at 17:75
//     //   DateTime dateTime = DateTime.now();
//     //   DateTime currentStoreTime = DateTime(dateTime.year, dateTime.month, dateTime.day, str.hour, str.minute);
//     //
//     //   if (dateTime.isAfter(currentStoreTime)) {
//     //     scheduler.run(() {
//     //       // startDisplayAds();
//     //
//     //     }, DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour, dateTime.minute));
//     //   }
//     //
//     //   notifyListeners();
//     //   // startDisplayAds();
//     // }
//   }
//
//   void showingMediaContent() {
//     if (isAdsRunning) {
//       getSyncDataAPIState.isLoading = false;
//       notifyListeners();
//       showLog("showingMediaContent called");
//
//       /// check media list
//       double displayCountSeconds = 0;
//       int mediaListCount = getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?.length ?? 0;
//       displayCountSeconds = (30 / mediaListCount);
//
//       if (getCurrentAdsMediaIndex < (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList!.length ?? 0)) {
//         showLog("======================= Showing ads Media =======================");
//         showLog("Media index $getCurrentAdsMediaIndex && Duration ${displayCountSeconds.floor()}");
//         showLog(
//             "====== Media Name ${getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?[getCurrentAdsMediaIndex]} =======");
//         showLog("======================= Showing ads Media =======================");
//         if (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaType == "image") {
//           loadImage(getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?[getCurrentAdsMediaIndex] ?? '');
//         } else {
//           loadVideo(getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?[getCurrentAdsMediaIndex] ?? '');
//         }
//         showLog('displayCountSeconds.floor() ${displayCountSeconds.floor()}');
//         Future.delayed(Duration(seconds: displayCountSeconds.floor()), () {
//           if (isAdsRunning) {
//             getCurrentAdsMediaIndex++;
//             showingMediaContent();
//           }
//           notifyListeners();
//         });
//       } else {
//         final data = getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex];
//         FullyPlayedAdDetail fullyPlayedAdDetail = FullyPlayedAdDetail()
//           ..timeSlot = '${data?.timeSlot}'
//           ..name = data?.name
//           ..mediaType = data?.mediaType
//           ..mediaList = data?.mediaList
//           ..logo = data?.logo
//           ..entityType = data?.entityType
//           ..downloadZipId = data?.downloadZipId
//           ..description = data?.description
//           ..date = data?.date
//           ..entityId = data?.entityId
//           ..adUrl = data?.adUrl
//           ..chargesPerAd = data?.chargesPerAd
//           ..walletBalance = data?.walletBalance
//           ..udf1 = data?.udf1
//           ..udf2 = data?.udf2
//           ..udf3 = data?.udf3
//           ..udid = data?.uuid
//           ..localPath = data?.localPath
//           ..duration = data?.duration
//           ..startTime = DateTime.now().subtract(const Duration(seconds: 30)).toUtc().toString().getCustomDateTimeFromUTC(dateFormatHHMMSS)
//           ..endTime = DateTime.now().toUtc().toString().getCustomDateTimeFromUTC(dateFormatHHMMSS)
//           ..isReplayed = false
//           ..isInterruptedAd = false
//           ..isInterruptedAdId = null
//           ..isDataSync = false
//           ..date = DateTime.now().toString().split(' ').first
//           ..isDestinationDefaultAd = data?.isDestinationDefaultAd;
//         storedFullyPlayedEntries(fullyPlayedAdDetail);
//         updateTimeSlotIndex();
//       }
//     }
//   }
//
//   void showDefaultAds(int seconds) {
//     int id = 0;
//     showLog("getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList");
//     showLog("${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.length}");
//     if (getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.isNotEmpty ?? false) {
//       if (getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaType == "image") {
//         loadImage(getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.first ?? '');
//       } else {
//         loadVideo(getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.first ?? '');
//       }
//     }
//     // if (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?.isNotEmpty ?? false) {
//     //   // double index = (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?.length ?? 0) / seconds;
//     //   showLog("======================= Showing Default Ads ======================= $seconds");
//     //   if (getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaType == "image") {
//     //     loadImage(getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?[id] ?? '');
//     //   } else {
//     //     loadVideo(getSyncDataAPIState.success?.data.target?.adSequences[getCurrentTimeSlotIndex].adDetails[getCurrentAdsTimeSlotIndex].mediaList?[id] ?? '');
//     //   }
//     //   // if (index.floor() <= 30) {
//     //   //   Future.delayed(Duration(seconds: index.floor()), () {
//     //   //     id++;
//     //   //     showDefaultAds(index.toInt());
//     //   //   });
//     //   // }
//     // }
//
//     notifyListeners();
//   }
//
//   String mediaURL = '';
//
//   loadImage(String imageURL) {
//     showLog("image path $mediaURL");
//     mediaURL = imageURL;
//     notifyListeners();
//   }
//
//   void loadVideo(String videoURL) async {
//     videoPlayerController?.dispose();
//     chewieController?.dispose();
//     chewieController = null;
//     videoPlayerController = null;
//     showLog('Load Video Gets Called ${videoURL}');
//
//     videoPlayerController = VideoPlayerController.file(File(videoURL));
//     showLog('videoPlayerController url ${videoPlayerController?.value.isInitialized}');
//     await videoPlayerController?.initialize().then(
//       (value) {
//         notifyListeners();
//       },
//     );
//     showLog('videoPlayerController check  ${videoPlayerController?.value.isInitialized}');
//     showLog('videoPlayerController value  ${videoPlayerController?.dataSource}');
//     chewieController = ChewieController(
//       videoPlayerController: videoPlayerController!,
//       autoPlay: true,
//       looping: false,
//       customControls: const Offstage(),
//       allowFullScreen: true,
//       fullScreenByDefault: false,
//       // startAt: Duration(seconds: (videoPlayerController?.value.duration.inSeconds ?? 0) - 2),
//     );
//     showLog('chewieController url ${chewieController?.videoPlayerController.value}');
//
//     notifyListeners();
//
//     /// Video Player Controller
//     videoPlayerController?.setLooping(false);
//     videoPlayerController?.addListener(() {
//       if (videoPlayerController?.value.duration == videoPlayerController?.value.position) {}
//     });
//     notifyListeners();
//   }
//
//   ///
//   ///
//   ///
//   ///
//   ///
//   ///
//
//   /// Update Widget
//   void updateWidget() {
//     notifyListeners();
//   }
//
//   /// On Init Called
//   void initCall(BuildContext context, WidgetRef ref) {
//     /// Binding Isolates
//     bindBackgroundIsolate(context, ref);
//
//     /// Register callback
//     FlutterDownloader.registerCallback(downloadCallback, step: 1);
//   }
//
//   /// Binding Isolates
//   void bindBackgroundIsolate(BuildContext context, WidgetRef ref) {
//     final isSuccess = IsolateNameServer.registerPortWithName(
//       downloadPort.sendPort,
//       'downloader_send_port',
//     );
//     if (!isSuccess) {
//       unbindBackgroundIsolate();
//       bindBackgroundIsolate(context, ref);
//       return;
//     }
//   }
//
//   void unbindBackgroundIsolate() {
//     IsolateNameServer.removePortNameMapping('downloader_send_port');
//   }
//
//   @pragma('vm:entry-point')
//   static void downloadCallback(
//     String id,
//     int status,
//     int progress,
//   ) {
//     showLog(
//       'Callback on background isolate: '
//       'task ($id) is in status ($status) and process ($progress)',
//     );
//
//     IsolateNameServer.lookupPortByName('downloader_send_port')?.send([id, status, progress]);
//   }
//
//   ///-------------------------------- API --------------------------------///
//
//   UIState<GetAssignSyncResponseModel> getSyncDataAPIState = UIState<GetAssignSyncResponseModel>();
//   List<AssignAdSequence> mainAdSequenceList = [];
//
//   /// Object Box Store Table
//   ObjectBoxClient<GetAssignSyncResponseModel> objMainSyncData = ObjectBoxClient<GetAssignSyncResponseModel>();
//   ObjectBoxClient<AssignSyncData> objSyncData = ObjectBoxClient<AssignSyncData>();
//   ObjectBoxClient<AssignAdSequence> objAdSequence = ObjectBoxClient<AssignAdSequence>();
//   ObjectBoxClient<AssignAdDetail> objAdDetails = ObjectBoxClient<AssignAdDetail>();
//
//   /// Fully Played Ad Obj
//   ObjectBoxClient<FullyPlayedAdDetail> objFullyPlayedAdDetail = ObjectBoxClient<FullyPlayedAdDetail>();
//
//   /// Object Box Response
//   ObjectBoxResponseModel<GetAssignSyncResponseModel?>? objMainSyncDataResponse;
//
//   ObjectBoxResponseModel<AssignSyncData?>? objSyncDataRes;
//
//   ObjectBoxResponseModel<List<AssignAdSequence>?>? objAdSequenceRes;
//
//   ObjectBoxResponseModel<AssignAdDetail?>? objAdDetailsRes;
//
//   /// Fully played response
//   ObjectBoxResponseModel<List<FullyPlayedAdDetail>?>? objFullyPlayedAdDetailsResponse;
//
//   List<AssignAdSequence> mergeLists(List<AssignAdSequence> list1, List<AssignAdSequence> list2) {
//     List<AssignAdSequence> list3 = [];
//
//     for (var item1 in list1) {
//       List<AssignAdDetail> mergedData = [];
//
//       /// Find overlapping intervals in list2
//       for (var item2 in list2) {
//         if (isOverlapping(item1, item2)) {
//           showLog('check list 2 for here ${item2.adDetails.length} ${item1.adDetails.length} ${item2.adDetails.first.uuid}');
//           List<AssignAdDetail> localList = [];
//
//           for (int i = 0; i < item1.adDetails.length; i++) {
//             if (i < item2.adDetails.length) {
//               localList.add(AssignAdDetail()
//                 ..uuid = item2.adDetails[i].uuid
//                 ..timeSlot = item1.adDetails[i].timeSlot
//                 ..duration = item2.adDetails[i].duration
//                 ..adUrl = item2.adDetails[i].adUrl
//                 ..chargesPerAd = item2.adDetails[i].chargesPerAd
//                 ..localPath = item2.adDetails[i].localPath
//                 ..udf1 = item2.adDetails[i].udf1
//                 ..udf2 = item2.adDetails[i].udf2
//                 ..udf3 = item2.adDetails[i].udf3
//                 ..date = item2.adDetails[i].date
//                 ..entityId = item2.adDetails[i].entityId
//                 ..entityType = item2.adDetails[i].entityType
//                 ..walletBalance = item2.adDetails[i].walletBalance
//                 ..description = item2.adDetails[i].description
//                 ..downloadZipId = item2.adDetails[i].downloadZipId
//                 ..logo = item2.adDetails[i].logo
//                 ..mediaList = item2.adDetails[i].mediaList
//                 ..mediaType = item2.adDetails[i].mediaType
//                 ..name = item2.adDetails[i].name
//                 ..isDestinationDefaultAd = false);
//             } else {
//               localList.add(AssignAdDetail()
//                 ..uuid = item1.adDetails[i].uuid
//                 ..timeSlot = item1.adDetails[i].timeSlot
//                 ..duration = item1.adDetails[i].duration
//                 ..adUrl = item1.adDetails[i].adUrl
//                 ..chargesPerAd = item1.adDetails[i].chargesPerAd
//                 ..localPath = item1.adDetails[i].localPath
//                 ..udf1 = item1.adDetails[i].udf1
//                 ..udf2 = item1.adDetails[i].udf2
//                 ..udf3 = item1.adDetails[i].udf3
//                 ..date = item1.adDetails[i].date
//                 ..entityId = item1.adDetails[i].entityId
//                 ..entityType = item1.adDetails[i].entityType
//                 ..walletBalance = item1.adDetails[i].walletBalance
//                 ..description = item1.adDetails[i].description
//                 ..downloadZipId = item1.adDetails[i].downloadZipId
//                 ..logo = item1.adDetails[i].logo
//                 ..mediaList = item1.adDetails[i].mediaList
//                 ..mediaType = item1.adDetails[i].mediaType
//                 ..name = item1.adDetails[i].name
//                 ..isDestinationDefaultAd = false);
//             }
//           }
//           mergedData.addAll(localList);
//         }
//       }
//
//       /// Add list1's data to mergedData
//       mergedData.addAll(item1.adDetails);
//
//       /// Remove duplicates while preserving order
//       mergedData = _removeDuplicatesAndLimit(mergedData, item1.adDetails.length);
//
//       // for (var slot in mergedData) {
//       //   showLog('Start: ${slot.timeSlot}, End: ${slot.uuid} ');
//       // for (var slot in slot.adDetails) {
//       //   showLog('Time slot: ${slot.timeSlot}, UUID  ${slot.uuid} ');
//       // }
//       // }
//
//       list3.add(AssignAdSequence(startTime: item1.startTime, endTime: item1.endTime, timeSlot: '${item1.startTime} - ${item1.endTime}')..adDetails.addAll(mergedData));
//     }
//     return list3;
//   }
//
//   bool isOverlapping(AssignAdSequence item1, AssignAdSequence item2) {
//     return item1.startTime!.compareTo(item2.endTime ?? '') < 0 && item1.endTime!.compareTo(item2.startTime ?? '') > 0;
//   }
//
//   List<AssignAdDetail> _removeDuplicatesAndLimit(List<AssignAdDetail> data, int maxLength) {
//     Set<AssignAdDetail> seen = {};
//     List<AssignAdDetail> result = [];
//
//     for (var item in data) {
//       if (!seen.contains(item)) {
//         seen.add(item);
//         result.add(item);
//         if (result.length == maxLength) {
//           break;
//         }
//       }
//     }
//
//     return result;
//   }
//
//   Future<void> getSyncDataLocally(BuildContext context) async {
//     getSyncDataAPIState.isLoading = true;
//     getSyncDataAPIState.success = null;
//     downloadingTasksIds.clear();
//     mainAdSequenceList.clear();
//     notifyListeners();
//
//     String data = await DefaultAssetBundle.of(context).loadString(Assets.json.dummyJson);
//     getSyncDataAPIState.success = getAssignSyncResponseModelFromJson(data);
//
//     if (getSyncDataAPIState.success?.status == ApiEndPoints.apiStatus_200) {
//       /// Storing Data Hive Provider
//
//       /// Creating Time Slot on basis of Destination time
//       ///
//       ///
//       ///
//       await generateTimeSlotWiseList();
//       Future.delayed(const Duration(seconds: 2), () {
//         /// Sorting Server List Using Start Time
//         getSyncDataAPIState.success?.data.target?.adSequences.sort(
//           (a, b) => a.startTime!.compareTo(b.startTime ?? ''),
//         );
//
//         /// Creating new slots
//         List<AssignAdSequence> newSlots = [];
//         newSlots.addAll(timeSlotWiseList);
//
//         /// Fill Time slot wise list with default ads
//         timeSlotWiseList.clear();
//
//         /// Fill Time slot wise list with default ads
//         for (AssignAdSequence slotNew in newSlots) {
//           List<AssignAdDetail> assignDetailsList = [];
//           for (AssignAdDetail adDetail in slotNew.adDetails) {
//             assignDetailsList.add(
//               AssignAdDetail()
//                 ..uuid = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.uuid
//                 ..timeSlot = adDetail.timeSlot
//                 ..duration = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.duration
//                 ..adUrl = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.adUrl
//                 ..chargesPerAd = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.chargesPerAd
//                 ..localPath = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.localPath
//                 ..udf1 = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.udf1
//                 ..udf2 = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.udf2
//                 ..udf3 = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.udf3
//                 ..date = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.date
//                 ..entityId = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.entityId
//                 ..entityType = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.entityType
//                 ..walletBalance = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.walletBalance
//                 ..description = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.description
//                 ..downloadZipId = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.downloadZipId
//                 ..logo = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.logo
//                 ..mediaList = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.mediaList
//                 ..mediaType = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.mediaType
//                 ..name = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.name
//                 ..isDestinationDefaultAd = true,
//             );
//           }
//
//           timeSlotWiseList.add(
//             AssignAdSequence(id: 0, timeSlot: slotNew.timeSlot, startTime: slotNew.startTime, endTime: slotNew.endTime)..adDetails.addAll(assignDetailsList),
//           );
//         }
//
//         /// Merge data for both server and default list
//         List<AssignAdSequence> finalSequenceList = mergeLists(timeSlotWiseList, getSyncDataAPIState.success!.data.target!.adSequences);
//         showLog('finalSequenceList check here ${finalSequenceList.length}');
//         // for (var slot in finalSequenceList) {
//         //   showLog('Start: ${slot.startTime}, End: ${slot.endTime} ${slot.adDetails.length}');
//         //   for (var slot in slot.adDetails) {
//         //     showLog('Time slot: ${slot.timeSlot}, UUID  ${slot.uuid} ');
//         //   }
//         // }
//
//         getSyncDataAPIState.success?.data.target?.adSequences.clear();
//         getSyncDataAPIState.success?.data.target?.adSequences.addAll(finalSequenceList);
//
//         showLog('getSyncDataAPIState.success!.data.target!.adSequences length ${getSyncDataAPIState.success!.data.target!.adSequences.first.adDetails.length}');
//         // for (var slot in getSyncDataAPIState.success!.data.target!.adSequences) {
//         //   showLog('Start: ${slot.startTime}, End: ${slot.endTime} ${slot.adDetails.length}');
//         //   for (var slot in slot.adDetails) {
//         //     showLog('Time slot: ${slot.timeSlot}, UUID  ${slot.uuid} ');
//         //   }
//         // }
//
//         Future.delayed(const Duration(seconds: 2), () async {
//           /// -------------------- Downloading -------------------- ///
//           ///                                                       ///
//           ///                                                       ///
//           /// ----------------------------------------------------- ///
//
//           Directory? savedDownloadAdsDirectory = await getDownloadsDirectory();
//
//           String downloadPath = savedDownloadAdsDirectory?.path ?? '';
//
//           /// Check Main Directory
//           if (savedDownloadAdsDirectory?.existsSync() == false) {
//             await savedDownloadAdsDirectory?.create();
//             showLog('savedDownloadAdsDirectory path  ${savedDownloadAdsDirectory?.path}');
//           } else {
//             showLog('savedDir already exist   test test ${savedDownloadAdsDirectory?.path}');
//           }
//
//           Directory? savedDownloadDirectoryUsingAdsId;
//           Directory? zipDirectoryForDownloading;
//           if (getSyncDataAPIState.success!.data.target != null) {
//             if (getSyncDataAPIState.success!.data.target?.adSequences != null) {
//               mainAdSequenceList.addAll(getSyncDataAPIState.success!.data.target!.adSequences);
//             }
//           }
//
//           /// CHECKING DEFAULT AD HERE
//           ///
//           if (getSyncDataAPIState.success?.data.target?.destinationDetails != null) {
//             if (getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.isNotEmpty ?? false) {
//               /// Creating a directory UUID wise
//               savedDownloadDirectoryUsingAdsId = Directory('$downloadPath/${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.uuid}');
//               if (!(savedDownloadDirectoryUsingAdsId.existsSync())) {
//                 await savedDownloadDirectoryUsingAdsId.create();
//                 showLog('savedDownloadDirectoryUsingAdsId path  ${savedDownloadDirectoryUsingAdsId.path}');
//                 zipDirectoryForDownloading = Directory('${savedDownloadDirectoryUsingAdsId.path}/zip');
//                 if (!(zipDirectoryForDownloading.existsSync())) {
//                   await zipDirectoryForDownloading.create();
//                   showLog('zipDirectoryForDownloading path  ${zipDirectoryForDownloading.path}');
//                 } else {
//                   showLog('zipDirectoryForDownloading already exist  ${zipDirectoryForDownloading.path}');
//                 }
//                 String? fileName = (getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.adUrl ?? '').contains('?subDir')
//                     ? getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.adUrl?.split('?subDir').first.split('/').last
//                     : '';
//                 showLog('fileName check $fileName');
//
//                 /// Downloading Zip based on UUID
//                 getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.downloadZipId = await FlutterDownloader.enqueue(
//                   url: getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.adUrl ?? '',
//                   headers: {},
//                   // optional: header send with url (auth token etc)
//                   savedDir: zipDirectoryForDownloading.path,
//                   showNotification: true,
//                   openFileFromNotification: false,
//                   fileName: fileName,
//                 );
//                 getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.localPath = '${zipDirectoryForDownloading.path}/$fileName';
//                 downloadingDefaultTaskId.add(
//                     '${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.uuid ?? ''}_${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.downloadZipId ?? ''}');
//                 showLog('file local task id ${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.downloadZipId}');
//               } else {
//                 bool isInDownloadingProcess = false;
//                 for (final c in downloadingDefaultTaskId) {
//                   if (c.split('_').first == getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.uuid) {
//                     isInDownloadingProcess = true;
//                   }
//                 }
//
//                 if (!isInDownloadingProcess) {
//                   getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.clear();
//                   final mediaDir =
//                       Directory('${savedDownloadDirectoryUsingAdsId.path}/${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaType}');
//                   final files = mediaDir.listSync().whereType<File>().toList();
//                   getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.addAll(files.map((file) => file.path));
//                 }
//
//                 showLog('savedDir Default check already exist  ${savedDownloadDirectoryUsingAdsId.path}');
//               }
//             }
//           }
//
//           /// CHECKING OTHER ADS HERE
//           for (AssignAdSequence adSequenceModel in mainAdSequenceList) {
//             /// AD DETAILS
//             for (int i = 0; i < adSequenceModel.adDetails.length; i++) {
//               /// Creating a directory UUID wise
//               ///
//               savedDownloadDirectoryUsingAdsId = Directory('$downloadPath/${adSequenceModel.adDetails[i].uuid}');
//               if (!(savedDownloadDirectoryUsingAdsId.existsSync())) {
//                 await savedDownloadDirectoryUsingAdsId.create();
//                 showLog('UUID DIRECTORY PATH  ${savedDownloadDirectoryUsingAdsId.path}');
//                 zipDirectoryForDownloading = Directory('${savedDownloadDirectoryUsingAdsId.path}/zip');
//                 if (!(zipDirectoryForDownloading.existsSync())) {
//                   await zipDirectoryForDownloading.create();
//                   showLog('zipDirectoryForDownloading path  ${zipDirectoryForDownloading.path}');
//                 } else {
//                   showLog('zipDirectoryForDownloading already exist  ${zipDirectoryForDownloading.path}');
//                 }
//                 String? fileName = (adSequenceModel.adDetails[i].adUrl ?? '').contains('?subDir') ? adSequenceModel.adDetails[i].adUrl?.split('?subDir').first.split('/').last : '';
//                 showLog('fileName check $fileName');
//
//                 if (adSequenceModel.adDetails[i].mediaList?.isEmpty ?? false) {
//                   /// Downloading Zip based on UUID
//                   adSequenceModel.adDetails[i].downloadZipId = await FlutterDownloader.enqueue(
//                     url: adSequenceModel.adDetails[i].adUrl ?? '',
//                     headers: {},
//                     // optional: header send with url (auth token etc)
//                     savedDir: zipDirectoryForDownloading.path,
//                     showNotification: true,
//                     openFileFromNotification: false,
//                     fileName: fileName,
//                   );
//
//                   adSequenceModel.adDetails[i].localPath = '${zipDirectoryForDownloading.path}/$fileName';
//                   downloadingTasksIds.add('${adSequenceModel.adDetails[i].uuid}_${adSequenceModel.adDetails[i].downloadZipId ?? ''}');
//                   showLog('file local task id ${adSequenceModel.adDetails[i].downloadZipId}');
//                 }
//               } else {
//                 bool isInDownloadingProcess = false;
//
//                 if (downloadingTasksIds.isNotEmpty) {
//                   for (final c in downloadingTasksIds) {
//                     if (c.split('_').first == adSequenceModel.adDetails[i].uuid) {
//                       isInDownloadingProcess = true;
//                     }
//                   }
//                 }
//                 if (!isInDownloadingProcess) {
//                   if (downloadingTasksIds.isEmpty && downloadingDefaultTaskId.isEmpty) {
//                     adSequenceModel.adDetails[i].mediaList?.clear();
//                     final mediaDir = Directory('${savedDownloadDirectoryUsingAdsId.path}/${adSequenceModel.adDetails[i].mediaType}');
//                     if (mediaDir.existsSync()) {
//                       final files = mediaDir.listSync().whereType<File>().toList();
//
//                       adSequenceModel.adDetails[i].mediaList?.addAll(files.map((file) => file.path));
//                     }
//                   }
//                 }
//               }
//             }
//           }
//
//           await Future.delayed(const Duration(seconds: 3), () {
//             if (downloadingTasksIds.isEmpty && downloadingDefaultTaskId.isEmpty) {
//               bool? isDownloadingStatus = false;
//               // if (Session.getIsDownloadingStatus() != null) {
//               //   isDownloadingStatus = Session.getIsDownloadingStatus();
//               // }
//               Future.delayed(const Duration(milliseconds: 500), () {
//                 if (isDownloadingStatus != null && isDownloadingStatus == false) {
//                   getCurrentIndexOfCurrentSlotAndTime(isFromAPI: true);
//                 }
//               });
//             }
//           });
//         });
//       });
//     }
//   }
//
//   Future<void> getSyncDataAPI(BuildContext context, WidgetRef ref) async {
//     getSyncDataAPIState.isLoading = true;
//     getSyncDataAPIState.success = null;
//     downloadingTasksIds.clear();
//     mainAdSequenceList.clear();
//     notifyListeners();
//
//     final result = await robotMetaDataRepository.syncAPI();
//
//     result.when(success: (data) async {
//       getSyncDataAPIState.success = data;
//
//       if (getSyncDataAPIState.success?.status == ApiEndPoints.apiStatus_200) {
//         /// Storing Data Hive Provider
//
//         /// Creating Time Slot on basis of Destination time
//         ///
//         ///
//         ///
//         await generateTimeSlotWiseList();
//         Future.delayed(const Duration(seconds: 2), () {
//           /// Sorting Server List Using Start Time
//           getSyncDataAPIState.success?.data.target?.adSequences.sort(
//             (a, b) => a.startTime!.compareTo(b.startTime ?? ''),
//           );
//
//           /// Creating new slots
//           List<AssignAdSequence> newSlots = [];
//           newSlots.addAll(timeSlotWiseList);
//
//           /// Fill Time slot wise list with default ads
//           timeSlotWiseList.clear();
//
//           /// Fill Time slot wise list with default ads
//           for (AssignAdSequence slotNew in newSlots) {
//             List<AssignAdDetail> assignDetailsList = [];
//             for (AssignAdDetail adDetail in slotNew.adDetails) {
//               assignDetailsList.add(
//                 AssignAdDetail()
//                   ..uuid = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.uuid
//                   ..timeSlot = adDetail.timeSlot
//                   ..duration = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.duration
//                   ..adUrl = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.adUrl
//                   ..chargesPerAd = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.chargesPerAd
//                   ..localPath = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.localPath
//                   ..udf1 = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.udf1
//                   ..udf2 = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.udf2
//                   ..udf3 = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.udf3
//                   ..date = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.date
//                   ..entityId = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.entityId
//                   ..entityType = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.entityType
//                   ..walletBalance = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.walletBalance
//                   ..description = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.description
//                   ..downloadZipId = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.downloadZipId
//                   ..logo = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.logo
//                   ..mediaList = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.mediaList
//                   ..mediaType = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.mediaType
//                   ..name = getSyncDataAPIState.success!.data.target!.destinationDetails!.defaultAds!.first.name
//                   ..isDestinationDefaultAd = true,
//               );
//             }
//
//             timeSlotWiseList.add(
//               AssignAdSequence(id: 0, timeSlot: slotNew.timeSlot, startTime: slotNew.startTime, endTime: slotNew.endTime)..adDetails.addAll(assignDetailsList),
//             );
//           }
//
//           /// Merge data for both server and default list
//           List<AssignAdSequence> finalSequenceList = mergeLists(timeSlotWiseList, getSyncDataAPIState.success!.data.target!.adSequences);
//           showLog('finalSequenceList check here ${finalSequenceList.length}');
//           // for (var slot in finalSequenceList) {
//           //   showLog('Start: ${slot.startTime}, End: ${slot.endTime} ${slot.adDetails.length}');
//           //   for (var slot in slot.adDetails) {
//           //     showLog('Time slot: ${slot.timeSlot}, UUID  ${slot.uuid} ');
//           //   }
//           // }
//
//           getSyncDataAPIState.success?.data.target?.adSequences.clear();
//           getSyncDataAPIState.success?.data.target?.adSequences.addAll(finalSequenceList);
//
//           showLog('getSyncDataAPIState.success!.data.target!.adSequences length ${getSyncDataAPIState.success!.data.target!.adSequences.first.adDetails.length}');
//           // for (var slot in getSyncDataAPIState.success!.data.target!.adSequences) {
//           //   showLog('Start: ${slot.startTime}, End: ${slot.endTime} ${slot.adDetails.length}');
//           //   for (var slot in slot.adDetails) {
//           //     showLog('Time slot: ${slot.timeSlot}, UUID  ${slot.uuid} ');
//           //   }
//           // }
//
//           Future.delayed(const Duration(seconds: 2), () async {
//             /// -------------------- Downloading -------------------- ///
//             ///                                                       ///
//             ///                                                       ///
//             /// ----------------------------------------------------- ///
//
//             Directory? savedDownloadAdsDirectory = await getDownloadsDirectory();
//
//             String downloadPath = savedDownloadAdsDirectory?.path ?? '';
//
//             /// Check Main Directory
//             if (savedDownloadAdsDirectory?.existsSync() == false) {
//               await savedDownloadAdsDirectory?.create();
//               showLog('savedDownloadAdsDirectory path  ${savedDownloadAdsDirectory?.path}');
//             } else {
//               showLog('savedDir already exist   test test ${savedDownloadAdsDirectory?.path}');
//             }
//
//             Directory? savedDownloadDirectoryUsingAdsId;
//             Directory? zipDirectoryForDownloading;
//             if (getSyncDataAPIState.success!.data.target != null) {
//               if (getSyncDataAPIState.success!.data.target?.adSequences != null) {
//                 mainAdSequenceList.addAll(getSyncDataAPIState.success!.data.target!.adSequences);
//               }
//             }
//
//             /// CHECKING DEFAULT AD HERE
//             ///
//             if (getSyncDataAPIState.success?.data.target?.destinationDetails != null) {
//               if (getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.isNotEmpty ?? false) {
//                 /// Creating a directory UUID wise
//                 savedDownloadDirectoryUsingAdsId = Directory('$downloadPath/${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.uuid}');
//                 if (!(savedDownloadDirectoryUsingAdsId.existsSync())) {
//                   await savedDownloadDirectoryUsingAdsId.create();
//                   showLog('savedDownloadDirectoryUsingAdsId path  ${savedDownloadDirectoryUsingAdsId.path}');
//                   zipDirectoryForDownloading = Directory('${savedDownloadDirectoryUsingAdsId.path}/zip');
//                   if (!(zipDirectoryForDownloading.existsSync())) {
//                     await zipDirectoryForDownloading.create();
//                     showLog('zipDirectoryForDownloading path  ${zipDirectoryForDownloading.path}');
//                   } else {
//                     showLog('zipDirectoryForDownloading already exist  ${zipDirectoryForDownloading.path}');
//                   }
//                   String? fileName = (getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.adUrl ?? '').contains('?subDir')
//                       ? getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.adUrl?.split('?subDir').first.split('/').last
//                       : '';
//                   showLog('fileName check $fileName');
//                   // Session.saveLocalData(keyIsDownloadingStarted, true);
//
//                   /// Downloading Zip based on UUID
//                   getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.downloadZipId = await FlutterDownloader.enqueue(
//                     url: getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.adUrl ?? '',
//                     headers: {},
//                     // optional: header send with url (auth token etc)
//                     savedDir: zipDirectoryForDownloading.path,
//                     showNotification: true,
//                     openFileFromNotification: false,
//                     fileName: fileName,
//                   );
//                   getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.localPath = '${zipDirectoryForDownloading.path}/$fileName';
//                   downloadingDefaultTaskId.add(
//                       '${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.uuid ?? ''}_${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.downloadZipId ?? ''}');
//                   showLog('file local task id ${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.downloadZipId}');
//                 } else {
//                   bool isInDownloadingProcess = false;
//                   for (final c in downloadingDefaultTaskId) {
//                     if (c.split('_').first == getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.uuid) {
//                       isInDownloadingProcess = true;
//                     }
//                   }
//
//                   if (!isInDownloadingProcess) {
//                     getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.clear();
//                     final mediaDir =
//                         Directory('${savedDownloadDirectoryUsingAdsId.path}/${getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaType}');
//                     final files = mediaDir.listSync().whereType<File>().toList();
//                     getSyncDataAPIState.success?.data.target?.destinationDetails?.defaultAds?.first.mediaList?.addAll(files.map((file) => file.path));
//                   }
//
//                   showLog('savedDir Default check already exist  ${savedDownloadDirectoryUsingAdsId.path}');
//                 }
//               }
//             }
//
//             /// CHECKING OTHER ADS HERE
//             for (AssignAdSequence adSequenceModel in mainAdSequenceList) {
//               /// AD DETAILS
//               for (int i = 0; i < adSequenceModel.adDetails.length; i++) {
//                 /// Creating a directory UUID wise
//                 ///
//                 savedDownloadDirectoryUsingAdsId = Directory('$downloadPath/${adSequenceModel.adDetails[i].uuid}');
//                 if (!(savedDownloadDirectoryUsingAdsId.existsSync())) {
//                   await savedDownloadDirectoryUsingAdsId.create();
//                   showLog('UUID DIRECTORY PATH  ${savedDownloadDirectoryUsingAdsId.path}');
//                   zipDirectoryForDownloading = Directory('${savedDownloadDirectoryUsingAdsId.path}/zip');
//                   if (!(zipDirectoryForDownloading.existsSync())) {
//                     await zipDirectoryForDownloading.create();
//                     showLog('zipDirectoryForDownloading path  ${zipDirectoryForDownloading.path}');
//                   } else {
//                     showLog('zipDirectoryForDownloading already exist  ${zipDirectoryForDownloading.path}');
//                   }
//                   String? fileName =
//                       (adSequenceModel.adDetails[i].adUrl ?? '').contains('?subDir') ? adSequenceModel.adDetails[i].adUrl?.split('?subDir').first.split('/').last : '';
//                   showLog('fileName check $fileName');
//
//                   if (adSequenceModel.adDetails[i].mediaList?.isEmpty ?? false) {
//                     // Session.saveLocalData(keyIsDownloadingStarted, true);
//
//                     /// Downloading Zip based on UUID
//                     adSequenceModel.adDetails[i].downloadZipId = await FlutterDownloader.enqueue(
//                       url: adSequenceModel.adDetails[i].adUrl ?? '',
//                       headers: {},
//                       // optional: header send with url (auth token etc)
//                       savedDir: zipDirectoryForDownloading.path,
//                       showNotification: true,
//                       openFileFromNotification: false,
//                       fileName: fileName,
//                     );
//
//                     adSequenceModel.adDetails[i].localPath = '${zipDirectoryForDownloading.path}/$fileName';
//                     downloadingTasksIds.add('${adSequenceModel.adDetails[i].uuid}_${adSequenceModel.adDetails[i].downloadZipId ?? ''}');
//                     showLog('file local task id ${adSequenceModel.adDetails[i].downloadZipId}');
//                   }
//                 } else {
//                   bool isInDownloadingProcess = false;
//
//                   if (downloadingTasksIds.isNotEmpty) {
//                     for (final c in downloadingTasksIds) {
//                       if (c.split('_').first == adSequenceModel.adDetails[i].uuid) {
//                         isInDownloadingProcess = true;
//                       }
//                     }
//                   }
//                   if (!isInDownloadingProcess) {
//                     if (downloadingTasksIds.isEmpty && downloadingDefaultTaskId.isEmpty) {
//                       adSequenceModel.adDetails[i].mediaList?.clear();
//                       final mediaDir = Directory('${savedDownloadDirectoryUsingAdsId.path}/${adSequenceModel.adDetails[i].mediaType}');
//                       if (mediaDir.existsSync()) {
//                         final files = mediaDir.listSync().whereType<File>().toList();
//
//                         adSequenceModel.adDetails[i].mediaList?.addAll(files.map((file) => file.path));
//                       }
//                     }
//                   }
//                 }
//               }
//             }
//
//             await Future.delayed(const Duration(seconds: 3), () {
//               if (downloadingTasksIds.isEmpty && downloadingDefaultTaskId.isEmpty) {
//                 bool? isDownloadingStatus = false;
//                 // if (Session.getIsDownloadingStatus() != null) {
//                 //   isDownloadingStatus = Session.getIsDownloadingStatus();
//                 // }¬
//                 Future.delayed(const Duration(milliseconds: 2000), () {
//                   if (isDownloadingStatus != null && isDownloadingStatus == false) {
//                     isDataSync = true;
//                     notifyListeners();
//                   }
//                 });
//               }
//             });
//           });
//         });
//       }
//     }, failure: (NetworkExceptions error) {
//       getSyncDataAPIState.isLoading = false;
//       String errorMsg = NetworkExceptions.getErrorMessage(error);
//       showMessageDialog(context, errorMsg, () => null);
//     });
//
//     notifyListeners();
//   }
//
//   /// Unzip File
//   Future<void> unzipFiles({required String filePath, required String location}) async {
//     final zipFile = File(filePath);
//     final destinationDir = Directory(location);
//     showLog('zipFile path ${zipFile.path}');
//     showLog('destinationDir path ${destinationDir.path}');
//
//     try {
//       ZipFile.extractToDirectory(zipFile: zipFile, destinationDir: destinationDir);
//     } catch (e) {
//       showLog('Zip error $e');
//     }
//   }
//
//   Future<void> storeDataIntoObjectBox() async {
//     /// -------------------- Storing Data Into Object Box -------------------- ///
//     ///                                                                        ///
//     ///                                                                        ///
//     /// ---------------------------------------------------------------------- ///
//     objAdSequence.deleteAll();
//     objSyncData.deleteAll();
//     objMainSyncData.deleteAll();
//     if (getSyncDataAPIState.success?.data != null) {
//       if (getSyncDataAPIState.success?.data.target?.adSequences != null) {
//         if (objAdSequence.box.isEmpty()) {
//           objAdSequence.addList(getSyncDataAPIState.success!.data.target!.adSequences);
//         }
//       }
//     }
//
//     await Future.delayed(const Duration(milliseconds: 500), () async {
//       objAdSequenceRes = await objAdSequence.getAll();
//
//       if (objAdSequenceRes?.data?.isNotEmpty ?? false) {
//         AssignSyncData syncData = AssignSyncData()
//           ..id = getSyncDataAPIState.success?.data.target?.id ?? 0
//           ..adSequences.addAll(objAdSequenceRes!.data!);
//         if (objSyncData.box.isEmpty()) {
//           objSyncData.add(syncData);
//         } else {
//           objSyncData.update(syncData.id, syncData);
//         }
//       }
//       Future.delayed(const Duration(milliseconds: 500), () async {
//         objSyncDataRes = await objSyncData.getSingle(1);
//
//         if (objSyncDataRes?.data != null) {
//           GetAssignSyncResponseModel getSyncResponseModel = GetAssignSyncResponseModel()
//             ..id = (getSyncDataAPIState.success?.id ?? 0)
//             ..data.target = objSyncDataRes?.data
//             ..status = getSyncDataAPIState.success?.status
//             ..message = getSyncDataAPIState.success?.message;
//
//           if (objMainSyncData.box.isEmpty()) {
//             objMainSyncData.add(getSyncResponseModel);
//           } else {
//             objMainSyncData.update(getSyncResponseModel.id, getSyncResponseModel);
//           }
//         }
//
//         await Future.delayed(const Duration(milliseconds: 500), () async {
//           objMainSyncDataResponse = await objMainSyncData.getSingle(1);
//           showLog('objMainSyncDataResponse 1 ${objMainSyncDataResponse?.data?.data.target?.adSequences.length}');
//           showLog('objMainSyncDataResponse 2 ${objMainSyncDataResponse?.data?.data.target?.adSequences.first.adDetails.length}');
//           // showLog('objMainSyncDataResponse 3 ${objMainSyncDataResponse?.data?.data.target?.adSequences.first.adDetails[3].mediaList?.length}');
//
//           getCurrentIndexOfCurrentSlotAndTime();
//           Future.delayed(const Duration(milliseconds: 500), () {
//             getSyncDataAPIState.isLoading = false;
//             notifyListeners();
//           });
//         });
//       });
//     });
//   }
//
//   Future<void> storedFullyPlayedEntries(FullyPlayedAdDetail fullyPlayedAdDetails) async {
//     /// -------------------- Storing Data Into Object Box -------------------- ///
//     ///                                                                        ///
//     ///                        FULLY PLAYED OBJECTS                            ///
//     /// ---------------------------------------------------------------------- ///
//     objFullyPlayedAdDetail.add(fullyPlayedAdDetails);
//
//     await Future.delayed(const Duration(milliseconds: 100), () async {
//       objFullyPlayedAdDetailsResponse = await objFullyPlayedAdDetail.getAll();
//       showLog('objFullyPlayedAdDetailsResponse length ${objFullyPlayedAdDetailsResponse?.data?.length}');
//     });
//   }
// }
