import 'dart:async';

import 'package:flutter/services.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/const/method_channel_const.dart';


class LightController {
  LightController._();

  static LightController instance = LightController._();

  final dasherLightChannel = MethodChannel(MethodChannelConstant.dasherLightChannel);
  final listenForSensorTappedEventChannel = EventChannel(MethodChannelConstant.listenForSensorTappedEventChannel);
  StreamSubscription? listenForSensorTappedStream;
  Timer? mainTimer;

  void turnOnLightOnLayer1() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnLightOnLayer1);
  }

  void turnOnLightOnLayer2() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnLightOnLayer2);
  }

  void turnOnLightOnLayer3() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnLightOnLayer3);
  }

  void blinkLightOnLayer1() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.blinkLightOnLayer1);
  }

  void blinkLightOnLayer2() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.blinkLightOnLayer2);
  }

  void blinkLightOnLayer3() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.blinkLightOnLayer3);
  }

  void turnOnAllLights() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnAllLights);
  }

  void turnOffAllLightsOnProduction() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOffAllLights);
  }

  void turnOffAllLights() {
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOffAllLights);
  }

  void startDiscoLight() {
    mainTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      showLog('Light Timer: ${timer.tick % 3}');
      showLog('Light Timer: ${timer.tick % 2}');
      dasherLightChannel.invokeMethod(MethodChannelConstant.turnOffAllLights);
      Future.delayed(const Duration(milliseconds: 200));
      if (timer.tick % 3 == 0) {
        dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnLightOnLayer3);
      } else if (timer.tick % 2 == 0) {
        dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnLightOnLayer2);
      } else {
        dasherLightChannel.invokeMethod(MethodChannelConstant.turnOnLightOnLayer1);
      }
    });
  }

  void stopDiscoLight() {
    mainTimer?.cancel();
    mainTimer = null;
    dasherLightChannel.invokeMethod(MethodChannelConstant.turnOffAllLights);
  }

  void listenForSensorTapped(Function(bool isSensorTapped) onSensorTapped) {
    listenForSensorTappedStream = listenForSensorTappedEventChannel.receiveBroadcastStream().listen((event) {
      showLog('sensor tap from dart');
      onSensorTapped.call(event);
    });
  }

  void cancelListenForSensorTappedStream() {
    listenForSensorTappedStream?.cancel();
  }
}
