import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/controller/download/download_controller.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/provider/network/network_exceptions.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/repository/operator/contract/operator_repository.dart';
import 'package:odigo_display/framework/repository/storage/contract/storage_repository.dart';
import 'package:odigo_display/framework/repository/storage/model/delete_archive_ads_response_model.dart';
import 'package:odigo_display/framework/repository/storage/model/storage_model.dart';
import 'package:odigo_display/framework/utils/helper/objectbox_client.dart';
import 'package:odigo_display/framework/utils/ui_state.dart';
import 'package:odigo_display/objectbox.g.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/widgets/common_dialogs.dart';
import 'package:path_provider/path_provider.dart';

final storageController = ChangeNotifierProvider((ref) => getIt<StorageController>());

@injectable
class StorageController extends ChangeNotifier {
  StorageController(this.operatorRepository, this.storageRepository);

  OperatorRepository operatorRepository;
  MethodChannel storageChannel = const MethodChannel('storageChannel');

  StorageResponseModel? storageModel;

  /// loader
  bool isLoading = false;
  void updateIsLoading(bool val){
    isLoading = val;
    notifyListeners();
  }

  // GetStorageInfoModel? storageList;

  /// Get Size Of Images And Videos
  String getSizeOfImageAndVideo(String path) {
    File file = File(path);
    int sizeInBytes = file.lengthSync();
    double sizeInMb = sizeInBytes / (1024 * 1024);
    double kb = sizeInMb * 1024;
    return kb.toStringAsFixed(2);
  }

  /*/// Get Stores From Directory
  Future<void> getStorageDirectoryInfo(WidgetRef ref) async {
    storageList = null;

    /// Stores List
    Directory? storeListFolder = await getExternalStorageDirectory();

    /// Total Size of image and video
    List<String> combineList = [];

    /// Total Length of image and video
    List<int> totalLength = [];

    /// Parent Directory check
    if (await storeListFolder?.exists() ?? false) {
      List<FileSystemEntity>? storeNameList = storeListFolder?.listSync();

      for (FileSystemEntity parentDirectory in storeNameList!) {
        List<FileSystemEntity>? localStoresListNew = [];

        /// Images And Videos Folder
        Directory? localPath = Directory(
            '${storeListFolder?.path}/${parentDirectory.path.split('/').last}');
        GetStorageInfoModel? localStoreInfoList;

        List<String> finalImageList = [];
        List<String> finalVideoList = [];

        /// Child Directory Check
        if (await localPath.exists()) {
          localStoresListNew = localPath.listSync();

          /// For Path Of Images
          List<FileSystemEntity> imageList = [];

          /// For Path Of Videos
          List<FileSystemEntity> videoList = [];

          /// Total Size Of Images And Videos
          List<double> imagesSizeList = [];
          List<double> videosSizeList = [];

          for (final childDirectory in localStoresListNew) {
            /// Images Directory
            Directory? imagesAndVideosDirectory =
                Directory(childDirectory.path);
            if (childDirectory.path.contains('/$imageFolderName')) {
              imageList = imagesAndVideosDirectory.listSync();
              for (final i in imageList) {
                String size = getSizeOfImageAndVideo(i.path);
                imagesSizeList.add(double.parse(size));
              }
            } else {
              videoList = imagesAndVideosDirectory.listSync();
              for (final i in videoList) {
                String size = getSizeOfImageAndVideo(i.path);
                videosSizeList.add(double.parse(size));
              }
            }
          }

          double imageSize = (imagesSizeList.isNotEmpty)
              ? imagesSizeList.length == 1
                  ? imagesSizeList.first
                  : imagesSizeList.reduce((value, element) => value + element)
              : 0.0;
          double videoSize = (videosSizeList.isNotEmpty)
              ? videosSizeList.length == 1
                  ? videosSizeList.first
                  : videosSizeList.reduce((value, element) => value + element)
              : 0.0;

          finalImageList.add((imageSize / 1024).toStringAsFixed(2));

          finalVideoList.add((videoSize / 1024).toStringAsFixed(2));
          String sum = (imageSize + videoSize).toString();
          combineList.add(sum);
          totalLength.add(imageList.length + videoList.length);

          /// Local Store Info List
          localStoreInfoList = GetStorageInfoModel(
            parentDirectory: storeNameList,
            imagesDirectory: imageList,
            videosDirectory: videoList,
            parentDirectorySize: combineList,
            imageSize: finalImageList,
            videoSize: finalVideoList,
            totalLength: totalLength,
          );

          storageList = localStoreInfoList;
        }
      }
    }

    notifyListeners();
  }*/

  ///Get Storage from native
  Future<void> getStorageFromNative() async {
    var response = (await storageChannel.invokeMethod('getStorage')) as Map<Object?, Object?>;
    Map<String, dynamic> responseMap = {};
    response.forEach((key, value) {
      responseMap[key.toString()] = value;
    });
    storageModel = storageResponseModelFromJson(jsonEncode(responseMap));

    showLog('response $response');
    notifyListeners();
  }

  // List<GetStorageInfoModel>? storagePathList;

  /// storeList
  List<StoreData> storeList = [];

  Future<void> getStoreList() async {
    storeList.clear();
    List<StoreData> responseModel = await operatorRepository.getStoreList();
    storeList = responseModel;
    getStoreWiseAdsList();
    notifyListeners();
  }

  Future<void> getStoreWiseAdsList() async {
    for (var store in storeList) {
      List<AdDetails> responseModel = await operatorRepository.getStoreWiseAdsList(store.storeUuid ?? '');
      List<AdDetails> adsList = responseModel ?? [];
      store.assignAdDetails.addAll(adsList);
      showLog("store wise ads length : ${store.assignAdDetails.length}");
    }
    getStorageDirectoryInformation();
    notifyListeners();
  }

  List<GetStorageInformationModel> storageInformationList = [];

  // Future<void> getStorageDirectoryInformation() async {
  //   storageInformationList.clear();
  //
  //   /// Stores List
  //   Directory? storeListFolder = await getApplicationDocumentsDirectory();
  //
  //   if (await storeListFolder.exists() ?? false) {
  //     List<FileSystemEntity>? storeNameList = storeListFolder.listSync();
  //     showLog("localStoresListNew ${storeNameList?.length}");
  //     for (final storePath in storeNameList!) {
  //       if (storePath is Directory) {
  //         if (storePath.path.contains(advertisementDirectoryName)) {
  //           List<FileSystemEntity>? localStoresListNew = [];
  //
  //           Directory? localPath = Directory(storePath.path);
  //
  //           localStoresListNew = localPath?.listSync();
  //
  //           for (final local in localStoresListNew!) {
  //             List<FileSystemEntity>? adsPathList = [];
  //
  //             Directory? adsPath = Directory(local.path);
  //
  //             adsPathList = adsPath.listSync();
  //
  //             for (final ads in adsPathList) {
  //               List<FileSystemEntity>? mediaList = [];
  //               Directory? mediaDirectory;
  //
  //               /// Total Size Of Images And Videos
  //               List<double> imagesSizeList = [];
  //               List<double> videosSizeList = [];
  //
  //               /// get image size of image folder
  //               if (ads.path.contains('IMAGE') || ads.path.contains('image')) {
  //                 mediaDirectory = Directory(ads.path);
  //
  //                 var imageList = mediaDirectory.listSync();
  //
  //                 /// check inside image folder
  //                 for (final image in imageList) {
  //                   if (image is File) {
  //                     String size = getSizeOfImageAndVideo(image.path);
  //                     imagesSizeList.add(double.parse(size));
  //                   }
  //                 }
  //               }
  //
  //               /// get video size of video folder
  //               if (ads.path.contains('VIDEO') || ads.path.contains('video')) {
  //                 mediaDirectory = Directory(ads.path);
  //                 var videoList = mediaDirectory.listSync();
  //                 for (final video in videoList) {
  //                   if (video is File) {
  //                     String size = getSizeOfImageAndVideo(video.path);
  //                     showLog("media video size ${size}");
  //                     imagesSizeList.add(double.parse(size));
  //                   }
  //                 }
  //               }
  //
  //               /// total size of image and video list
  //               double imageSize = (imagesSizeList.isNotEmpty)
  //                   ? imagesSizeList.length == 1
  //                       ? imagesSizeList.first
  //                       : imagesSizeList.reduce((value, element) => value + element)
  //                   : 0.0;
  //               double videoSize = (videosSizeList.isNotEmpty)
  //                   ? videosSizeList.length == 1
  //                       ? videosSizeList.first
  //                       : videosSizeList.reduce((value, element) => value + element)
  //                   : 0.0;
  //               double sum = (imageSize + videoSize);
  //
  //               showLog("sum of sum $sum");
  //
  //               GetStorageInformationModel getStorageInformationModel = GetStorageInformationModel(adsUuid: local.path.split('/').last, mediaPath: local.path, totalLength: sum.floor(), totalSize: []);
  //
  //               storageInformationList.add(getStorageInformationModel);
  //               notifyListeners();
  //             }
  //           }
  //         }
  //       }
  //     }
  //
  //     showLog("storageInformationList length ${storageInformationList.length}");
  //
  //     /// adding store wise size
  //     for (final store in storeList) {
  //       int usedStorage = 0;
  //       for (final ads in store.assignAdDetails) {
  //         for (final storage in storageInformationList) {
  //           if (ads.uuid == storage.adsUuid) {
  //             usedStorage = usedStorage + storage.totalLength;
  //           }
  //         }
  //       }
  //       store.storageUsedSize = usedStorage;
  //     }
  //   }
  //   notifyListeners();
  // }
  Future<void> getStorageDirectoryInformation() async {
    var adsDirectory = await getApplicationDocumentsDirectory();
    adsDirectory = Directory('${adsDirectory.path}/$advertisementDirectoryName');
    for (var store in storeList) {
      var totalMemory = 0;
      for (var ads in store.assignAdDetails) {
        var adFinalDirectory = Directory('${adsDirectory.path}/${ads.uuid}/${ads.mediaType?.toUpperCase() ?? ''}');
        adFinalDirectory.listSync().forEach((file) {
          totalMemory += file.statSync().size;
        });
      }
      store.storageUsedSize = totalMemory.toDouble();
    }
    notifyListeners();
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  /// delete archive ads from object box
  Future<void> deleteArchiveAds(WidgetRef ref, List<String> adList) async {
    ObjectBoxClient<AdDetails> archiveAdsDetails = ObjectBoxClient<AdDetails>();

    for(String id in adList) {
      Query<AdDetails> query = archiveAdsDetails.box.query(AdDetails_.uuid.equals(id)).build();
      AdDetails? adDetails = await query.findFirstAsync();
      if(adDetails?.uuid != null) {
        ref.read(downloadController).deleteAdDirectory(adDetails?.uuid ?? '');
      }
      
      query.remove();
    }
  }


  StorageRepository storageRepository;
  UIState<DeleteArchiveAdsResponseModel> deleteArchiveAdsState = UIState<DeleteArchiveAdsResponseModel>();

  /// delete archive ads
  Future<void> deleteArchiveAdsAPI(BuildContext context, WidgetRef ref) async {
    deleteArchiveAdsState.isLoading = true;
    deleteArchiveAdsState.success = null;
    notifyListeners();

    final result = await storageRepository.deleteArchiveAdsAPI();

    result.when(success: (data) async {
      deleteArchiveAdsState.isLoading = false;
      deleteArchiveAdsState.success = data;
    }, failure: (NetworkExceptions error) {
      deleteArchiveAdsState.isLoading = false;
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      showCommonErrorDialog(context: context, message: errorMsg);
    });

    notifyListeners();
  }


}

/// Get Storage Info Model
// class GetStorageInfoModel {
//   List<FileSystemEntity> parentDirectory;
//   List<FileSystemEntity> imagesDirectory;
//   List<FileSystemEntity> videosDirectory;
//   List<String> parentDirectorySize;
//   List<String> imageSize;
//   List<String> videoSize;
//   List<int> totalLength;
//
//   GetStorageInfoModel({
//     required this.parentDirectory,
//     required this.imagesDirectory,
//     required this.videosDirectory,
//     required this.parentDirectorySize,
//     required this.imageSize,
//     required this.videoSize,
//     required this.totalLength,
//   });
// }

class GetStorageInformationModel {
  String? storeUuid;
  String adsUuid;
  String mediaPath;
  int totalLength;
  List<int> totalSize;

  GetStorageInformationModel({
    this.storeUuid,
    required this.adsUuid,
    required this.mediaPath,
    required this.totalLength,
    required this.totalSize,
  });
}
