import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_display/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';

// Provider to create and manage the RobotActionController instance
final robotActionController = ChangeNotifierProvider((ref) => getIt<RobotActionController>());

/// RobotActionController is responsible for managing robot events and notifying listeners.
/// It acts as a bridge between the RobotActionManager and the UI.
@injectable
class RobotActionController extends ChangeNotifier {
  // Singleton instance of RobotActionManager to interact with robot actions
  RobotEventManger robotActionManger = RobotEventManger.instance;

  // Getter to access the latest HostNameEvent
  HostNameEvent? get hostNameEvent => robotActionManger.hostNameEvent;

  // Getter to access the latest CoreDataEvent
  CoreDataEvent? get coreDataEvent => robotActionManger.coreDataEvent;

  // Getter to access the latest IpEvent
  IpEvent? get ipEvent => robotActionManger.ipEvent;

  // Getter to access the Android-specific IpEvent
  IpEvent? get androidIpEvent => robotActionManger.androidIpEvent;

  // Getter to access the latest HflsVersionEvent
  HflsVersionEvent? get hflsVersionEvent => robotActionManger.hflsVersionEvent;

  // Getter to access the latest PositionEvent
  PositionEvent? get positionEvent => robotActionManger.positionEvent;

  // Getter to access the latest SensorEvent
  SensorEvent? get sensorEvent => robotActionManger.sensorEvent;

  // Getter to access the latest LaserEvent
  LaserEvent? get laserEvent => robotActionManger.laserEvent;

  // Getter to access the latest VersionEvent
  VersionEvent? get versionEvent => robotActionManger.versionEvent;

  NavResultEvent? get navResultEvent => robotActionManger.navResultEvent;

  @override
  void notifyListeners() {
    // Notify all registered listeners about the state changes
    super.notifyListeners();
  }
}
