import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/utils/ui_state.dart';

final settingsScreenController = ChangeNotifierProvider(
  (ref) => getIt<SettingsScreenController>(),
);

@injectable
class SettingsScreenController extends ChangeNotifier {


  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    settingsscreenState.isLoading = false;

    // updateSelectedLanguage('English');
    if (isNotify) {
      notifyListeners();
    }
  }

  var settingsscreenState = UIState();

  /// update loading
  void updateLoadingStatus(value) {
    settingsscreenState.isLoading = value;
    notifyListeners();
  }

  @override
  void notifyListeners() {
    // TODO: implement notifyListeners
    super.notifyListeners();
  }
}
