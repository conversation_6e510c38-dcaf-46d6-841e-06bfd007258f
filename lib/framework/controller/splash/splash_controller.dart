import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:kody_enc_dec/kody_enc_dec.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/encryption/contract/encryption_repository.dart';
import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/framework/utils/extension/datetime_extension.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_dialogs.dart';

final splashController = ChangeNotifierProvider((ref) => getIt<SplashController>());

@injectable
class SplashController extends ChangeNotifier {
  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    showLog('Splash disposeController  Called');
    isLoading = false;
    isSecretApiLoading = false;
    if (isNotify) {
      notifyListeners();
    }
  }

  List<String> dateTimeData = [];

  /// Fill From Hive
  fillListOfDateTimeDataFromHive() {
    final localData = RobotSession.listOfSplashDateTime;
    showLog('localData $localData');
    if (localData != null && localData != '') {
      dateTimeData.addAll(localData.replaceAll('[', '').replaceAll(']', '').split(',').toList());
      showLog('dateTimeData Length ${dateTimeData.length}');
    }
  }

  /// Add to DateTime Data
  addDateTimeData(String dateTime) {
    showLog('addDateTimeData Called with dateTime =>$dateTime<=');
    dateTimeData.add(dateTime);

    if (dateTimeData.isNotEmpty) {
      RobotSession.listOfSplashDateTime = '';
      RobotSession.listOfSplashDateTime = dateTimeData.toString();
      showLog('addDateTimeData Called with dateTime Local =>${RobotSession.listOfSplashDateTime}<=');
    }
  }

  GlobalKey successDialogKey = GlobalKey();

  bool isLocationPermissionEnabled = false;
  bool isNavCompleteEventEnabled = false;
  final mainServiceChannel = const MethodChannel('odigoAndroidChannel');

  Future<void> checkLocationPermission() async {
    isLocationPermissionEnabled = await mainServiceChannel.invokeMethod('checkLocationPermission');
  }

  Future<void> navCompleteEvent() async {
    isNavCompleteEventEnabled = await mainServiceChannel.invokeMethod('navCompleteEvent');
  }

  /// Check if location permission is not enabled the stores the number of times it denied.
  Future<void> configureLocationPermission(BuildContext context) async {
    await checkLocationPermission();
    if (!isLocationPermissionEnabled) {
      isLocationPermissionEnabled = await mainServiceChannel.invokeMethod('configureLocationPermission');
    }

    notifyListeners();
  }

  // getFullyPlayedData(BuildContext context, {required String startTime, required String endTime}) async {
  //   SyncedData data = SyncedData.fromJson(jsonDecode(await DefaultAssetBundle.of(context).loadString(Assets.json.dummyJson))['data']);
  //   List<AdSequence> overlapSequence = getOverlappingRanges(startTime.dateFromOnlyTime, endTime.dateFromOnlyTime, data.adSequences ?? []);
  //   List<AdDetails> overlapAdDetails = [];
  //   for (var sequence in overlapSequence) {
  //     for (var ads in sequence.adDetails) {
  //       if (ads.time!.dateFromOnlyTime.isTimeAfterOrEqual(startTime.dateFromOnlyTime) && ads.time!.dateFromOnlyTime.isTimeBefore(endTime.dateFromOnlyTime)) {
  //         overlapAdDetails.add(ads);
  //       }
  //     }
  //   }
  //   log(jsonEncode(overlapAdDetails.map((e) {
  //     return {'uuid': e.uuid, 'timeDeduct': e.time!.fullDateFromTime.millisecondsSinceEpoch};
  //   }).toList()));
  // }
  //
  // List<AdSequence> getOverlappingRanges(DateTime givenStart, DateTime givenEnd, List<AdSequence> adSequence) {
  //   List<AdSequence> overlappingRanges = [];
  //   for (var sequence in adSequence) {
  //     DateTime rangeStart = sequence.startTime;
  //     DateTime rangeEnd = sequence.endTime;
  //     if (isOverlap(givenStart, givenEnd, rangeStart, rangeEnd)) {
  //       overlappingRanges.add(sequence);
  //     }
  //   }
  //   return overlappingRanges;
  // }
  //
  // bool isOverlap(DateTime start1, DateTime end1, DateTime start2, DateTime end2) {
  //   return start1.isTimeBeforeOrEqual(end2) && end1.isTimeAfter(start2);
  // }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  ///Progress Indicator
  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  EncryptionRepository encryptionRepository;

  SplashController(this.encryptionRepository);

  bool isSecretApiLoading = false;

  void updateLoadingState(bool value) {
    isSecretApiLoading = value;
    notifyListeners();
  }

  ///Get Backend Key
  Future<(String s1, String s2)> getBackendEncryptionKey(BuildContext context) async {
    updateLoadingState(true);
    notifyListeners();
    final result = await encryptionRepository.getBackendPublicKey();
    result.when(success: (data) async {
      String publicKey = data;
      showLog('publicKey publicKey publicKey $publicKey');
      SessionRepository.session.backendPublicSet = publicKey;
      await Future.delayed(const Duration(milliseconds: 500));
      EncryptionHelper.instance.initRSAEncryptor(publicKey);
      await Future.delayed(const Duration(milliseconds: 500));
      if (SessionRepository.session.frontEndPrivateKey != '' || SessionRepository.session.frontEndPublicKey != '') {
        var (pubK, priK) = await EncryptionHelper.instance.generateKeyValuePair();
        await SessionRepository.session.setFrontEndKey(pubK, priK);
      }
      await Future.delayed(const Duration(seconds: 1));
      showLog('SessionRepository.session.frontEndPrivateKey ${SessionRepository.session.frontEndPrivateKey}');
      EncryptionHelper.instance.initRSADecrypt(SessionRepository.session.frontEndPrivateKey ?? '');
      updateLoadingState(false);
    }, failure: (error) {
      updateLoadingState(false);

      String errorMsg = NetworkExceptions.getErrorMessage(error);

      showTryAgainDialog(
        context: context,
        message: errorMsg.contains('!doctype html') ? 'Please check your server IP' : errorMsg,
        onButtonTap: () {
          getBackendEncryptionKey(context);
        },
      );
    });
    showLog("isSecretApiLoading >>> $isSecretApiLoading");
    notifyListeners();
    return (SessionRepository.session.frontEndPrivateKey ?? "", SessionRepository.session.frontEndPublicKey ?? "");
  }
}
