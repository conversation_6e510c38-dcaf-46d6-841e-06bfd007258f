import 'dart:async';
import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/repository/operator/contract/operator_repository.dart';
import 'package:odigo_display/framework/utils/extension/string_extension.dart';
import 'package:odigo_display/framework/utils/helper/objectbox_response_model.dart';
import 'package:odigo_display/framework/utils/ui_state.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:video_player/video_player.dart';

final waitingScreenController = ChangeNotifierProvider(
  (ref) => getIt<WaitingScreenController>(),
);

@injectable
class WaitingScreenController extends ChangeNotifier {
  DateTime destinationStartTime = DateTime.now();
  DateTime destinationEndTime = DateTime.now();

  OperatorRepository operatorRepository;

  WaitingScreenController(this.operatorRepository);

  bool isStoreClosed = false;

  bool isShowing = false;

  /// Video Player Controller
  ChewieController? chewieController;

  /// Video Player Controller
  VideoPlayerController? videoPlayerController;

  /// Get Destination Start Time from Object box

  Timer? waitingTimer;
  Duration? waitingDuration;
  String formattedTime = "";

  void _startTimer(WidgetRef ref) {
    waitingTimer?.cancel();
    waitingTimer = null;
    waitingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if ((waitingDuration?.inSeconds ?? 0) > 0) {
        waitingDuration = waitingDuration! - const Duration(seconds: 1);

        formattedTime = _formatDuration(waitingDuration!);
        notifyListeners();
      } else {
        waitingTimer?.cancel();
        waitingTimer = null;
      }
    });
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '${duration.inHours.toString().padLeft(2, '0')}:$minutes:$seconds';
  }

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    destinationStartTime = DateTime.now();
    destinationEndTime = DateTime.now();
    isStoreClosed = false;
    waitingscreenState.isLoading = false;
    if (waitingTimer?.isActive ?? false) {
      waitingTimer?.cancel();
    }

    if (isNotify) {
      notifyListeners();
    }
  }

  Duration? duration;

  Future<void> initializeStartAndEndTime(WidgetRef ref) async {
    ObjectBoxResponseModel<DestinationDetails?> syncedDataClient = await operatorRepository.getDestinationDetails();
    if (syncedDataClient.data == null) {
      ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.downloadScreen());
      return;
    }
    destinationStartTime = syncedDataClient.data?.destinationStartTime?.dateFromTime ?? DateTime.now();
    destinationEndTime = syncedDataClient.data?.destinationEndTime?.dateFromTime ?? DateTime.now();
    showLog('destinationStartTime $destinationStartTime');
    showLog("destinationEndTime $destinationEndTime");
  }

  /// Convert time into duration
  Future<void> convertTimeIntoDuration(WidgetRef ref) async {
    DateTime startDateTime = destinationStartTime;
    showLog('dateTime $startDateTime');
    DateTime endDateTime = destinationEndTime;
    showLog('dateTime $endDateTime');
    DateTime currentTime = DateTime.now();
    showLog('currentTime $currentTime');
    duration = startDateTime.difference(currentTime);
    if (duration?.isNegative ?? false) {
      Duration totalDuration = endDateTime.difference(currentTime);
      if (totalDuration.isNegative || (totalDuration.inHours == 0 && totalDuration.inMinutes == 0)) {
        isStoreClosed = true;
        showDefaultAds();
        notifyListeners();
      }
    } else {
      formattedTime = _formatDuration(duration!);
      waitingDuration = duration;
      notifyListeners();
      _startTimer(ref);
      showDefaultAds();
      notifyListeners();
    }
    notifyListeners();
  }

  var waitingscreenState = UIState();

  /// update loading
  void updateLoadingStatus(value) {
    waitingscreenState.isLoading = value;
    notifyListeners();
  }

  List<AdDetails> defaultAdsList = [];

  getDefaultAdsList() async {
    defaultAdsList.clear();
    List<AdDetails> responseModel = await operatorRepository.getDefaultAdsList();
    defaultAdsList = responseModel ?? [];
    showLog("defaultAdsList defaultAdsList ${defaultAdsList.length}");
    notifyListeners();
  }

  showDefaultAds() {
    isShowing = true;
    notifyListeners();
    if (defaultAdsList.isNotEmpty) {
      if (defaultAdsList.first.mediaType?.toLowerCase() == "IMAGE".toLowerCase()) {
        loadImage(defaultAdsList.first.mediaList.first ?? '');
      } else {
        loadVideo(defaultAdsList.first.mediaList.first ?? '');
      }
    }
  }

  String mediaURL = '';

  loadImage(String imageURL) {
    showLog("image path $mediaURL");
    mediaURL = imageURL;
    notifyListeners();
  }

  void loadVideo(String videoURL) async {
    videoPlayerController?.dispose();
    chewieController?.dispose();
    chewieController = null;
    videoPlayerController = null;
    showLog('Load Video Gets Called $videoURL');

    videoPlayerController = VideoPlayerController.file(File(videoURL));
    showLog('videoPlayerController url ${videoPlayerController?.value.isInitialized}');
    await videoPlayerController?.initialize().then(
      (value) {
        notifyListeners();
      },
    );
    showLog('videoPlayerController check  ${videoPlayerController?.value.isInitialized}');
    showLog('videoPlayerController value  ${videoPlayerController?.dataSource}');
    chewieController = ChewieController(
      videoPlayerController: videoPlayerController!,
      autoPlay: true,
      looping: true,
      customControls: const Offstage(),
      allowFullScreen: true,
      fullScreenByDefault: false,
      // startAt: Duration(seconds: (videoPlayerController?.value.duration.inSeconds ?? 0) - 2),
    );
    showLog('chewieController url ${chewieController?.videoPlayerController.value}');

    notifyListeners();

    /// Video Player Controller
    videoPlayerController?.setLooping(true);
    videoPlayerController?.addListener(() {
      if (videoPlayerController?.value.duration == videoPlayerController?.value.position) {}
    });
    notifyListeners();
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }
}
