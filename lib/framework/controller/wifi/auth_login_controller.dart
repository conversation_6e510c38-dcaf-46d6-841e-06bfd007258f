import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/provider/network/network_exceptions.dart';
import 'package:odigo_display/framework/repository/common/model/common_response_model.dart';
import 'package:odigo_display/framework/repository/operator/contract/operator_repository.dart';
import 'package:odigo_display/framework/repository/setting/contract/setting_repository.dart';
import 'package:odigo_display/framework/repository/setting/model/request_model/settings_request_model.dart';
import 'package:odigo_display/framework/repository/setting/model/response_model/settings_response_model.dart';
import 'package:odigo_display/framework/utils/ui_state.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/widgets/common_dialogs.dart';
import 'package:odigo_display/ui/wifi/web/helper/robot_session.dart';

import '../../dependency_injection/inject.dart';

final authLoginController = ChangeNotifierProvider((ref) => getIt<AuthLoginController>());

@injectable
class AuthLoginController extends ChangeNotifier {
  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    phoneNumberController.clear();
    Future.delayed(const Duration(milliseconds: 100), () {
      formKey.currentState?.reset();
    });

    if (isNotify) {
      notifyListeners();
    }
  }

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  TextEditingController phoneNumberController = TextEditingController();

  ///To check if all fields are valid or not
  bool isAllFieldsValid = false;

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  // DefaultAuthenticationRepository authenticationRepository;
  SettingRepository settingRepository;
  OperatorRepository operatorRepository;

  AuthLoginController(this.settingRepository, this.operatorRepository);

  var loginState = UIState<CommonResponseModel>();

  ///TODO add settings api when needed
  UIState<SettingsListResponseModel> settingsListState = UIState<SettingsListResponseModel>();

  ///Get Settings Api
  Future<void> getSettingsApi(BuildContext context) async {
    settingsListState.isLoading = true;
    settingsListState.success = null;
    notifyListeners();
    final result = await settingRepository.getSettingsApi(RobotSession.robotUuid, settingsKey: 'ipAddress1');

    result.when(success: (data) async {
      settingsListState.isLoading = false;
      settingsListState.success = data;
      showLog('settingsListState.success ${jsonEncode(settingsListState.success?.data)}');
    }, failure: (NetworkExceptions error) {
      settingsListState.isLoading = false;
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      // showCommonErrorDialog(context: context, message: errorMsg);
    });
    settingsListState.isLoading = false;
    notifyListeners();
  }



  UIState<CommonResponseModel> saveSettingState = UIState<CommonResponseModel>();
  UIState<SettingsListResponseModel> getSettingState = UIState<SettingsListResponseModel>();

  ///Get Settings Api
  Future<void> saveSettingsApi(String? ipAddress, String? wifiName, {bool isPutRequest = false}) async {
    saveSettingState.isLoading = true;
    saveSettingState.success = null;
    notifyListeners();
    SettingsRequestModel? settingsRequestModel;
    if (!isPutRequest) {
      settingsRequestModel = SettingsRequestModel(
        key: 'ipAddress1',
        value: jsonEncode(
          {
            'ipAddress': ipAddress,
            'wifiName': wifiName,
          },
        ),
        robotUuid: RobotSession.robotUuid,
      );
    } else {
      settingsRequestModel = SettingsRequestModel(
        key: 'ipAddress',
        value: jsonEncode(
          {
            'ipAddress': ipAddress,
            'wifiName': wifiName,
          },
        ),
        robotUuid: RobotSession.robotUuid,
        uuid: settingsListState.success?.data?.first.uuid,
      );
    }

    final result = await settingRepository.saveSettingsApi(settingsRequestModel, putRequest: isPutRequest);

    result.when(success: (data) async {
      saveSettingState.isLoading = false;
      saveSettingState.success = data;
    }, failure: (NetworkExceptions error) {
      // String errorMsg = NetworkExceptions.getErrorMessage(error);
    });

    saveSettingState.isLoading = false;
    notifyListeners();
  }

  // ///To delete all the local maps in system
  // Future<void> deleteLocalMapAfterLogin(BuildContext context, WidgetRef ref, Function() allLocalMapsDeleted) async {
  //   final mapRead = ref.read(mapController);
  //   mapRead.ipMapListApi(context).then((value) async {
  //     mapRead.ipCurrentMapApi(context).then((value) {
  //       mapRead.ipMapListState.success?.maps?.removeWhere((map) => map.name == mapRead.ipCurrentMapState.success?.name);
  //       if (mapRead.ipMapListState.success?.maps?.isEmpty ?? true) {
  //         allLocalMapsDeleted.call();
  //         return;
  //       }
  //       if (mapRead.ipMapListState.success?.maps?.last.name != null) {
  //         mapRead.deleteMapLocalApi(context, mapRead.ipMapListState.success!.maps!.last.name!).then((value) {
  //           deleteLocalMapAfterLogin(context, ref, allLocalMapsDeleted);
  //         });
  //       }
  //     });
  //   });
  // }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }
}
