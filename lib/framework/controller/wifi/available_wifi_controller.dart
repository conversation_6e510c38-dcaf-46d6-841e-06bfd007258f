import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/repository/wifi/wifi_devices_list_response_model.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/const/method_channel_const.dart';

final availableWifiController = ChangeNotifierProvider(
  (ref) => getIt<AvailableWifiController>(),
);

@injectable
class AvailableWifiController extends ChangeNotifier {
  // ///Dispose Controller
  // void disposeController({bool isNotify = false}) {
  //   if (isNotify) {
  //     notifyListeners();
  //   }
  // }
  //
  // clearDialogData() {
  //   isPasswordHidden = true;
  //   networkSecurityCtr.clear();
  //   networkSecurityCtr.text = '59319290';
  //   notifyListeners();
  // }
  //
  // bool isPasswordHidden = true;
  // GlobalKey connectingDialogKey = GlobalKey();
  //
  // ///change visibility of the password
  // void changePasswordVisibility() {
  //   isPasswordHidden = !isPasswordHidden;
  //   notifyListeners();
  // }
  //
  // GlobalKey<FormState> formKey = GlobalKey<FormState>();
  // TextEditingController networkSecurityCtr = TextEditingController();
  //
  // EventChannel? wifiEventChannel;
  // EventChannel? wifiConnectionChannel;
  //
  // final wifiDevicesEventChannel = const EventChannel('scanForDevices');
  // final wifiMethodChannel = const MethodChannel('wifiChannel');
  // bool isFetchingWifiDevices = false;
  //
  // /// Event channel to invoke the connect to network
  // final wifiNetworkEventChannel = const EventChannel('connectToNetwork');
  // StreamSubscription? wifiNetworkEventSubscription;
  // StreamSubscription? wifiDevicesEventSubscription;
  // List<WifiModel> wifiDeviceList = [];
  // WifiModel? connectedWifi;
  //
  // /// Enable the wifi
  // enableWifi() async {
  //   wifiMethodChannel.invokeMethod('enableWifi');
  // }
  //
  // Timer? stopListeningForWifiDevicesTimer;
  //
  // /// Call it when you require the list of available wifi
  // Future<void> scanForDevices({bool clearDevices = true}) async {
  //   isFetchingWifiDevices = true;
  //   if (clearDevices) {
  //     wifiDeviceList.clear();
  //   }
  //   notifyListeners();
  //
  //   /// Getting the connected wifi
  //   enableWifi();
  //   await getConnectedWifi();
  //   notifyListeners();
  //   stopListeningForWifiDevicesTimer?.cancel();
  //   stopListeningForWifiDevicesTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
  //     if (timer.tick == 60) {
  //       stopListeningForWifiDevicesTimer?.cancel();
  //       stopListeningForWifiDevicesTimer == null;
  //       wifiDevicesEventSubscription?.cancel();
  //       isFetchingWifiDevices = false;
  //       notifyListeners();
  //     }
  //   });
  //
  //   /// Gets the available wifi from native
  //   wifiDevicesEventSubscription = wifiDevicesEventChannel.receiveBroadcastStream().listen((wifiDevicesEvent) {
  //     WifiModel deviceModel = wifiModelFromJson(wifiDevicesEvent);
  //
  //     ///Bool is compared with false because required. Please don't remove.
  //     isFetchingWifiDevices = deviceModel.isScanning ?? true;
  //     if (deviceModel.deviceName != null && deviceModel.deviceName?.removeWhiteSpace != '') {
  //       /// stores the index number where if the wifi name is already existing
  //       int deviceIndex = wifiDeviceList.indexWhere((wifiDevice) => wifiDevice.deviceName == deviceModel.deviceName);
  //       if (deviceIndex == -1) {
  //         /// Stores if the wifi is new
  //         wifiDeviceList.add(deviceModel);
  //       } else {
  //         /// update  at the old index
  //         wifiDeviceList[deviceIndex] = deviceModel;
  //       }
  //     }
  //     notifyListeners();
  //   });
  // }
  //
  // /// Getting the list of connected wifi
  // Future<void> getConnectedWifi() async {
  //   connectedWifi = null;
  //   await wifiMethodChannel.invokeMethod('getConnectedWifi').then((wifi) {
  //     connectedWifi = wifiModelFromJson(wifi.toString());
  //   });
  //   showLog(connectedWifi?.toJson() ?? '');
  //   notifyListeners();
  // }
  //
  // /// Function will call  to authenticate the wifi
  // Future<void> authenticateWifi(BuildContext context, WidgetRef ref, String? networkSSID, String password, bool isSecure) async {
  //   showCommonSwitchingDialog(
  //     key: connectingDialogKey,
  //     context: context,
  //     iconWidget: CommonSVG(
  //       height: context.height * 0.07,
  //       strIcon: Assets.svgs.svgWifiLockThree.path,
  //     ),
  //     text: LocaleKeys.keyConnectingToWifi.localized,
  //   );
  //
  //   /// Platform channel use to connect to the wifi
  //   wifiMethodChannel.invokeMethod('connectToNetwork', {'networkSSID': networkSSID, 'password': password, 'isSecure': isSecure});
  //
  //   /// calling to handle the status of the wifi connection after invoking the connection wifi
  //   await handleConnectionStatus(context, ref, networkSSID, password);
  // }
  //
  // String? wifiConnectionReason;
  //
  // /// function to handle the status of the wifi connection after invoking the connection wifi
  // Future<void> handleConnectionStatus(BuildContext context, WidgetRef ref, String? networkSSID, String password) async {
  //   wifiConnectionReason = null;
  //   wifiNetworkEventSubscription = wifiNetworkEventChannel.receiveBroadcastStream().listen((connectionEvent) async {
  //     WifiConnectionResponseModel wifiConnection = wifiConnectionResponseModelFromJson(connectionEvent);
  //     if (wifiConnection.state != null) {
  //       switch (networkStateValues.map[wifiConnection.state]) {
  //         case null:
  //           break;
  //         case NetworkStateEnum.CONNECTING:
  //           if (wifiConnection.reason != null && wifiConnection.reason == DetailedState.AUTHENTICATING.name) {
  //             wifiConnectionReason = wifiConnection.reason;
  //           }
  //           break;
  //         case NetworkStateEnum.CONNECTED:
  //           if (connectingDialogKey.currentContext != null) {
  //             Navigator.pop(connectingDialogKey.currentContext!);
  //           }
  //           await getConnectedWifi();
  //           break;
  //         case NetworkStateEnum.SUSPENDED:
  //           break;
  //         case NetworkStateEnum.DISCONNECTING:
  //           break;
  //         case NetworkStateEnum.DISCONNECTED:
  //           wifiDevicesEventSubscription?.cancel();
  //           if (wifiConnectionReason != null && wifiConnectionReason == DetailedState.AUTHENTICATING.name) {
  //             if (connectingDialogKey.currentContext != null) {
  //               Navigator.pop(connectingDialogKey.currentContext!);
  //               showCommonErrorDialog(context: context, message: 'Wifi Password is Incorrect');
  //             }
  //           }
  //           break;
  //         case NetworkStateEnum.UNKNOWN:
  //           break;
  //       }
  //     }
  //     notifyListeners();
  //   });
  // }

  MethodChannel? wifiMethodChannel;
  EventChannel? wifiEventChannel;
  EventChannel? wifiConnectionChannel;
  bool isPasswordVisible = false;
  TextEditingController passwordController = TextEditingController();

  ///To initialize method channel and wifi controllers
  Future<void> initializeWifiChannel() async {
    /// Check Wifi Status
    wifiMethodChannel = MethodChannel(MethodChannelConstant.dasherWifiChannel);

    /// Enable -> Scan wifi
    wifiEventChannel = EventChannel(MethodChannelConstant.scanForDevices);

    /// Connect Wifi
    wifiConnectionChannel = EventChannel(MethodChannelConstant.connectToNetwork);
    checkIfWifiEnabled();
  }

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isWifiScanningStarted = false;
    passwordController.clear();
    if (isNotify) {
      notifyListeners();
    }
  }

//////------------------------- Method Channel Implementation ................./////////

  updatePasswordController({String? password}) {
    isPasswordVisible = false;
    if (password != null) {
      passwordController.text = password;
    } else {
      passwordController.clear();
    }
    notifyListeners();
  }

  /// Password Visibility
  updateIsPasswordVisible() {
    isPasswordVisible = !isPasswordVisible;
    notifyListeners();
  }

  bool isWifiEnabled = false;

  ///To check whether the wifi is enabled or not
  Future<void> checkIfWifiEnabled() async {
    isWifiEnabled = await wifiMethodChannel?.invokeMethod(MethodChannelConstant.isWifiEnable);
    notifyListeners();
  }

  ///To enable wifi
  Future<void> enableWifi() async {
    isWifiEnabled = await wifiMethodChannel?.invokeMethod(MethodChannelConstant.enableWifi);
    isWifiEnabled = true;
    scanForDevices();
    notifyListeners();
  }

  ///To get connected wifi
  Future getConnectedWifi() async {
    connectedWifi = null;
    Map<String, dynamic> deviceDataMap = ({});
    Map<Object?, Object?>? map = ({});
    map = await wifiMethodChannel?.invokeMethod(MethodChannelConstant.getConnectedWifi);
    if (map != null) {
      map.forEach((key, value) {
        deviceDataMap['$key'] = value;
      });
      connectedWifi = WifiDevicesListResponseModel.fromJson(deviceDataMap);
      if (connectedWifi?.hostIpAddress?.contains('127.0.0') ?? false) {
        connectedWifi = null;
      }

      /// TODO
      // RobotSession.hostIpAddress = connectedWifi?.hostIpAddress;
      // CanvasMapApiClass.api.initDio();
    }
    notifyListeners();
  }

  bool isConnecting = false;

  GlobalKey wifiConnectingDialogKey = GlobalKey();

  int? updatingWifiIndex;

  StreamSubscription? wifiConnectionStream;

  ///Connect to network
  Future<void> connectToNetwork({required String networkSSID, required String password, required WidgetRef ref}) async {
    isConnecting = true;
    connectedWifi = null;
    Map<String, dynamic> deviceDataMap = ({});
    updatingWifiIndex = wifiDevicesList.indexWhere((wifiDevice) => wifiDevice.networkName == networkSSID);
    await wifiMethodChannel?.invokeMethod(MethodChannelConstant.connectToNetwork, {
      'networkSSID': networkSSID,
      'password': password,
    });
    wifiConnectionStream = wifiConnectionChannel?.receiveBroadcastStream().listen((event) async {
      event?.forEach((key, value) {
        deviceDataMap['$key'] = value;
      });
      connectedWifi = WifiDevicesListResponseModel.fromJson(deviceDataMap);
      showLog('connectedWifi?.hostIpAddress ${connectedWifi?.hostIpAddress}');
      showLog('connectedWifi?.ipAddress ${connectedWifi?.ipAddress}');
      if (connectedWifi?.hostIpAddress != null) {
        /// TODO
        // RobotSession.hostIpAddress = connectedWifi?.hostIpAddress;
        // RobotSession.ipAddress = connectedWifi?.ipAddress;
        // RobotSession.wifiName = connectedWifi?.networkName;
      }
      if (connectedWifi?.isEndOfStream ?? false) {
        isConnecting = false;

        /// TODO
        // if (Session.getUserAccessToken().isNotEmpty) {
        //   await ref.read(authLoginController).saveSettingsApi(RobotSession.hostIpAddress ?? '', RobotSession.wifiName ?? '');
        // }
        // VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceSuccessfullyConnected);
        if (wifiConnectingDialogKey.currentContext != null) {
          Navigator.pop(wifiConnectingDialogKey.currentContext!);
        }
      }
      notifyListeners();
    });
  }

  ///To disable wifi
  Future<void> disableWifi() async {
    wifiSubscriptionStream?.cancel();
    await wifiMethodChannel?.invokeMethod(MethodChannelConstant.disableWifi).then((value) {
      isWifiScanningStarted = false;
      wifiDevicesList.clear();
      isWifiEnabled = false;
    });
    notifyListeners();
  }

  ///Start Wifi scanning
  StreamSubscription? wifiSubscriptionStream;

  /// Wifi List
  List<WifiDevicesListResponseModel> wifiDevicesList = [];

  /// Wifi Enables
  bool isWifiScanningStarted = false;

  WifiDevicesListResponseModel? connectedWifi;

  /// Scan for wifi
  Future<void> scanForDevices({Function(Object? event)? eventCallback, Function(Object error)? errorCallBack}) async {
    /// Get Connected Wifi details
    getConnectedWifi();

    /// Getting List of available devices
    wifiSubscriptionStream = wifiEventChannel?.receiveBroadcastStream().listen((Object? event) async {
      eventCallback?.call(event);
      Map<String, dynamic> deviceDataMap = ({});
      (event as Map<Object?, Object?>).forEach((key, value) {
        deviceDataMap['$key'] = value;
      });

      showLog('Scanning devices for wifi $deviceDataMap');
      WifiDevicesListResponseModel wifiDevicesListResponseModel = WifiDevicesListResponseModel.fromJson(deviceDataMap);
      isWifiScanningStarted = wifiDevicesListResponseModel.isScanning ?? false;
      notifyListeners();
      //If the device is already in list, then it should replace it
      if (wifiDevicesListResponseModel.networkName != null && (wifiDevicesListResponseModel.networkName?.isNotEmpty ?? false)) {
        int wifiDeviceIndex = wifiDevicesList.indexWhere((wifiDevice) => (wifiDevice.networkName == wifiDevicesListResponseModel.networkName));
        if (wifiDeviceIndex != -1) {
          /// Update Wifi DTO
          wifiDevicesList[wifiDeviceIndex] = wifiDevicesListResponseModel;
        } else {
          /// Add Wifi DTO
          wifiDevicesList.add(wifiDevicesListResponseModel);
        }
      }

      /// Remove Wifi
      wifiDevicesList.removeWhere((wifiDevice) => wifiDevice.networkName == connectedWifi?.networkName);
      notifyListeners();
    }, onError: (Object error) {
      errorCallBack?.call(error);
    });
  }

/*
  /// ---------------------------- Api Integration ---------------------------------///
   */
}
