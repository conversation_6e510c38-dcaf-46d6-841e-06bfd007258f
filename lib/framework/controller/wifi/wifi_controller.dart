import 'dart:async';

import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/repository/wifi/wifi_devices_list_response_model.dart';
import 'package:odigo_display/framework/utils/helper/voice_helper.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/const/method_channel_const.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

final wifiController = ChangeNotifierProvider((ref) => getIt<WifiController>());

@injectable
class WifiController extends ChangeNotifier {
  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isWifiScanningStarted = false;
    if (isNotify) {
      notifyListeners();
    }
  }

//////------------------------- Method Channel Implementation ................./////////
  MethodChannel? wifiMethodChannel;
  EventChannel? wifiEventChannel;
  EventChannel? wifiConnectionChannel;
  bool isPasswordVisible = false;
  TextEditingController passwordController = TextEditingController();

  updatePasswordController({String? password}) {
    isPasswordVisible = false;
    if (password != null) {
      passwordController.text = password;
    } else {
      passwordController.clear();
    }
    notifyListeners();
  }

  updateIsPasswordVisible() {
    isPasswordVisible = !isPasswordVisible;
    notifyListeners();
  }

  ///To initialize method channel and wifi controllers
  Future<void> initializeWifiChannel() async {
    showLog('initializeWifiChannel Called');
    wifiMethodChannel = MethodChannel(MethodChannelConstant.dasherWifiChannel);
    wifiEventChannel = EventChannel(MethodChannelConstant.scanForDevices);
    wifiConnectionChannel = EventChannel(MethodChannelConstant.connectToNetwork);
    checkIfWifiEnabled();
  }

  bool isWifiEnabled = false;

  ///To check whether the wifi is enabled or not
  Future<void> checkIfWifiEnabled() async {
    isWifiEnabled = await wifiMethodChannel?.invokeMethod(MethodChannelConstant.isWifiEnable);
    notifyListeners();
  }

  ///To enable wifi
  Future<void> enableWifi() async {
    isWifiEnabled = await wifiMethodChannel?.invokeMethod(MethodChannelConstant.enableWifi);
    isWifiEnabled = true;

    scanForDevices();
    notifyListeners();
  }

  ///To get connected wifi
  Future getConnectedWifi() async {
    showLog('getConnectedWifi Called');
    connectedWifi = null;
    Map<String, dynamic> deviceDataMap = ({});
    Map<Object?, Object?> map = ({});
    map = await wifiMethodChannel?.invokeMethod(MethodChannelConstant.getConnectedWifi);
    map.forEach((key, value) {
      showLog('deviceDataMap[key] ${deviceDataMap['$key']}');
      showLog('deviceDataMap[value] $value');
      deviceDataMap['$key'] = value;
    });
    connectedWifi = WifiDevicesListResponseModel.fromJson(deviceDataMap);
    if (connectedWifi?.hostIpAddress?.contains('127.0.0') ?? false) {
      connectedWifi = null;
    }

    RobotSession.hostIpAddress = connectedWifi?.hostIpAddress;
    RobotSession.ipAddress = connectedWifi?.ipAddress;
    RobotSession.wifiName = connectedWifi?.networkName;
    // RobotSession.wifiName = 'ROBOT_ROUTER';
    showLog('RobotSession.hostIpAddress test ${RobotSession.hostIpAddress}');
    showLog('RobotSession.networkName test ${connectedWifi?.networkName}');
    showLog('RobotSession.hostDeviceName test ${connectedWifi?.hostDeviceName}');
    notifyListeners();
  }

  bool isConnecting = false;

  GlobalKey wifiConnectingDialogKey = GlobalKey();

  int? updatingWifiIndex;

  StreamSubscription? wifiConnectionStream;

  ///Connect to network
  Future<void> connectToNetwork({required String networkSSID, required String password}) async {
    isConnecting = true;
    connectedWifi = null;
    Map<String, dynamic> deviceDataMap = ({});
    updatingWifiIndex = wifiDevicesList.indexWhere((wifiDevice) => wifiDevice.networkName == networkSSID);
    await wifiMethodChannel?.invokeMethod(MethodChannelConstant.connectToNetwork, {
      'networkSSID': networkSSID,
      'password': password,
    });
    wifiConnectionStream = wifiConnectionChannel?.receiveBroadcastStream().listen((event) async {
      showLog("event wifi connection steam $event");
      event?.forEach((key, value) {
        deviceDataMap['$key'] = value;
      });
      connectedWifi = WifiDevicesListResponseModel.fromJson(deviceDataMap);
      if (connectedWifi?.hostIpAddress != null) {
        RobotSession.hostIpAddress = connectedWifi?.hostIpAddress;
      }
      notifyListeners();
      if (connectedWifi?.isEndOfStream ?? false) {
        isConnecting = false;
        // if (Session.getUserAccessToken().isNotEmpty) {
        // await globalRef?.read(authLoginController).saveSettingsApi({"androidId": RobotSession.androidId, "ipAddress": RobotSession.hostIpAddress});
        // }
        notifyListeners();
        VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceSuccessfullyConnected);
        if (wifiConnectingDialogKey.currentContext != null) {
          Navigator.pop(wifiConnectingDialogKey.currentContext!);
        }
      }
      notifyListeners();
    });
  }

  ///To disable wifi
  Future<void> disableWifi() async {
    wifiSubscriptionStream?.cancel();
    await wifiMethodChannel?.invokeMethod(MethodChannelConstant.disableWifi).then((value) {
      isWifiScanningStarted = false;
      wifiDevicesList.clear();
      isWifiEnabled = false;
    });
    notifyListeners();
  }

  ///Start Wifi scanning
  StreamSubscription? wifiSubscriptionStream;
  List<WifiDevicesListResponseModel> wifiDevicesList = [];
  bool isWifiScanningStarted = false;

  WifiDevicesListResponseModel? connectedWifi;

  Future<void> scanForDevices({Function(Object? event)? eventCallback, Function(Object error)? errorCallBack}) async {
    getConnectedWifi();
    wifiSubscriptionStream = wifiEventChannel?.receiveBroadcastStream().listen((Object? event) async {
      eventCallback?.call(event);
      Map<String, dynamic> deviceDataMap = ({});
      (event as Map<Object?, Object?>).forEach((key, value) {
        deviceDataMap['$key'] = value;
      });
      WifiDevicesListResponseModel wifiDevicesListResponseModel = WifiDevicesListResponseModel.fromJson(deviceDataMap);
      isWifiScanningStarted = wifiDevicesListResponseModel.isScanning ?? false;
      notifyListeners();
      //If the device is already in list, then it should replace it
      if (wifiDevicesListResponseModel.networkName != null && (wifiDevicesListResponseModel.networkName?.isNotEmpty ?? false)) {
        int wifiDeviceIndex = wifiDevicesList.indexWhere((wifiDevice) => (wifiDevice.networkName == wifiDevicesListResponseModel.networkName));
        if (wifiDeviceIndex != -1) {
          wifiDevicesList[wifiDeviceIndex] = wifiDevicesListResponseModel;
        } else {
          wifiDevicesList.add(wifiDevicesListResponseModel);
        }
      }
      wifiDevicesList.removeWhere((wifiDevice) => wifiDevice.networkName == connectedWifi?.networkName);
      notifyListeners();
    }, onError: (Object error) {
      errorCallBack?.call(error);
    });
  }

  @override
  void notifyListeners() {
    super.notifyListeners();
  }
}
