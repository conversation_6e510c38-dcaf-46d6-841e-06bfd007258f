import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/ui/wifi/web/helper/robot_session.dart';

class ApiEndPoints {
  /*
  * ----- Api status
  * */
  static const int apiStatus_200 = 200; //success
  static const int apiStatus_201 = 201; //success
  static const int apiStatus_202 = 202; //success for static page
  static const int apiStatus_203 = 203; //success
  static const int apiStatus_205 = 205; // for remaining step 2
  static const int apiStatus_400 = 400; //Invalid data
  static const int apiStatus_401 = 401; //Invalid data
  static const int apiStatus_404 = 404; //Invalid data

  ///CMS Module
  static String cms = 'vendor_cms';
  static String faqs = 'faqs';

  static String signIn = 'auth/signin';

  /// Get Robot Controllers and Store list
  static String getRobotMetaData = 'http://49.13.228.207:9901/odigo/json/robot';

  /// Login
  // static String loginAPI =                             '/login';

  /// Sync Data
  // TODO:(24/07/24) <PERSON><PERSON> change url
  // static String getSyncData                         = 'https://dummyjson.com/c/2bca-f6ac-4fd9-8c92';
  // static String getSyncData                            = 'https://mocki.io/v1/1767851c-32cc-44ef-9593-4a6c9f914e04'; // timeslot: 10:00am to 23:45pm
  // static String getSyncData                            = 'https://dummyjson.com/c/f027-b02d-4859-9e99'; // timeslot: 10:00am to 19:00pm
  static String getSyncData = '/robot/offline/sequence'; // timeslot: 10:00am to 19:00pm
  // static String getSyncData                            = 'https://dummyjson.com/c/a04b-f25c-4d6f-a527'; // timeslot: 11:37am to 21:00pm
  // static String getSyncData                            = 'https://dummyjson.com/c/6e29-5ea7-4936-9cfd'; // timeslot: 11:45am to 15:00pm
  // static String getSyncData                            = 'https://dummyjson.com/c/ee2f-2987-4582-87d5'; // timeslot: 11:37am to 15:00pm
  // static String getSyncData                         = 'https://dummyjson.com/c/cb10-89d7-4ecd-acb1';//timeslot - 10 to 20
  // static String getSyncData                         = 'https://dummyjson.com/c/cb10-89d7-4ecd-acb1';//timeslot - 10 to 20
  // static String getSyncData                         = 'https://dummyjson.com/c/408d-29e5-41dc-aaf4';//timeslot - 11:30 to 19:30
  // static String getSyncData                         = 'https://dummyjson.com/c/574f-1057-4e2b-bf63';//timeslot - 15:30:00 - 19:30:00
  // static String getSyncData                         = 'https://dummyjson.com/c/3790-d248-4242-b9f0';//timeslot - 15:30:00 - 19:30:00

  // TODO:(24/07/24) Manthan change url
  static String deleteAdsAPI = 'http://49.13.228.207:9901/odigo/json/robot';

  // TODO:(24/07/24) manthan change url
  static String sendAdsData = 'http://49.13.228.207:9901/odigo/json/robot';
  // static String deductWalletBalance = '/package/wallet/debit';
  static String deductWalletBalance = '/package/wallet/transaction/offline/debit';

  static String getSettings({String? settingsKey, required String? robotUuid}) => '/offline/settings/table${settingsKey == null ? '' : '?key=$settingsKey'}&robotUuid=$robotUuid';

  static String saveSettings({String? settingsKey}) => '/offline/settings/table${settingsKey == null ? '' : '?key=$settingsKey'}';

  ///Delete Last Week Archive Ads
  static String deleteArchiveAdsAPI = '/ads/offline/delete';
  static String robotList = '/robot/list/dms';

  ///AES - RSA
  static String loginSecret = '/login/secret';
  static String loginEncryption = '/login/enc';

  static String robot_listing = '/robot/offline/destination/list?activeRecords=true&destinationUuid=${SessionRepository.session.destinationUuid}';




  ///ROBOT APIS IP >>>>>>>>>>>>>>>>
  ///Robot Api
  static String getRobotWayPoints = 'http://${RobotSession.hostIpAddress}/reeman/position';
  static String getRobotRoutes = 'http://${RobotSession.hostIpAddress}/reeman/navi_routes';
  static String saveRoute = 'http://${RobotSession.hostIpAddress}/cmd/save_routes';
  static String getHostName = 'http://${RobotSession.hostIpAddress}/reeman/hostname';

  ///Default Api
  ///Auth Api
  static String authLogin = '/otp/generate';
  static String verifyAuthLogin = '/login/robot/default';

  /// Map
  static String getMapList = 'http://${RobotSession.hostIpAddress}/reeman/history_map';
  static String getIpCurrentMap = 'http://${RobotSession.hostIpAddress}/reeman/current_map';
  static String uploadNewMap = 'http://${RobotSession.hostIpAddress}/upload/import_map';
  static String exportMap = 'http://${RobotSession.hostIpAddress}/download/export_map';
  static String getVirtualWall = 'http://${RobotSession.hostIpAddress}/reeman/restrict_layer';

  ///Map Api
  static String addMap = '/offline/maps';
  static String updateMap = '/offline/maps';

  static String getCurrentMap(String robotUuid) => '/offline/maps/current/$robotUuid';

  static String getAllMaps({required bool activeRecords, required String robotUuid}) => '/offline/maps/list?activeRecords=$activeRecords&robotUuid=$robotUuid';

  static String uploadMapImage(String mapName) => '/offline/maps/image/upload/$mapName';

  static String setCurrentMap(String mapUuid) => '/offline/maps/change/current/$mapUuid';

  static String deleteMap(String mapName) => '/offline/maps/status/$mapName?active=false';

  static String deleteMapLocal = '/cmd/delete_map';

  static String deleteMapFromRecycle(String mapName) => '/offline/maps/status/$mapName?active=false';

  static String restoreMapFromRecycle(String mapName) => '/offline/maps/status/$mapName?active=true';

  static String updateCurrentMap(String mapName) => '/offline/maps/change/current/$mapName';

  static String addWayPaths = '/offline/way/paths';

  static String deleteAllWayPointsForMap(String mapUuid) => '/way/points/maps/$mapUuid';

  static String addDeliveryPoints = '/offline/way/points';

  static String deleteDeliveryPoints(String pointName) => '/way/points/$pointName';

  static String odigoStoreList(String? destinationUuid, String? robotUuid) => '/odigo/store/offline/list/destination/$destinationUuid?pageNumber=1&pageSize=1000&activeRecords=true&robotUuid=$robotUuid';
  // static String odigoStoreList(String? destinationUuid, String? robotUuid) => '/odigo/store/list/robot/$destinationUuid?pageNumber=1&pageSize=1000&activeRecords=true&robotUuid=$robotUuid';

  /// Destination Details
  static String getDestinationDetails(String destinationUuid) => '/destination/$destinationUuid';

  static String odigoStoreDetails(String storeUuid) => '/odigo/store/offline/$storeUuid';
  static String addLocation = '/offline/location';

  static String locationDetails(String locationUuid) => '/location/language/$locationUuid';
  static String deleteLocation(String locationUuid) => '/offline/location/$locationUuid';
  static String deleteLocationByFloorNumber(String floorNumber) => '/offline/location?floorNumber=$floorNumber';
  static String locationList = '/offline/location/list';
  static String addLocationPoint = '/offline/location/points';

  /// Delete Server Map Apis--------------------------
  static String deleteMapByUuid(String robotUuid) => '/offline/maps/$robotUuid';

  static String changeMapStatusByUuid(String robotUuid, bool isActive) => '/offline/maps/status/$robotUuid?active=$isActive';

  /// Check Sequence Api
  static String checkSequence(bool isRead) => '/robot/offline/sequence/check?isRead=$isRead';

  /// Deployment Version ==> For Install New Apk That is available in server
  static String checkDeploymentVersion = "/deployment/offline/version";

  static String interactionAPI = '/offline/user/interaction';

  static String getLanguageList = '/language?activeRecords=true';
}
