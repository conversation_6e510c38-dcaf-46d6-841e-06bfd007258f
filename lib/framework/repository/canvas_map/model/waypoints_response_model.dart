// To parse this JSON data, do
//
//     final wayPointsResponseModel = wayPointsResponseModelFromJson(jsonString);

import 'dart:convert';

import 'package:odigo_display/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
export 'package:odigo_display/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
import 'package:odigo_display/ui/utils/const/app_enums.dart';



WayPointsResponseModel wayPointsResponseModelFromJson(String str) => WayPointsResponseModel.fromJson(json.decode(str));

String wayPointsResponseModelToJson(WayPointsResponseModel data) => json.encode(data.toJson());

class WayPointsResponseModel {
  List<Waypoint> waypoints;

  WayPointsResponseModel({
    required this.waypoints,
  });

  factory WayPointsResponseModel.fromJson(Map<String, dynamic> json) => WayPointsResponseModel(
        waypoints: List<Waypoint>.from(json['waypoints']!.map((x) => Waypoint.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'waypoints': List<dynamic>.from(waypoints.map((x) => x.toJson())),
      };
}
