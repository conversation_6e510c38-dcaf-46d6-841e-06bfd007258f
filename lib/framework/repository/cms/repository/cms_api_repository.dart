import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/cms/contract/cms_repository.dart';
import 'package:odigo_display/framework/repository/cms/model/cms_response_model.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';

@LazySingleton(as: CMSRepository, env: [development, production])
class CMSApiRepository implements CMSRepository {
  final DioClient apiClient;

  CMSApiRepository(this.apiClient);

  /// cms Api
  @override
  Future cmsApi() async{
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.cms);
      CmsResponseModel responseModel = cmsResponseModelFromJson(response.toString());
      if (responseModel.status == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

}
