// To parse this JSON data, do
//
//     final navigationEvent = navigationEventFromJson(jsonString);

import 'dart:convert';

NavigationEvent navigationEventFromJson(String str) => NavigationEvent.fromJson(json.decode(str));

String navigationEventToJson(NavigationEvent data) => json.encode(data.toJson());

class NavigationEvent {
  String? locationPoint;
  NavigationState? navigationState;
  String? type;
  int? code;

  NavigationEvent({
    this.locationPoint,
    this.navigationState,
    this.type,
    this.code,
  });

  factory NavigationEvent.fromJson(Map<String, dynamic> json) => NavigationEvent(
        locationPoint: json['locationPoint'],
        navigationState: navigationStateValues.map[json['navigationState']] ?? NavigationState.INITIAL,
        type: json['type'],
        code: json['code'],
      );

  Map<String, dynamic> toJson() => {
        'locationPoint': locationPoint,
        'navigationState': navigationState,
        'type': type,
        'code': code,
      };
}

enum NavigationState {
  FAILURE,
  INITIAL,
  START,
  PAUSE,
  COMPLETE,
  CANCEL,
  RESUME,
  RECEIVE,
}

final navigationStateValues = NavigationStateValues<NavigationState>({
  -1: NavigationState.FAILURE,
  0: NavigationState.INITIAL,
  1: NavigationState.START,
  2: NavigationState.PAUSE,
  3: NavigationState.COMPLETE,
  4: NavigationState.CANCEL,
  5: NavigationState.RESUME,
  6: NavigationState.RECEIVE,
});

class NavigationStateValues<T> {
  Map<int, T> map;
  late Map<T, int> reverseMap;

  NavigationStateValues(this.map);

  Map<T, int> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
