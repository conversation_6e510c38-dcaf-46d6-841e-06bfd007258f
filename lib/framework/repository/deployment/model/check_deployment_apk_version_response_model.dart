// To parse this JSON data, do
//
//     final checkDeploymentApkVersionResponseModel = checkDeploymentApkVersionResponseModelFromJson(jsonString);

import 'dart:convert';

CheckDeploymentApkVersionResponseModel checkDeploymentApkVersionResponseModelFromJson(String str) => CheckDeploymentApkVersionResponseModel.fromJson(json.decode(str));

String checkDeploymentApkVersionResponseModelToJson(CheckDeploymentApkVersionResponseModel data) => json.encode(data.toJson());

class CheckDeploymentApkVersionResponseModel {
  String? message;
  Data? data;
  int? status;

  CheckDeploymentApkVersionResponseModel({
    this.message,
    this.data,
    this.status,
  });

  factory CheckDeploymentApkVersionResponseModel.fromJson(Map<String, dynamic> json) => CheckDeploymentApkVersionResponseModel(
    message: json["message"],
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "data": data?.toJson(),
    "status": status,
  };
}

class Data {
  String? uuid;
  String? buildUrl;
  String? robotUuid;
  double? version;
  String? technology;
  String? buildType;

  Data({
    this.uuid,
    this.buildUrl,
    this.robotUuid,
    this.version,
    this.technology,
    this.buildType,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    uuid: json["uuid"],
    buildUrl: json["buildUrl"],
    robotUuid: json["robotUuid"],
    version: json["version"]?.toDouble(),
    technology: json["technology"],
    buildType: json["buildType"],
  );

  Map<String, dynamic> toJson() => {
    "uuid": uuid,
    "buildUrl": buildUrl,
    "robotUuid": robotUuid,
    "version": version,
    "technology": technology,
    "buildType": buildType,
  };
}
