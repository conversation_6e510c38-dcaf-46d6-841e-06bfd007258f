import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/encryption/contract/encryption_repository.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';

@LazySingleton(as: EncryptionRepository, env: [development, production])
class EncryptionApiRepository implements EncryptionRepository {
  DioClient apiClient;

  EncryptionApiRepository(this.apiClient);

  @override
  Future<ApiResult<String>> getBackendPublicKey() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.loginSecret);
      if (response != null && response.statusCode == ApiEndPoints.apiStatus_200) {
        showLog("str >>>> ${jsonDecode(response.toString())['data']}");
        return ApiResult.success(data: jsonDecode(response.toString())['data']);
        // return jsonDecode(response.toString())['data'];
      }else{
        return ApiResult.failure(error: NetworkExceptions.getDioException(response?.statusMessage));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }
}
