import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

@Entity()
class NavigationInterruption {
  @Id()
  int navigationInterruptionId;
  DateTime startedInterruptionTime;
  DateTime? endedInterruption;
  String navigationType;
  final syncedData = ToOne<SyncedData>();
  String? storeUuid;
  String? vendorUuid;
  String? storeName;
  bool successfullyNavigated;
  // int? duration;
  /// When Dialog appears for getting confirmation about store navigation at that time if user clicks on
  /// Yes -> it will be true
  /// No -> It will be false
  bool? isRequestSubmitted;

  NavigationInterruption({
    this.navigationInterruptionId = 0,
    required this.startedInterruptionTime,
    this.endedInterruption,
    required this.navigationType,
    required this.storeUuid,
    required this.storeName,
    required this.successfullyNavigated,
    required this.vendorUuid,
    // this.duration,
    this.isRequestSubmitted,
  });

}
