// To parse this JSON data, do
//
//     final ipCurrentMapResponseModel = ipCurrentMapResponseModelFromJson(jsonString);

import 'dart:convert';

IpCurrentMapResponseModel ipCurrentMapResponseModelFromJson(String str) => IpCurrentMapResponseModel.fromJson(json.decode(str));

String ipCurrentMapResponseModelToJson(IpCurrentMapResponseModel data) => json.encode(data.toJson());

class IpCurrentMapResponseModel {
  String? name;
  String? alias;

  IpCurrentMapResponseModel({
    this.name,
    this.alias,
  });

  factory IpCurrentMapResponseModel.fromJson(Map<String, dynamic> json) => IpCurrentMapResponseModel(
    name: json["name"],
    alias: json["alias"],
  );

  Map<String, dynamic> toJson() => {
    "name": name,
    "alias": alias,
  };
}
