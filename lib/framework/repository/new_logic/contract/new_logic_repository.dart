import 'package:odigo_display/framework/repository/new_logic/model/agency_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/category_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/client_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/elevator_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/vendor_data.dart';
import 'package:odigo_display/framework/utils/helper/objectbox_response_model.dart';

abstract class NewLogicRepository {
  Future<void> clearAllData();

  Future<ObjectBoxResponseModel<SyncedData?>?> storeJsonInLocalDB(SyncedData syncedData, Function? onError);

  Future<void> syncTimeSlots(SyncedData syncedData);

  Future<bool> syncEntities({required List<ClientData> clientList, required List<VendorData> vendorList, required List<AgencyData> agencyList});

  Future<List<VendorData>> getVendorList();

  Future<void> syncDistinctAds(List<AdDetails> adDetails);

  Future<void> syncStores(List<StoreData> storeList);

  Future<void> syncElevator(List<ElevatorData> elevatorList);

  Future<List<CategoryData>> syncCategories(List<CategoryData> categoryList);

  Future<ObjectBoxResponseModel<List<AdSequence>?>?> syncAdSequence(List<AdSequence> adSequenceList);

  Future<List<AdDetails>?> getDefaultAds(int syncedDataId);

  Future<List<AdDetails>?> getSequencedAd(int syncedDataId);

  Future<ObjectBoxResponseModel<StoreData?>> getStoreVideosByStoreId(String id);

  Future<AdDetails?> getDefaultAssetAd();

  Future<AdDetails?> createDefaultAssetAd(AdDetails adDetails);
}
