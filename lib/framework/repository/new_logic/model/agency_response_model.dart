import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

@Entity()
class AgencyData {
  @Id()
  int agencyId;
  String agencyUuid;
  String agencyName;
  @Property(type: PropertyType.float)
  double walletBalance;
  @Transient()
  final assignAdDetails = ToMany<AdDetails>();
  @Transient()
  double? storageUsedSize;
  bool isDeleted;

  AgencyData({
    this.agencyId = 0,
    required this.agencyUuid,
    required this.agencyName,
    required this.walletBalance,
    this.storageUsedSize,
    this.isDeleted = false,
  });

  factory AgencyData.fromJson(Map<String, dynamic> json) {
    AgencyData agencyData = AgencyData(
      agencyUuid: json["entityUuid"],
      agencyName: json["name"],
      walletBalance: json["walletBalance"],
      storageUsedSize: json["storageUsedSize"],
    );
    List<AdDetails> assignAdDetails = json['assignAdDetails'] == null ? [] : List<AdDetails>.from(json['assignAdDetails']!.map((x) => AdDetails.fromJson(x)));
    agencyData.assignAdDetails.clear();
    agencyData.assignAdDetails.addAll(assignAdDetails);
    return agencyData;
  }

  AgencyData copyWith({
    required int agencyId,
    String? agencyUuid,
    String? agencyName,
    double? walletBalance,
    double? storageUsedSize,
    bool? isDeleted,
  }) {
    return AgencyData(
      agencyId: agencyId,
      agencyUuid: agencyUuid ?? this.agencyUuid,
      agencyName: agencyName ?? this.agencyName,
      walletBalance: walletBalance ?? this.walletBalance,
      storageUsedSize: storageUsedSize ?? this.storageUsedSize,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}
