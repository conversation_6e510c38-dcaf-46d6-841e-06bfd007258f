import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/agency_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

@Entity()
class ClientData {
  @Id()
  int clientId;
  String clientUuid;
  String agencyUuid;
  String agencyName;
  String clientName;
  @Property(type: PropertyType.float)
  double walletBalance;
  @Transient()
  final assignAdDetails = ToMany<AdDetails>();
  final agencyData = ToOne<AgencyData>();
  @Transient()
  double? storageUsedSize;
  bool isDeleted;

  ClientData({
    this.clientId = 0,
    required this.clientUuid,
    required this.agencyUuid,
    required this.agencyName,
    required this.clientName,
    required this.walletBalance,
    this.storageUsedSize,
    this.isDeleted = false,
  });

  factory ClientData.fromJson(Map<String, dynamic> json) {
    ClientData client = ClientData(
      clientUuid: json["entityUuid"],
      agencyName: json["agencyName"],
      agencyUuid: json["agencyUuid"],
      clientName: json["name"],
      walletBalance: json["walletBalance"],
      storageUsedSize: json["storageUsedSize"],
    );
    List<AdDetails> assignAdDetails = json['assignAdDetails'] == null ? [] : List<AdDetails>.from(json['assignAdDetails']!.map((x) => AdDetails.fromJson(x)));
    client.assignAdDetails.clear();
    client.assignAdDetails.addAll(assignAdDetails);
    return client;
  }

  ClientData copyWith({
    required int clientId,
    String? clientUuid,
    String? agencyUuid,
    AgencyData? agencyData,
    String? agencyName,
    String? clientName,
    double? walletBalance,
    double? storageUsedSize,
    bool? isDeleted,
  }) {
    ClientData clientData = ClientData(
      clientId: clientId,
      clientUuid: clientUuid ?? this.clientUuid,
      agencyUuid: agencyUuid ?? this.agencyUuid,
      agencyName: agencyUuid ?? this.agencyName,
      clientName: clientName ?? this.clientName,
      walletBalance: walletBalance ?? this.walletBalance,
      storageUsedSize: storageUsedSize ?? this.storageUsedSize,
      isDeleted: isDeleted ?? this.isDeleted,
    );
    clientData.agencyData.target = agencyData ?? this.agencyData.target;
    return clientData;
  }
}
