import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

class DownloadAdModel {
  String uuid;
  AdDetails? adDetails;
  double downloadPercentage;
  double downloadSize;
  String? downloadTaskId;
  String filePath;
  String directoryPath;
  bool isDownloadFailed;
  bool isUnzipFailed;
  bool isRetried;
  bool isUnzipped;
  bool isUnZipping;
  int retryCount;

  DownloadAdModel({
    required this.uuid,
    required this.adDetails,
    this.downloadSize = 1,
    this.downloadPercentage = 0,
    this.downloadTaskId,
    this.filePath = '',
    this.directoryPath = '',
    this.isDownloadFailed = false,
    this.isRetried = false,
    this.isUnzipped = false,
    this.isUnZipping = false,
    this.isUnzipFailed = true,
    this.retryCount = 0,
  });
}

class DownloadAssetModel {
  String uuid;
  String downloadUrl;
  double downloadSize;
  double downloadPercentage;
  String? downloadTaskId;
  String filePath;
  String fileName;
  String name;
  String directoryPath;
  bool isDownloadFailed;
  bool isRetried;
  bool isDownloaded;
  AssetType? assetType;
  int retryCount;

  DownloadAssetModel({
    required this.uuid,
    required this.downloadUrl,
    this.downloadSize = 1,
    this.downloadPercentage = 0,
    this.downloadTaskId,
    this.filePath = '',
    this.directoryPath = '',
    this.fileName = '',
    this.name = '',
    this.isDownloadFailed = false,
    this.isRetried = false,
    this.isDownloaded = false,
    this.assetType,
    this.retryCount = 0,
  });
}

enum AssetType { CATEGORY, STORE }
