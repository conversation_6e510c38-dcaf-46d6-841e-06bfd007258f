import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';

@Entity()
class ElevatorData {
  @Id()
  int elevatorId;
  @Index()
  String? uuid;
  String? name;
  int? floorNumber;
  @Transient()
  List<LocationPointData>? locationPoints;
  bool isDeleted;

  ElevatorData({
    this.elevatorId = 0,
    this.uuid,
    this.name,
    this.floorNumber,
    this.isDeleted = false,
  });

  factory ElevatorData.fromJson(Map<String, dynamic> json) {
    ElevatorData storeData = ElevatorData(
      uuid: json["uuid"],
      name: json["name"],
      floorNumber: json["floorNumber"],
    );
    List<LocationPointData> locationPoints = json['locationPoints'] == null ? [] : List<LocationPointData>.from(json['locationPoints']!.map((x) => LocationPointData.fromJson(x)));
    storeData.locationPoints = locationPoints;
    return storeData;
  }

  Map<String, dynamic> toJson() => {
    "elevatorId": elevatorId,
    "uuid": uuid,
    "name": name,
    "floorNumber": floorNumber,
    "locationPoints": List<dynamic>.from(locationPoints!.map((x) => x.toJson())),
  };

  ElevatorData copyWith({
    required int elevatorId,
    String? uuid,
    String? name,
    int? floorNumber,
    bool? isDeleted,
  }) {
    ElevatorData elevatorData = ElevatorData(
      elevatorId: elevatorId,
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      floorNumber: floorNumber ?? this.floorNumber,
      isDeleted: isDeleted ?? this.isDeleted,
    );
    return elevatorData;
  }
}
