import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/category_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/elevator_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/utils/extension/string_extension.dart';

GetSyncResponseModel getSyncResponseModelFromJson(String str) => GetSyncResponseModel.fromJson(json.decode(str));

String getSyncResponseModelToJson(GetSyncResponseModel data) => json.encode(data.toJson());

class GetSyncResponseModel {
  bool? success;
  int? status;
  String? message;
  SyncedData? data;

  GetSyncResponseModel({
    this.success,
    this.status,
    this.message,
    this.data,
  });

  factory GetSyncResponseModel.fromJson(Map<String, dynamic> json) => GetSyncResponseModel(
        success: json['success'],
        status: json['status'],
        message: json['message'],
        data: json["data"] == null ? null : SyncedData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        'success': success,
        'status': status,
        'message': message,
        'data': data?.toJson(),
      };
}

@Entity()
class SyncedData {
  @Id()
  int syncedDataId;
  String date;
  String? waypaths;
  @Backlink("SyncedData")
  List<ProductionPoint>? productionPoints;
  List<ChargingPoint>? chargingPoints;
  @Transient()
  var destinationDetails = ToOne<DestinationDetails>();
  @Transient()
  var robotMetaData = ToOne<RobotMetaData>();
  @Backlink("SyncedData")
  final totalTimeSlots = ToMany<TimeSlotModel>();
  @Transient()
  @Backlink("SyncedData")
  List<StoreData>? stores;
  List<ElevatorData>? elevators;
  @Transient()
  List<CategoryData>? categories;
  @Transient()
  List<AdSequence>? adSequences;
  @Transient()
  List<AdDetails>? defaultAdDetails;

  SyncedData({
    this.syncedDataId = 0,
    required this.date,
    required this.waypaths,
  });

  factory SyncedData.fromJson(Map<String, dynamic> json) {
    SyncedData syncedData = SyncedData(
      date: json["date"] ?? DateFormat('dd/MM/yyyy').format(DateTime.now()).convertToOnlyDateFormat,
      waypaths: json["waypaths"],
    );
    syncedData.destinationDetails.target = DestinationDetails.fromJson(json["destinationDetails"]);
    syncedData.robotMetaData.target = RobotMetaData.fromJson(json['robotMetadata']);
    List<AdSequence> adSequences = json['adSequences'] == null ? [] : List<AdSequence>.from(json['adSequences']!.map((x) => AdSequence.fromJson(x)));
    if (syncedData.adSequences == null) {
      syncedData.adSequences = adSequences;
    } else {
      syncedData.adSequences?.clear();
      syncedData.adSequences?.addAll(adSequences);
    }
    List<AdDetails> adDetails = json['destinationDetails']?['defaultAds'] == null ? [] : List<AdDetails>.from(json['destinationDetails']['defaultAds']!.map((x) => AdDetails.fromJson(x)));
    if (syncedData.defaultAdDetails == null) {
      syncedData.defaultAdDetails = adDetails;
    } else {
      syncedData.defaultAdDetails?.clear();
      syncedData.defaultAdDetails?.addAll(adDetails);
    }
    List<StoreData> stores = json['storePointsOnDestination'] == null ? [] : List<StoreData>.from(json['storePointsOnDestination']!.map((x) => StoreData.fromJson(x)));
    if (syncedData.stores == null) {
      syncedData.stores = stores;
    } else {
      syncedData.stores?.clear();
      syncedData.stores?.addAll(stores);
    }
    List<ElevatorData> elevators = json['elevatorPoints'] == null ? [] : List<ElevatorData>.from(json['elevatorPoints']!.map((x) => ElevatorData.fromJson(x)));
    if (syncedData.elevators == null) {
      syncedData.elevators = elevators;
    } else {
      syncedData.elevators?.clear();
      syncedData.elevators?.addAll(elevators);
    }
    List<ProductionPoint> productionPoints = json['productionPoints'] == null ? [] : List<ProductionPoint>.from(json['productionPoints']!.map((x) => ProductionPoint.fromJson(x)));
    if (syncedData.productionPoints == null) {
      syncedData.productionPoints = productionPoints;
    } else {
      syncedData.productionPoints?.clear();
      syncedData.productionPoints?.addAll(productionPoints);
    }
    List<ChargingPoint> chargingPoints = json['chargingPoints'] == null ? [] : List<ChargingPoint>.from(json['chargingPoints']!.map((x) => ChargingPoint.fromJson(x)));
    if (syncedData.chargingPoints == null) {
      syncedData.chargingPoints = chargingPoints;
    } else {
      syncedData.chargingPoints?.clear();
      syncedData.chargingPoints?.addAll(chargingPoints);
    }

    List<CategoryData> categories = json['businessCategories'] == null ? [] : List<CategoryData>.from(json['businessCategories']!.map((x) => CategoryData.fromJson(x)));
    if (syncedData.categories == null) {
      syncedData.categories = categories;
    } else {
      syncedData.categories?.clear();
      syncedData.categories?.addAll(categories);
    }
    return syncedData;
  }

  Map<String, dynamic> toJson() {
    return {
      'syncedDataId': syncedDataId,
      'date': date,
      'destinationDetails': destinationDetails.target?.toJson(),
      "totalTimeSlots": List<dynamic>.from(totalTimeSlots.map((x) => x.toJson())),
    };
  }
}

@Entity()
class DestinationDetails {
  @Id()
  int destinationId;
  final String? destinationStartTime;
  final String? destinationEndTime;
  final syncedData = ToOne<SyncedData>();
  final String? destinationCurrency;

  DestinationDetails({
    this.destinationId = 0,
    this.destinationStartTime,
    this.destinationEndTime,
    this.destinationCurrency,
  });

  factory DestinationDetails.fromJson(Map<String, dynamic> json) => DestinationDetails(destinationStartTime: json['destinationStartTime'].toString().convertToDateFormat, destinationEndTime: json['destinationEndTime'].toString().convertToDateFormat, destinationCurrency: json["destinationCurrency"]);

  Map<String, dynamic> toJson() => {
        "destinationStartTime": destinationStartTime,
        "destinationEndTime": destinationEndTime,
        "destinationCurrency": destinationCurrency,
      };
}

@Entity()
class TimeSlotModel {
  @Id()
  int timeSlotId;
  DateTime startTime;
  DateTime endTime;
  var adDetail = ToOne<AdDetails>();
  final syncedData = ToOne<SyncedData>();

  TimeSlotModel({
    this.timeSlotId = 0,
    required this.startTime,
    required this.endTime,
  });

  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    TimeSlotModel timeSlotModel = TimeSlotModel(
      startTime: json["startTime"].toString().fullDateFromTime,
      endTime: json["endTime"].toString().fullDateFromTime,
    );
    return timeSlotModel;
  }

  Map<String, dynamic> toJson() => {
        "id": timeSlotId,
        "startTime": startTime.toIso8601String(),
        "endTime": endTime.toIso8601String(),
        "adDetail": adDetail.target?.toJson(),
      };
}

@Entity()
class AdDetails {
  @Id()
  int adDetailsId;
  String? uuid;
  int? entityId;
  String? entityUuid;
  String? entityType;
  String? name;
  String? storeUuid;
  String? agencyUuid;
  String? logo;
  String? description;
  String? adUrl;
  List<String> mediaList = ToMany<String>();
  String? mediaType;
  int? duration;
  double? chargesPerAd;
  double? walletBalance;
  String? udf1;
  String? udf2;
  String? udf3;
  String? storeName;
  bool isDestinationDefaultAd;
  @Transient()
  int? playedCount;
  bool? isInterruptedAd;
  String? interruptBy;
  bool isActive;
  bool isCorrupted;
  bool isFileNotFound;

  AdDetails({
    this.adDetailsId = 0,
    this.uuid,
    this.entityUuid,
    required this.isDestinationDefaultAd,
    this.entityId,
    this.entityType,
    this.name,
    this.storeUuid,
    this.agencyUuid,
    this.logo,
    this.description,
    this.adUrl,
    this.mediaType,
    this.duration,
    this.chargesPerAd,
    this.walletBalance,
    this.udf1,
    this.udf2,
    this.udf3,
    this.playedCount,
    this.isInterruptedAd,
    this.interruptBy,
    this.isActive = true,
    this.isCorrupted = false,
    this.isFileNotFound = false,
    this.storeName,
  });

  factory AdDetails.fromJson(Map<String, dynamic> json) {
    return AdDetails(
      uuid: json["uuid"],
      entityId: json["entityId"],
      entityUuid: json["entityUuid"],
      entityType: json["entityType"],
      name: json["name"],
      storeUuid: json["storeUuid"],
      agencyUuid: json["agencyUuid"],
      logo: json["logo"],
      description: json["description"],
      adUrl: json["adUrl"].toString().replaceAll(' ', ''),
      mediaType: json["mediaType"],
      duration: json["duration"],
      chargesPerAd: json["chargesPerAd"],
      walletBalance: json["walletBalance"],
      udf1: json["udf1"],
      udf2: json["udf2"],
      udf3: json["udf3"],
      playedCount: json["playedCount"],
      isInterruptedAd: json['isInterruptedAd'],
      isDestinationDefaultAd: json["isDestinationDefaultAd"] ?? true,
      interruptBy: json['interruptBy'],
      storeName: json["storeName"],
      // isCorrupted: json['isCorrupted'],
      // isFileNotFound: json['isFileNotFound'],
    );
  }

  Map<String, dynamic> toJson() => {
        "adDetailsId": adDetailsId,
        "uuid": uuid,
        "isDestinationDefaultAd": isDestinationDefaultAd,
        "entityId": entityId,
        "entityUuid": entityUuid,
        "entityType": entityType,
        "name": name,
        "storeUuid": storeUuid,
        "agencyUuid": agencyUuid,
        "logo": logo,
        "description": description,
        "adUrl": adUrl.toString().replaceAll(' ', ''),
        "mediaList": mediaList,
        "mediaType": mediaType,
        "duration": duration,
        "chargesPerAd": chargesPerAd,
        "walletBalance": walletBalance,
        "udf1": udf1,
        "udf2": udf2,
        "udf3": udf3,
        "playedCount": playedCount,
        'isInterruptedAd': isInterruptedAd,
        'interruptBy': interruptBy,
        'storeName': storeName,
        // 'isCorrupted': isCorrupted,
        // 'isFileNotFound': isFileNotFound,
      };
}

@Entity()
class RobotMetaData {
  @Id(assignable: true)
  int id;
  int robotMetaDataId;
  final String? robotUuid;
  final int? floorNumber;
  double? speed;
  double? speekerSound;
  final String? chassisId;
  final String? serialNumber;
  final String? hostName;
  final String? navigationVersion;
  final String? powerBoardVersion;
  final String? mainDisplayId;
  final String? backDisplayId;

  final dynamic udf1;
  final dynamic udf2;
  final dynamic udf3;
  final syncedData = ToOne<SyncedData>();

  RobotMetaData({
    this.id = 0,
    this.robotMetaDataId = 0,
    this.robotUuid,
    this.floorNumber,
    this.speed,
    this.speekerSound,
    this.chassisId,
    this.serialNumber,
    this.hostName,
    this.navigationVersion,
    this.powerBoardVersion,
    this.mainDisplayId,
    this.backDisplayId,
    this.udf1,
    this.udf2,
    this.udf3,
  });

  factory RobotMetaData.fromJson(Map<String, dynamic> json) {
    return RobotMetaData(
      robotUuid: json["uuid"],
      floorNumber: int.parse(json["floorNumber"].toString()),
      speed: json["speed"] ?? 0.9,
      speekerSound: json["speekerSound"],
      chassisId: json["chassisId"],
      serialNumber: json["serialNumber"],
      hostName: json["hostName"],
      navigationVersion: json["navigationVersion"],
      powerBoardVersion: json["powerBoardVersion"],
      mainDisplayId: json["mainDisplayId"],
      backDisplayId: json["backDisplayID"],
      udf1: json["udf1"],
      udf2: json["udf2"],
      udf3: json["udf3"],
    );
  }

  Map<String, dynamic> toJson() => {
        "syncedDataId": syncedData.target?.syncedDataId,
        "robotMetaDataId": robotMetaDataId,
        "uuid": robotUuid,
        "floorNumber": floorNumber,
        "speed": speed,
        "speekerSound": speekerSound,
        "chassisId": chassisId,
        "serialNumber": serialNumber,
        "hostName": hostName,
        "navigationVersion": navigationVersion,
        "powerBoardVersion": powerBoardVersion,
        "mainDisplayId": mainDisplayId,
        "backDisplayID": backDisplayId,
        "udf1": udf1,
        "udf2": udf2,
        "udf3": udf3,
      };
}

@Entity()
class AdSequence {
  @Id()
  int adSequenceId;
  DateTime startTime;
  DateTime endTime;
  @Transient()
  final adDetails = ToMany<AdDetails>();
  final syncedData = ToOne<SyncedData>();

  AdSequence({
    this.adSequenceId = 0,
    required this.startTime,
    required this.endTime,
  });

  factory AdSequence.fromJson(Map<String, dynamic> json) {
    var adSequence = AdSequence(
      startTime: json["startTime"].toString().fullDateFromTime,
      endTime: json["endTime"].toString().fullDateFromTime,
    );
    List<AdDetails> adDetails = json['adDetails'] == null ? [] : List<AdDetails>.from(json['adDetails']!.map((x) => AdDetails.fromJson(x)));
    adSequence.adDetails.addAll(adDetails);
    return adSequence;
  }

  Map<String, dynamic> toJson() => {
        "adSequenceId": adSequenceId,
        "syncedDataId": syncedData.target?.syncedDataId,
        "startTime": startTime.toIso8601String(),
        "endTime": endTime.toIso8601String(),
        "adDetails": List<dynamic>.from(adDetails.map((x) => x.toJson())),
      };
}

@Entity()
class ProductionPoint {
  @Id()
  int id;
  String? name;
  String? type;
  double? x;
  double? y;
  double? theta;
  final syncedData = ToOne<SyncedData>();
  bool isDeleted;

  ProductionPoint({this.id = 0, this.name, this.type, this.x, this.y, this.theta, this.isDeleted = false});

  factory ProductionPoint.fromJson(Map<String, dynamic> json) => ProductionPoint(
        name: json["name"],
        type: json["type"],
        x: json["x"],
        y: json["y"],
        theta: json["theta"],
      );

  Map<String, dynamic> toJson() => {
        "syncedDataId": syncedData.target?.syncedDataId,
        "name": name,
        "type": type,
        "x": x,
        "y": y,
        "theta": theta,
      };

  ProductionPoint copyWith({@Id() required int id, String? name, String? type, double? x, double? y, double? theta, bool? isDeleted}) {
    ProductionPoint locationPointData = ProductionPoint(theta: theta ?? this.theta, y: y ?? this.y, x: x ?? this.x, name: name ?? this.name, id: id, type: type ?? this.type, isDeleted: this.isDeleted);

    return locationPointData;
  }
}

@Entity()
class ChargingPoint {
  @Id()
  int id;
  String? name;
  String? type;
  double? x;
  double? y;
  double? theta;
  final syncedData = ToOne<SyncedData>();
  bool isDeleted;

  ChargingPoint({this.id = 0, this.name, this.type, this.x, this.y, this.theta, this.isDeleted = false});

  factory ChargingPoint.fromJson(Map<String, dynamic> json) => ChargingPoint(
        name: json["name"],
        type: json["type"],
        x: json["x"],
        y: json["y"],
        theta: json["theta"],
      );

  Map<String, dynamic> toJson() => {
        "syncedDataId": syncedData.target?.syncedDataId,
        "name": name,
        "type": type,
        "x": x,
        "y": y,
        "theta": theta,
      };

  ChargingPoint copyWith({@Id() required int id, String? name, String? type, double? x, double? y, double? theta, bool? isDeleted}) {
    ChargingPoint chargingPointData = ChargingPoint(theta: theta ?? this.theta, y: y ?? this.y, x: x ?? this.x, name: name ?? this.name, id: id, type: type ?? this.type, isDeleted: this.isDeleted);

    return chargingPointData;
  }
}

class Pose {
  double? x;
  double? y;
  double? theta;

  Pose({
    this.x,
    this.y,
    this.theta,
  });

  factory Pose.fromJson(Map<String, dynamic> json) => Pose(
        x: json["x"]?.toDouble(),
        y: json["y"]?.toDouble(),
        theta: json["theta"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "x": x,
        "y": y,
        "theta": theta,
      };
}
