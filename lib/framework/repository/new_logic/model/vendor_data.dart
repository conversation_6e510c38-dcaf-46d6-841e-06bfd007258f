import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

@Entity()
class VendorData {
  @Id()
  int vendorId;
  String vendorUuid;
  String vendorName;
  @Property(type: PropertyType.float)
  double walletBalance;
  @Transient()
  final assignAdDetails = ToMany<AdDetails>();
  @Transient()
  double? storageUsedSize;
  bool isDeleted;

  VendorData({
    this.vendorId = 0,
    required this.vendorUuid,
    required this.vendorName,
    required this.walletBalance,
    this.storageUsedSize,
    this.isDeleted = false,
  });

  factory VendorData.fromJson(Map<String, dynamic> json) {
    VendorData vendorData = VendorData(
      vendorUuid: json["entityUuid"],
      vendorName: json["name"],
      walletBalance: json["walletBalance"],
      storageUsedSize: json["storageUsedSize"],
    );
    List<AdDetails> assignAdDetails = json['assignAdDetails'] == null ? [] : List<AdDetails>.from(json['assignAdDetails']!.map((x) => AdDetails.fromJson(x)));
    vendorData.assignAdDetails.clear();
    vendorData.assignAdDetails.addAll(assignAdDetails);
    return vendorData;
  }

  VendorData copyWith({
    required int vendorId,
    String? vendorUuid,
    String? vendorName,
    double? walletBalance,
    double? storageUsedSize,
    bool? isDeleted,
  }) {
    return VendorData(
      vendorId: vendorId,
      vendorUuid: vendorUuid ?? this.vendorUuid,
      vendorName: vendorName ?? this.vendorName,
      walletBalance: walletBalance ?? this.walletBalance,
      storageUsedSize: storageUsedSize ?? this.storageUsedSize,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}
