import 'package:odigo_display/framework/provider/network/api_result.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/common/model/common_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/delete_ads_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/get_language_list_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/login_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/server_robot_list_response_model.dart';

abstract class RobotMetaDataRepository {
  /// Robot Login API
  Future<ApiResult<LoginResponseModel>> loginAPI(String map);

  /// Data Syncing API
  Future<ApiResult<GetSyncResponseModel>> syncAPI();

  /// Check Sequence Api
  Future<ApiResult<CommonResponseModel>> checkSequenceApi(bool isRead);

  /// Get Music Api
  Future<int> getMusicApi();

  /// Delete Advertise API
  Future<ApiResult<DeleteAdsResponseModel>> deleteAdsAPI();

  // /// Send Ads data
  // Future sendAdsData();

  /// Deduct Wallet Balance
  Future<ApiResult<CommonResponseModel>> deductWalletBalanceApi(String request);

  ///Get Robot List from Live Server
  Future<ApiResult<ServerRobotListResponseModel>> getRobotList();

  ///Get Robot List from Live Server
  Future<ApiResult<GetLanguageListResponseModel>> getLanguageListApi();
}
