import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

@Entity()
class FullyPlayedAdDetail {
  @Id(assignable: true)
  int id;
  String? udid;
  String? entityId;
  String? entityType;
  String? name;
  String? logo;
  String? description;
  String? adUrl;
  String? mediaType;
  String? duration;
  String? chargesPerAd;
  String? walletBalance;
  String? udf1;
  String? udf2;
  String? udf3;
  String? timeSlot;
  String? date;
  List<String>? mediaList;
  String? downloadZipId;
  String? localPath;

  /// Fully Played
  @Property(type: PropertyType.date)
  DateTime? startTime;
  @Property(type: PropertyType.date)
  DateTime? endTime;
  bool? isInterruptedAd;
  String? isInterruptedAdId;
  bool? isReplayed;
  bool? isDataSync;
  bool? isDestinationDefaultAd;
  String? interruptedBy;
  bool? isReschedule;
  final syncedData = ToOne<SyncedData>();
  @Unique(onConflict: ConflictStrategy.replace)
  final timeSlotModel = ToOne<TimeSlotModel>();

  FullyPlayedAdDetail({
    this.id = 0,
    this.udid,
    this.entityId,
    this.entityType,
    this.name,
    this.logo,
    this.description,
    this.adUrl,
    this.mediaList,
    this.mediaType,
    this.duration,
    this.chargesPerAd,
    this.walletBalance,
    this.udf1,
    this.udf2,
    this.udf3,
    this.downloadZipId,
    this.localPath,
    this.timeSlot,
    this.date,
    this.startTime,
    this.endTime,
    this.isInterruptedAd,
    this.isInterruptedAdId,
    this.isReplayed,
    this.isDataSync,
    this.isDestinationDefaultAd,
    this.interruptedBy,
    this.isReschedule,
  });

  factory FullyPlayedAdDetail.fromJson(Map<String, dynamic> json) => FullyPlayedAdDetail(
        downloadZipId: json["downloadZipId"],
        udid: json["udid"],
        entityId: json["entityId"],
        entityType: json["entityType"],
        name: json["name"],
        logo: json["logo"],
        description: json["description"],
        adUrl: json["adUrl"],
        mediaList: json["mediaList"] == null ? [] : List<String>.from(json["mediaList"]!.map((x) => x)),
        mediaType: json["mediaType"],
        duration: json["duration"],
        chargesPerAd: json["chargesPerAd"],
        walletBalance: json["walletBalance"],
        udf1: json["udf1"],
        udf2: json["udf2"],
        udf3: json["udf3"],
        localPath: json["localPath"],
        date: json["date"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        timeSlot: json["timeSlot"],
        isInterruptedAd: json["isInterruptedAd"],
        isInterruptedAdId: json["isInterruptedAdId"],
        isReplayed: json["isReplayed"],
        isDataSync: json["isDataSync"],
        isDestinationDefaultAd: json["isDestinationDefaultAd"],
        interruptedBy: json["interruptedBy"],
        isReschedule: json["isReschedule"],
      );

  Map<String, dynamic> toJson() => {
        "taskId": downloadZipId,
        "udid": udid,
        "entityId": entityId,
        "entityType": entityType,
        "name": name,
        "logo": logo,
        "description": description,
        "adUrl": adUrl,
        "mediaList": mediaList == null ? [] : List<dynamic>.from(mediaList!.map((x) => x)),
        "mediaType": mediaType,
        "duration": duration,
        "chargesPerAd": chargesPerAd,
        "walletBalance": walletBalance,
        "udf1": udf1,
        "udf2": udf2,
        "udf3": udf3,
        "localPath": localPath,
        "date": date,
        "startTime": startTime?.toIso8601String(),
        "endTime": endTime?.toIso8601String(),
        "timeSlot": timeSlot,
        "isInterruptedAd": isInterruptedAd,
        "isInterruptedAdId": isInterruptedAdId,
        "isReplayed": isReplayed,
        "isDataSync": isDataSync,
        "isDestinationDefaultAd": isDestinationDefaultAd,
        "interruptedBy": interruptedBy,
        "isReschedule": isReschedule,
      };
}
