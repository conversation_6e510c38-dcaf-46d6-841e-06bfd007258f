import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';

@Entity()
class InterruptedAdDetail {
  @Id(assignable: true)
  int id;
  String? uuid;
  String? entityId;
  String? entityType;
  String? name;
  String? logo;
  String? description;
  String? adUrl;
  String? mediaType;
  String? duration;
  String? chargesPerAd;
  String? walletBalance;
  String? udf1;
  String? udf2;
  String? udf3;
  String? timeSlot;
  String? date;
  String? startTime;
  String? interruptedBy;
  String? endTime;
  String? localPath;
  List<String>? mediaList;
  bool? isReschedule;
  final syncedData = ToOne<SyncedData>();
  final timeSlotModel = ToOne<TimeSlotModel>();

  InterruptedAdDetail({
    this.id = 0,
    this.uuid,
    this.entityId,
    this.entityType,
    this.name,
    this.logo,
    this.description,
    this.adUrl,
    this.mediaList,
    this.mediaType,
    this.duration,
    this.chargesPerAd,
    this.walletBalance,
    this.udf1,
    this.udf2,
    this.udf3,
    this.localPath,
    this.timeSlot,
    this.date,
    this.startTime,
    this.endTime,
    this.interruptedBy,
    this.isReschedule,
  });

  factory InterruptedAdDetail.fromJson(Map<String, dynamic> json) => InterruptedAdDetail(
    uuid: json["uuid"],
    entityId: json["entityId"],
    entityType: json["entityType"],
    name: json["name"],
    logo: json["logo"],
    description: json["description"],
    adUrl: json["adUrl"],
    mediaList: json["mediaList"] == null ? [] : List<String>.from(json["mediaList"]!.map((x) => x)),
    mediaType: json["mediaType"],
    duration: json["duration"],
    chargesPerAd: json["chargesPerAd"],
    walletBalance: json["walletBalance"],
    udf1: json["udf1"],
    udf2: json["udf2"],
    udf3: json["udf3"],
    localPath: json["localPath"],
    date: json["date"],
    startTime: json["startTime"],
    endTime: json["endTime"],
    timeSlot: json["timeSlot"],
    interruptedBy: json["interruptedBy"],
    isReschedule: json["isReschedule"],
  );

  Map<String, dynamic> toJson() => {
    "uuid": uuid,
    "entityId": entityId,
    "entityType": entityType,
    "name": name,
    "logo": logo,
    "description": description,
    "adUrl": adUrl,
    "mediaList": mediaList == null ? [] : List<dynamic>.from(mediaList!.map((x) => x)),
    "mediaType": mediaType,
    "duration": duration,
    "chargesPerAd": chargesPerAd,
    "walletBalance": walletBalance,
    "udf1": udf1,
    "udf2": udf2,
    "udf3": udf3,
    "localPath": localPath,
    "date": date,
    "startTime": startTime,
    "endTime": endTime,
    "timeSlot": timeSlot,
    "interruptedBy": interruptedBy,
    "isReschedule": isReschedule,
  };
}
