import 'dart:convert';
import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/common/model/common_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/contract/robot_meta_data_repository.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/delete_ads_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/get_language_list_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/login_response_model.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/server_robot_list_response_model.dart';
import 'package:odigo_display/ui/routing/delegate.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/assets.gen.dart';

@LazySingleton(as: RobotMetaDataRepository, env: [development, production])
class RobotMetaDataApiRepository implements RobotMetaDataRepository {
  final DioClient apiClient;

  RobotMetaDataApiRepository(this.apiClient);

  bool isMock = false;

  ///q Login Api
  @override
  Future<ApiResult<LoginResponseModel>> loginAPI(String map) async {
    if (isMock) {
      await Future.delayed(const Duration(seconds: 2));
      var response = {
        "message": "User login successfully",
        "data": {
          "scope": null,
          "userUuid": "HDBV-BA8P-INEM-VVQO",
          "userId": 21,
          "roleId": 6,
          "uuid": "HDBV-BA8P-INEM-VVQO",
          "name": null,
          "message": "login successfully",
          "status": 200,
          "entityUuid": null,
          "entityId": 20,
          "entityType": "ADS_PANEL",
          "email": "manavpatel@gmail.com_com.kodytechnolabs.odigo_f3bb5ab8eddef7e0",
          "contactNumber": "1239875647_com.kodytechnolabs.odigo_f3bb5ab8eddef7e0",
          "profileImage": null,
          "roleUuid": null,
          "roleName": "ADS_PANEL",
          "canChangePassword": true,
          "emailVerified": null,
          "contactVerified": null,
          "robotUuid": "UC8C-0W0P-AZ44-T2V6",
          "destinationUuid": "D763-W0W2-JPQG-37MS",
          "isRegistrationCompleted": null,
          "entityStatus": null,
          "countryUuid": null,
          "currencyName": null,
          "passcode": "123456",
          "aesSecretKey": null,
          "aesSalt": null,
          "access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
          "token_type": "Bearer",
          "expires_in": 31103999
        },
        "status": 200
      };
      LoginResponseModel responseModel = loginResponseModelFromJson(jsonEncode(response));
      return ApiResult.success(data: responseModel);
    } else {
      try {
        Response? response = await apiClient.postRequest(ApiEndPoints.loginEncryption, map, passPublicKey: true);
        LoginResponseModel responseModel = loginResponseModelFromJson(response.toString());
        if (responseModel.status == ApiEndPoints.apiStatus_200) {
          return ApiResult.success(data: responseModel);
        } else {
          return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
        }
      } catch (err) {
        showLog('error $err');
        return ApiResult.failure(error: NetworkExceptions.getDioException(err));
      }
    }
  }

  /// Data Syncing API
  @override
  Future<ApiResult<GetSyncResponseModel>> syncAPI() async {
    /*try {*/
    if (isMock) {
      String data;
      data = await DefaultAssetBundle.of(globalNavigatorKey.currentContext!).loadString(Assets.json.dummyJson);
      GetSyncResponseModel responseModel = getSyncResponseModelFromJson(data.toString());
      return ApiResult.success(data: responseModel);
    } else {
      try {
        Response? response = await apiClient.getRequest(ApiEndPoints.getSyncData);
        GetSyncResponseModel responseModel = getSyncResponseModelFromJson(response.toString());
        if (responseModel.status == ApiEndPoints.apiStatus_200) {
          return ApiResult.success(data: responseModel);
        } else {
          return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
        }
      } catch (err) {
        showLog('error $err');
        return ApiResult.failure(error: NetworkExceptions.getDioException(err));
      }
    }
    /*} catch (err) {
      showLog('error $err');
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }*/
  }

  @override
  Future<int> getMusicApi() async {
    try {
      Response? response = await apiClient.getRequest('/music/list');
      CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
      return commonResponseModel.status ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Delete Advertise API
  @override
  Future<ApiResult<DeleteAdsResponseModel>> deleteAdsAPI() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.deleteAdsAPI);
      DeleteAdsResponseModel responseModel = deleteAdsResponseModelFromJson(response.toString());
      if (responseModel.status == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
      }
    } catch (err) {
      showLog('error $err');
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  // /// Send Ads data
  // @override
  // Future sendAdsData() async {
  //   try {
  //     // TODO:(24/07/24) manthan make it post request with json params
  //     Response? response = await apiClient.getRequest(ApiEndPoints.sendAdsData);
  //     SendDataResponseModel responseModel = sendDataResponseModelFromJson(response.toString());
  //     if (responseModel.status == ApiEndPoints.apiStatus_200) {
  //       return ApiResult.success(data: responseModel);
  //     } else {
  //       return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
  //     }
  //   } catch (err) {
  //     showLog('error $err');
  //     return ApiResult.failure(error: NetworkExceptions.getDioException(err));
  //   }
  // }

  /// Deduct Wallet Balance
  @override
  Future<ApiResult<CommonResponseModel>> deductWalletBalanceApi(String request) async {
    try {
      log('Wallet Deduction Request: $request');
      Response? response = await apiClient.postRequest(ApiEndPoints.deductWalletBalance, request);
      log('Wallet Deduction Response: ${response.toString()}');
      CommonResponseModel responseModel = commonResponseModelFromJson(response.toString());
      if (responseModel.status == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
      }
    } catch (err) {
      showLog('error $err');
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  ///Get Robot List from Live Server
  @override
  Future<ApiResult<ServerRobotListResponseModel>> getRobotList() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.robotList);
      ServerRobotListResponseModel responseModel = serverRobotListResponseModelFromJson(response.toString());
      if (responseModel.status == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
      }
    } catch (err) {
      showLog('error $err');
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  ///Check Sync Api
  @override
  Future<ApiResult<CommonResponseModel>> checkSequenceApi(bool isRead) async {
    try {
      showLog('Check sequence related $isRead');
      Response? response = await apiClient.getRequest(ApiEndPoints.checkSequence(isRead));
      CommonResponseModel responseModel = commonResponseModelFromJson(response.toString());
      if (responseModel.status == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
      }
    } catch (err) {
      showLog('error $err');
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  /// Get Language List
  @override
  Future<ApiResult<GetLanguageListResponseModel>> getLanguageListApi() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getLanguageList);
      GetLanguageListResponseModel responseModel = getLanguageListResponseModelFromJson(response.toString());
      if (responseModel.status == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(responseModel.message ?? ''));
      }
    } catch (err) {
      showLog('error $err');
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }
}
