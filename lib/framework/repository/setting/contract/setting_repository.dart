
import 'package:odigo_display/framework/provider/network/api_result.dart';
import 'package:odigo_display/framework/repository/common/model/common_response_model.dart';
import 'package:odigo_display/framework/repository/setting/model/request_model/settings_request_model.dart';
import 'package:odigo_display/framework/repository/setting/model/response_model/settings_response_model.dart';

abstract class SettingRepository {
  ///Robot detail api
  Future robotDetailApi({required String uuid});

  ///Active Deactivate Robot
  Future activeDeactivateRobotApi({required String uuid, required bool isActive});

  /// Change Robot State API
  Future changeRobotStateApi({required String uuid, required String robotState});

  /// Robot settings details api
  Future getRobotSettingDetails({required String robotUuid});

  /// Update the robot settings API
  Future updateRobotSettings({required String robotSettingUuid, required String key, required String value});

  ///Save Settings Api
  Future<ApiResult<CommonResponseModel>> saveSettingsApi(SettingsRequestModel settings, {bool putRequest = false});

  ///Get Settings Api
  Future<ApiResult<SettingsListResponseModel>> getSettingsApi(String? robotUuid, {String? settingsKey});
}
