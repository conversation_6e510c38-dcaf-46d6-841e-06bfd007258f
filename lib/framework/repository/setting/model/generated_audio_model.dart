// To parse this JSON data, do
//
//     final generatedAudioModel = generatedAudioModelFromJson(jsonString);

import 'dart:convert';

List<GeneratedAudioModel> generatedAudioModelListFromJson(String str) => List<GeneratedAudioModel>.from(json.decode(str).map((x) => GeneratedAudioModel.fromJson(x)));

String generatedAudioModelListToJson(List<GeneratedAudioModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

GeneratedAudioModel generatedAudioModelFromJson(String str) => GeneratedAudioModel.fromJson(json.decode(str));

String generatedAudioModelToJson(GeneratedAudioModel data) => json.encode(data.toJson());

class GeneratedAudioModel {
  String? uuid;
  String? mode;
  String? audioType;
  String path;
  String name;
  bool play;

  GeneratedAudioModel({
    this.uuid,
    required this.path,
    required this.name,
    this.mode,
    this.audioType,
    this.play = false,
  });

  factory GeneratedAudioModel.fromJson(Map<String, dynamic> json) => GeneratedAudioModel(
    uuid: json["uuid"],
    path: json["path"],
    name: json["name"],
    mode: json["mode"],
    audioType: json["audioType"],
    play: json["play"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "uuid": uuid,
    "path": path,
    "name": name,
    "mode": mode,
    "audioType": audioType,
    "play": play,
  };
}