// To parse this JSON data, do
//
// final mealDeliverySettingModel = mealDeliverySettingModelFromJson(jsonString);

import 'dart:convert';

RecycleSettingModel recycleSettingModelFromJson(String str) => RecycleSettingModel.fromJson(json.decode(str));

String recycleSettingModelToJson(RecycleSettingModel data) => json.encode(data.toJson());

class RecycleSettingModel {
  int maxTime;
  int minTime;
  int residenceTime;
  double operatingSpeed;
  double maxOperatingSpeed;
  double minOperatingSpeed;
  String placingRecyclablePrompt;
  bool enablePlacingRecyclablePrompt;

  RecycleSettingModel({
    this.maxTime = 40,
    this.minTime = 5,
    this.residenceTime = 25,
    this.operatingSpeed = 0.3,
    this.maxOperatingSpeed = 1.0,
    this.minOperatingSpeed = 0.1,
    this.placingRecyclablePrompt = '',
    this.enablePlacingRecyclablePrompt = false,
  });

  factory RecycleSettingModel.fromJson(Map<String, dynamic> json) => RecycleSettingModel(
        maxTime: json['max_time'],
        minTime: json['min_time'],
        residenceTime: json['residence_time'],
        operatingSpeed: json['operating_speed']?.toDouble(),
        maxOperatingSpeed: json['max_operating_speed'],
        minOperatingSpeed: json['min_operating_speed'],
        placingRecyclablePrompt: json['placing_recyclable_prompt'],
        enablePlacingRecyclablePrompt: json['enable_placing_recyclable_prompt'],
      );

  factory RecycleSettingModel.getDefault() => RecycleSettingModel(
        maxTime: 40,
        minTime: 5,
        residenceTime: 25,
        operatingSpeed: 0.3,
        maxOperatingSpeed: 1.0,
        minOperatingSpeed: 0.1,
        placingRecyclablePrompt: '',
        enablePlacingRecyclablePrompt: false,
      );

  Map<String, dynamic> toJson() => {
        'max_time': maxTime,
        'min_time': minTime,
        'residence_time': residenceTime,
        'operating_speed': operatingSpeed,
        'max_operating_speed': maxOperatingSpeed,
        'min_operating_speed': minOperatingSpeed,
        'placing_recyclable_prompt': placingRecyclablePrompt,
        'enable_placing_recyclable_prompt': enablePlacingRecyclablePrompt,
      };
}
