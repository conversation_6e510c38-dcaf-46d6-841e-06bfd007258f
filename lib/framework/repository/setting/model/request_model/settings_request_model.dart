// To parse this JSON data, do
//
//     final settingsRequestModel = settingsRequestModelFromJson(jsonString);

import 'dart:convert';

SettingsRequestModel settingsRequestModelFromJson(String str) => SettingsRequestModel.fromJson(json.decode(str));

String settingsRequestModelToJson(SettingsRequestModel data) => json.encode(data.toJson());

class SettingsRequestModel {
  String? key;
  String? value;
  String? uuid;
  String? robotUuid;

  SettingsRequestModel({
    this.key,
    this.value,
    this.uuid,
    this.robotUuid,
  });

  factory SettingsRequestModel.fromJson(Map<String, dynamic> json) => SettingsRequestModel(
        key: json['key'],
        value: json['value'],
        uuid: json["uuid"],
        robotUuid: json["robotUuid"],
      );

  String toJson() => jsonEncode({
        'key': key,
        'value': value,
        'uuid': uuid,
        "robotUuid": robotUuid,
      });
}
