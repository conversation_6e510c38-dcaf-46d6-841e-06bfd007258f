// To parse this JSON data, do
//
//     final deleteArchiveAdsResponseModel = deleteArchiveAdsResponseModelFromJson(jsonString);

import 'dart:convert';

DeleteArchiveAdsResponseModel deleteArchiveAdsResponseModelFromJson(String str) => DeleteArchiveAdsResponseModel.fromJson(json.decode(str));

String deleteArchiveAdsResponseModelTo<PERSON>son(DeleteArchiveAdsResponseModel data) => json.encode(data.toJson());

class DeleteArchiveAdsResponseModel {
  String? message;
  List<String>? data;
  int? status;

  DeleteArchiveAdsResponseModel({
    this.message,
    this.data,
    this.status,
  });

  factory DeleteArchiveAdsResponseModel.fromJson(Map<String, dynamic> json) => DeleteArchiveAdsResponseModel(
    message: json["message"],
    data: json["data"] == null ? [] : List<String>.from(json["data"]!.map((x) => x)),
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x)),
    "status": status,
  };
}
