// To parse this JSON data, do
//
//     final storageResponseModel = storageResponseModelFromJson(jsonString);

import 'dart:convert';

StorageResponseModel storageResponseModelFromJson(String str) => StorageResponseModel.fromJson(json.decode(str));

String storageResponseModelToJson(StorageResponseModel data) => json.encode(data.toJson());

class StorageResponseModel {
  final double totalStorage;
  final double usedStorage;
  final double availableStorage;

  StorageResponseModel({
    required this.totalStorage,
    required this.usedStorage,
    required this.availableStorage,
  });

  factory StorageResponseModel.fromJson(Map<String, dynamic> json) => StorageResponseModel(
        totalStorage: double.parse(json["totalStorage"]),
        usedStorage: double.parse(json["usedStorage"]),
        availableStorage: double.parse(json["availableStorage"]),
      );

  Map<String, dynamic> toJson() => {
        "totalStorage": totalStorage.toString(),
        "usedStorage": usedStorage.toString(),
        "availableStorage": availableStorage.toString(),
      };
}
