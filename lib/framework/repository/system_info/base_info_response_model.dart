// To parse this JSON data, do
//
//     final baseInfoResponseModel = baseInfoResponseModelFromJson(jsonString);

import 'dart:convert';

BaseInfoResponseModel baseInfoResponseModelFromJson(String str) => BaseInfoResponseModel.fromJson(json.decode(str));

String baseInfoResponseModelToJson(BaseInfoResponseModel data) => json.encode(data.toJson());

class BaseInfoResponseModel {
  int battery;
  int chargeFlag;
  int emergencyButton;

  BaseInfoResponseModel({
    required this.battery,
    required this.chargeFlag,
    required this.emergencyButton,
  });

  factory BaseInfoResponseModel.fromJson(Map<String, dynamic> json) => BaseInfoResponseModel(
    battery: json['battery'],
    chargeFlag: json['chargeFlag'],
    emergencyButton: json['emergencyButton'],
  );

  Map<String, dynamic> toJson() => {
    'battery': battery,
    'chargeFlag': chargeFlag,
    'emergencyButton': emergencyButton,
  };
}
