// To parse this JSON data, do
//
//     final modeResponseModel = modeResponseModelFromJson(jsonString);

import 'dart:convert';

ModeResponseModel modeResponseModelFromJson(String str) => ModeResponseModel.fromJson(json.decode(str));

String modeResponseModelToJson(ModeResponseModel data) => json.encode(data.toJson());

class ModeResponseModel {
  int mode;

  ModeResponseModel({
    required this.mode,
  });

  factory ModeResponseModel.fromJson(Map<String, dynamic> json) => ModeResponseModel(
    mode: json["mode"],
  );

  Map<String, dynamic> toJson() => {
    "mode": mode,
  };
}
