// To parse this JSON data, do
//
//     final speedResponseModel = speedResponseModelFromJson(jsonString);

import 'dart:convert';

SpeedResponseModel speedResponseModelFromJson(String str) => SpeedResponseModel.fromJson(json.decode(str));

String speedResponseModelToJson(SpeedResponseModel data) => json.encode(data.toJson());

class SpeedResponseModel {
  double vx;
  double vth;

  SpeedResponseModel({
    required this.vx,
    required this.vth,
  });

  factory SpeedResponseModel.fromJson(Map<String, dynamic> json) => SpeedResponseModel(
    vx: json["vx"].toDouble(),
    vth: json["vth"].toDouble(),
  );

  Map<String, dynamic> toJson() => {
    "vx": vx,
    "vth": vth,
  };
}
