// To parse this JSON data, do
//
//     final wifiModel = wifiModelFromJson(jsonString);

import 'dart:convert';

WifiModel wifiModelFromJson(String str) => WifiModel.fromJson(json.decode(str));

String wifiModelToJson(WifiModel data) => json.encode(data.toJson());

class WifiModel {
  String? deviceName;
  String? devicePassword;
  String? ipAddress;
  bool? isScanning;
  int? wifiStrength;
  bool? isSecured;

  WifiModel({
    this.deviceName,
    this.devicePassword,
    this.ipAddress,
    this.isScanning,
    this.wifiStrength,
    this.isSecured,
  });

  factory WifiModel.fromJson(Map<String, dynamic> json) =>
      WifiModel(
    deviceName: json['device_name'],
    devicePassword: json['device_password'],
    ipAddress: json['ip_address'],
    isScanning: json['is_scanning'],
    wifiStrength: json['wifi_strength'],
    isSecured: json['is_secured'],
  );

  String toJson() => jsonEncode({
    'device_name': deviceName,
    'device_password': devicePassword,
    'ip_address': ipAddress,
    'is_scanning': isScanning,
    'wifi_strength': wifiStrength,
    'is_secured': isSecured,
  });
}
