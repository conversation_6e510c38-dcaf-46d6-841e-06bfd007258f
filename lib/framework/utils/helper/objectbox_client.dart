import 'package:objectbox/objectbox.dart';
import 'package:odigo_display/framework/utils/helper/objectbox_provider.dart';
import 'package:odigo_display/framework/utils/helper/objectbox_response_model.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';

class ObjectBoxClient<Type> {
  final box = ObjectBoxProvider.objectBox.store.box<Type>();

  /// Add Object
  Future<ObjectBoxResponseModel<Type?>> add(Type data) async {
    try {
      Type response = await box.putAndGetAsync(data, mode: PutMode.insert);
      var responseModel = ObjectBoxResponseModel<Type?>(success: SuccessType.CREATED);
      responseModel.data = response;
      return responseModel;
    } catch (e) {
      print(e);
      return ObjectBoxResponseModel<Type?>(success: SuccessType.EXISTS);
    }
  }

  /// Add List
  Future<ObjectBoxResponseModel<List<Type>>> addList(List<Type> data) async {
    try {
      List<Type> response = await box.putAndGetManyAsync(data, mode: PutMode.insert);
      var responseModel = ObjectBoxResponseModel<List<Type>>(success: SuccessType.CREATED);
      responseModel.data = response;
      return responseModel;
    } catch (e) {
      return ObjectBoxResponseModel<List<Type>>(success: SuccessType.EXISTS, data: []);
    }
  }

  Future<ObjectBoxResponseModel<Type?>> update(int id, Type data) async {
    try {
      showLog('update response called ');
      Type? previousData = box.get(id);
      if (previousData != null) {
        previousData = data;
        Type? response;
        try {
          response = await box.putAndGetAsync((previousData as Type), mode: PutMode.put);
        } catch (e) {
          print('E:$e');
        }
        var responseModel = ObjectBoxResponseModel<Type?>(success: SuccessType.UPDATED);
        responseModel.data = response;
        return responseModel;
      } else {
        var responseModel = ObjectBoxResponseModel<Type?>(success: SuccessType.NOT_FOUND);
        return responseModel;
      }
    } catch (e) {
      return ObjectBoxResponseModel<Type?>(success: SuccessType.ERROR);
    }
  }

  Future<ObjectBoxResponseModel<int?>> deleteAll() async {
    int response = box.removeAll();
    if (response != 0) {
      var responseModel = ObjectBoxResponseModel<int?>(success: SuccessType.DELETED);
      responseModel.data = response;
      return responseModel;
    } else {
      var responseModel = ObjectBoxResponseModel<int?>(success: SuccessType.NOT_FOUND);
      return responseModel;
    }
  }

  Future<ObjectBoxResponseModel<bool>> delete(int id) async {
    bool response = box.remove(id);
    if (response) {
      var responseModel = ObjectBoxResponseModel<bool>(success: SuccessType.DELETED);
      responseModel.data = response;
      return responseModel;
    } else {
      var responseModel = ObjectBoxResponseModel<bool>(success: SuccessType.NOT_FOUND);
      return responseModel;
    }
  }

  Future<ObjectBoxResponseModel<List<Type>?>> getAll() async {
    List<Type> response = await box.getAllAsync();
    if (response.isNotEmpty) {
      var responseModel = ObjectBoxResponseModel<List<Type>?>(success: SuccessType.FOUND);
      responseModel.data = response;
      return responseModel;
    } else {
      var responseModel = ObjectBoxResponseModel<List<Type>?>(success: SuccessType.NOT_FOUND);
      return responseModel;
    }
  }

  Future<ObjectBoxResponseModel<List<Type>?>> search(Condition<Type> condition) async {
    Query<Type> query = box.query(condition).build();
    List<Type> response = await query.findAsync();
    if (response.isNotEmpty) {
      var responseModel = ObjectBoxResponseModel<List<Type>?>(success: SuccessType.FOUND);
      responseModel.data = response;
      return responseModel;
    } else {
      var responseModel = ObjectBoxResponseModel<List<Type>?>(success: SuccessType.NOT_FOUND);
      return responseModel;
    }
  }

  Future<ObjectBoxResponseModel<Type>> getSingle(int id) async {
    Type? response = await box.getAsync(id);
    if (response != null) {
      var responseModel = ObjectBoxResponseModel<Type>(success: SuccessType.FOUND);
      responseModel.data = response;
      return responseModel;
    } else {
      var responseModel = ObjectBoxResponseModel<Type>(success: SuccessType.NOT_FOUND);
      return responseModel;
    }
  }
}
