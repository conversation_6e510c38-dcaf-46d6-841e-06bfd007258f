import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kiosk_mode/kiosk_mode.dart';
import 'package:odigo_display/framework/controller/about_device/about_device_controller.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';

class AboutDeviceMobile extends ConsumerWidget with BaseConsumerWidget {
  const AboutDeviceMobile({super.key});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: _bodyWidget(context, ref),
    );
  }

  Widget _bodyWidget(BuildContext context, WidgetRef ref) {
    final aboutDeviceWatch = ref.watch(aboutDeviceController);
    RobotMetaData? robotData = SessionRepository.session.robotMetaData;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
      child: Column(
        children: [
          const Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CommonBackWidget(
                color: AppColors.white,
              ),
            ],
          ),

          ///title
          Row(
            children: [
              Expanded(
                child: CommonText(
                  title: LocaleKeys.keyAboutDevice.localized,
                  textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 46.sp),
                ),
              ),
            ],
          ).paddingSymmetric(vertical: 35.h),
          SizedBox(
            height: 20.h,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  /// list
                  _commonTextWidget(context, LocaleKeys.keyRobotUuid.localized, robotData?.robotUuid, isCopy: robotData?.robotUuid != null && robotData?.robotUuid != ''),
                  _commonTextWidget(context, LocaleKeys.keyROSIP.localized, RobotSession.hostIpAddress.toString(), isCopy: RobotSession.hostIpAddress != null && RobotSession.hostIpAddress != ''),
                  _commonTextWidget(context, 'WI-FI IP', SessionRepository.session.baseUrlGet, isCopy: SessionRepository.session.baseUrlGet != '', isDivider: true),
                  _commonTextWidget(context, LocaleKeys.keyFloorNumber.localized, robotData?.floorNumber.toString()),
                  _commonTextWidget(context, LocaleKeys.keySpeed.localized, robotData?.speed.toString()),
                  _commonTextWidget(context, LocaleKeys.keySpeakerSound.localized, (robotData?.speekerSound != null) ?  robotData?.speekerSound.toString():'0'),
                  // _commonTextWidget(context, LocaleKeys.keyMainDisplayId.localized, robotData?.mainDisplayId, isCopy: robotData?.mainDisplayId != null && robotData?.mainDisplayId != ''),
                  // _commonTextWidget(context, LocaleKeys.keyBackDisplayId.localized, robotData?.backDisplayId, isCopy: true),

                  /// TODO Migration
                  // _commonTextWidget(context, LocaleKeys.keySocketUrl.localized, ref.read(operatorController).socketManager.socketServerUrl.socketUrl),
                  // _commonTextWidget(context, LocaleKeys.keySocketPortNumber.localized, ref.read(operatorController).socketManager.socketServerUrl.socketPortNumber),
                  _commonTextWidget(context, LocaleKeys.keyChassisId.localized, robotData?.chassisId, isCopy: robotData?.chassisId != null && robotData?.chassisId != ''),
                  _commonTextWidget(context, LocaleKeys.keySerialNumber.localized, robotData?.serialNumber, isCopy: robotData?.serialNumber != null && robotData?.serialNumber != ''),
                  _commonTextWidget(context, LocaleKeys.keyHostName.localized, robotData?.hostName, isCopy: robotData?.hostName != null && robotData?.hostName != ''),
                  _commonTextWidget(context, LocaleKeys.keyNavigationVersion.localized, robotData?.navigationVersion, isCopy: robotData?.navigationVersion != null && robotData?.navigationVersion != ''),
                  _commonTextWidget(context, LocaleKeys.keyPowerBoardVersion.localized, robotData?.powerBoardVersion, isCopy: robotData?.powerBoardVersion != null && robotData?.powerBoardVersion != '', isDivider: false),

                  CommonButton(
                    height: 60,
                    width: double.infinity,
                    onTap: () async {
                      showLog('aboutDeviceWatch.kioskMode ${aboutDeviceWatch.kioskMode}');
                      if (aboutDeviceWatch.kioskMode == KioskMode.enabled) {
                        await stopKioskMode();
                        aboutDeviceWatch.updateKioskMode(KioskMode.disabled);
                      } else {
                        await startKioskMode();
                        aboutDeviceWatch.updateKioskMode(KioskMode.enabled);
                      }
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: AppColors.white,
                          margin: EdgeInsets.fromLTRB(context.width * 0.05, 0, context.width * 0.05, context.height * 0.07),
                          padding: EdgeInsets.symmetric(horizontal: context.width * 0.05, vertical: context.height * 0.01),
                          shape: const StadiumBorder(),
                          duration: const Duration(seconds: 2),
                          content: Text(
                            aboutDeviceWatch.kioskMode == KioskMode.enabled ? 'Kiosk Mode Enabled' : 'Kiosk Mode Disabled',
                            style: TextStyles.medium.copyWith(fontSize: 44.sp, color: AppColors.primary),
                          ),
                        ),
                      );
                    },
                    // buttonText: '{aboutDeviceWatch.kioskMode == KioskMode.enabled ? \'DISABLE\' : \'ENABLE\'} KIOSK MODE',
                    buttonText: 'DISABLED KIOSK MODE',
                    buttonTextStyle: TextStyles.medium.copyWith(fontSize: 40.sp, color: AppColors.black),
                    backgroundColor: Colors.white,
                    buttonTextColor: AppColors.black,
                  ).paddingOnly(top: 40.h),
                  SizedBox(height: context.height * 0.1),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _commonTextWidget(BuildContext context, String title, String? value, {bool isCopy = false, bool isDivider = true}) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            /// title
            Expanded(
              flex: 2,
              child: Text(
                title,
                style: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 40.sp),
              ),
            ),
            SizedBox(
              width: context.width * 0.02,
            ),
            // const Spacer(),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  /// value
                  Flexible(
                    child: CommonText(
                      title: value ?? '',
                      textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 36.sp),
                    ),
                  ),
                  SizedBox(
                    width: context.width * 0.03,
                  ),

                  /// copy
                  Visibility(
                    visible: isCopy,
                    child: InkWell(
                      onTap: () {
                        Clipboard.setData(ClipboardData(text: value ?? '')).then((_) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: AppColors.white,
                              margin: EdgeInsets.fromLTRB(context.width * 0.05, 0, context.width * 0.05, context.height * 0.07),
                              padding: EdgeInsets.symmetric(horizontal: context.width * 0.05, vertical: context.height * 0.01),
                              shape: const StadiumBorder(),
                              duration: const Duration(seconds: 1),
                              content: Text(
                                '$title ${LocaleKeys.keyCopiedToYourClipboard.localized}',
                                style: TextStyles.medium.copyWith(fontSize: 44.sp, color: AppColors.primary),
                              ),
                            ),
                          );
                        });
                      },
                      child: Icon(
                        size: context.width * 0.04,
                        Icons.copy_rounded,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        /// divider
        if (isDivider) const Divider().paddingSymmetric(vertical: context.height * 0.02),
      ],
    );
  }
}
