import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_display/framework/controller/navigation/navigation_controller.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/ui/dashboard/custom_keyboard/artistic_multilingual_keyboard.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/const/app_enums.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class OperatorAuthenticationDialog extends ConsumerStatefulWidget {
  final bool isFromWaitingScreen;
  final bool resumeNavigation;

  const OperatorAuthenticationDialog({super.key, this.resumeNavigation = true, required this.isFromWaitingScreen});

  @override
  ConsumerState<OperatorAuthenticationDialog> createState() => _OperatorAuthenticationDialogState();
}

class _OperatorAuthenticationDialogState extends ConsumerState<OperatorAuthenticationDialog> with BaseConsumerStatefulWidget {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final helpInfoDashboardWatch = ref.read(helpInfoDashboardController);
      helpInfoDashboardWatch.passwordCTR.clear();
      helpInfoDashboardWatch.focusNode.requestFocus();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    final helpInfoDashboardWatch = ref.watch(helpInfoDashboardController);
    final displayAdsRead = ref.read(displayAdsController);
    return helpInfoDashboardWatch.isAuthenticationDialogVisible
        ? FadeBoxTransition(
            onPopCall: (animationController) {
              helpInfoDashboardWatch.animationController = animationController;
            },
            child: Container(
              height: MediaQuery.sizeOf(context).height * 0.46,
              margin: EdgeInsets.symmetric(horizontal: MediaQuery.sizeOf(context).width * 0.05),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(color: AppColors.black),
              ),
              child: Column(
                children: [
                  CommonText(
                    title: LocaleKeys.keyAuthenticate.localized,
                    textStyle: TextStyles.semiBold.copyWith(fontSize: 40.sp, color: AppColors.black),
                  ).alignAtCenter().paddingOnly(top: 30.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 25.w),
                    child: Form(
                      key: helpInfoDashboardWatch.formKey,
                      child: PinCodeTextField(
                        appContext: context,
                        focusNode: helpInfoDashboardWatch.focusNode,
                        autoDisposeControllers: false,
                        readOnly: true,
                        cursorColor: AppColors.black,
                        length: 6,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(6),
                        ],
                        controller: helpInfoDashboardWatch.passwordCTR,
                        keyboardType: TextInputType.number,
                        onChanged: (code) {
                          showLog('code check here $code');
                        },
                        textStyle: TextStyles.regular.copyWith(
                          color: AppColors.black,
                        ),
                        onCompleted: (String? code) {
                          authenticateFunction(context, ref);
                        },
                        pinTheme: PinTheme(
                          borderRadius: BorderRadius.circular(20.r),
                          shape: PinCodeFieldShape.box,
                          fieldWidth: 80.w,
                          fieldHeight: 80.w,
                          activeColor: AppColors.textFieldBorderColor,
                          inactiveColor: AppColors.textFieldBorderColor,
                          selectedColor: AppColors.textFieldBorderColor,
                          fieldOuterPadding: EdgeInsets.zero,
                          activeBorderWidth: 1.w,
                          selectedBorderWidth: 1.w,
                          inactiveBorderWidth: 1.w,
                        ),
                        onSubmitted: (otp) {
                          authenticateFunction(context, ref);
                        },
                      ).paddingOnly(top: 25.h),
                    ),
                  ),
                  helpInfoDashboardWatch.isErrorTextVisible
                      ? CommonText(
                          title: LocaleKeys.keyOtpInvalidValidation.localized,
                          textStyle: TextStyles.medium.copyWith(color: AppColors.red, fontSize: 40.sp),
                        ).alignAtCenter().paddingOnly(bottom: 15.h)
                      : SizedBox(height: 60.h),
                  Expanded(
                    child: KeyboardLayouts(
                      textEditingController: helpInfoDashboardWatch.passwordCTR,
                      focusNode: helpInfoDashboardWatch.focusNode,
                      enableLanguageButton: false,
                      keyShadowColor: Colors.black,
                      keyboardBackgroundColor: Colors.black,
                      keysBackgroundColor: Colors.black,
                      keyTextStyle: TextStyle(color: Colors.white, fontSize: 40.sp),
                      keyBorderRadius: BorderRadius.circular(8),
                      keyboardAction: KeyboardAction.actionNext,
                      currentKeyboardLanguage: KeyboardLanguages.english,
                      currentKeyboardsType: KeyboardsTypes.numericOnly,
                      keyboardBorderRadius: BorderRadius.circular(16.r),
                      onKeyPressed: () {
                        helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref, isFromWaiting: widget.resumeNavigation == false ? true : false);
                        // helpInfoDashboardWatch.reStartTimer(context, ref: ref, resumeNavigation: widget.resumeNavigation);
                      },
                      onCancelTap: () {
                        helpInfoDashboardWatch.animationController?.reverse(from: 0.3).then((value) {
                          helpInfoDashboardWatch.passwordCTR.clear();
                          displayAdsRead.chewieController?.play();
                          helpInfoDashboardWatch.updateIsAuthenticationDialogVisible(context, false, ref: ref);
                          if (helpInfoDashboardWatch.searchTimer?.isActive ?? false) {
                            helpInfoDashboardWatch.searchTimer?.cancel();
                            helpInfoDashboardWatch.searchTimer = null;
                          }
                          if (widget.resumeNavigation) {
                            /// TODO Migration
                            ref.read(navigationController).resumeNavigation();
                            // helpInfoDashboardWatch.socketManager.resumeNavigation();
                          }
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          )
        : const Offstage();
  }

  void authenticateFunction(BuildContext context, WidgetRef ref) {
    final helpInfoDashboardRead = ref.read(helpInfoDashboardController);
    final displayAdsRead = ref.read(displayAdsController);
    if (helpInfoDashboardRead.passwordCTR.text == SessionRepository.session.passcode) {
      helpInfoDashboardRead.elapsedSeconds = '';
      ref.read(helpInfoDashboardController).updateAllowInteraction(true);
      ref.read(navigationController).cancelNavigation(ref);

      if (helpInfoDashboardRead.robotStopTimer?.isActive ?? false) {
        helpInfoDashboardRead.robotStopTimer?.cancel();
      }
      displayAdsRead.stopAdPlaying();
      helpInfoDashboardRead.animationController?.reverse(from: 0.3).then((value) {
        helpInfoDashboardRead.searchTimer?.cancel();
        helpInfoDashboardRead.searchTimer = null;
        helpInfoDashboardRead.passwordCTR.clear();
        helpInfoDashboardRead.operatorStartInterruptionTime();

        if (displayAdsRead.chewieController != null) {
          displayAdsRead.chewieController = null;
        }
        if (displayAdsRead.chewieController != null) {
          displayAdsRead.videoPlayerController?.dispose();
          displayAdsRead.videoPlayerController = null;
        }
        displayAdsRead.updateWidget();

        ref.read(navigationStackController).push(NavigationStackItem.newDashboard(isFromWaitingScreen: widget.isFromWaitingScreen));
        helpInfoDashboardRead.updateIsAuthenticationDialogVisible(context, false, ref: ref);
      });
    } else {
      helpInfoDashboardRead.passwordCTR.clear();
      Future.delayed(const Duration(milliseconds: 100), () {
        helpInfoDashboardRead.formKey.currentState?.reset();
      });
      Future.delayed(const Duration(seconds: 3), () {
        helpInfoDashboardRead.updateIsErrorTextVisible(false);
      });
      helpInfoDashboardRead.updateIsErrorTextVisible(true);
    }
  }
}
