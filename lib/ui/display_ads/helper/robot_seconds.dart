import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class RobotSeconds extends StatelessWidget {
  final String seconds;
  final bool isCruise;

  const RobotSeconds({super.key, required this.seconds, this.isCruise = false});

  @override
  Widget build(BuildContext context) {
    showLog('seconds $seconds');
    return (seconds != '')
        ? Container(
            decoration: BoxDecoration(
              color: AppColors.black.withOpacity(0.9),
              borderRadius: BorderRadius.circular(23.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                isCruise
                    ? CommonText(
                        title: '00:${seconds.padLeft(2, '0')}',
                        textStyle: TextStyles.medium,
                      )
                    : CommonText(
                        title: seconds,
                        textStyle: TextStyles.medium,
                      ),
                SizedBox(
                  width: 5.w,
                ),
                Icon(
                  Icons.timer_outlined,
                  color: Colors.white,
                  size: 40.h,
                ),
              ],
            ).paddingSymmetric(vertical: 8.h, horizontal: 10.w),
          )
        : const Offstage();

    // Container(
    //   height: 30.h,
    //   width: 30.h,
    //   decoration: const BoxDecoration(
    //     color: AppColors.primary,
    //     shape: BoxShape.circle,
    //   ),
    //   child: CommonText(
    //     title: seconds.toString().padLeft(2, '00'),
    //     textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.white),
    //     textAlign: TextAlign.center,
    //   ).alignAtCenter(),
    // ) : Offstage();
  }
}
