import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_display/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class GuideScreenMobile extends ConsumerStatefulWidget {
  final String? anim;
  final String? title;
  final String? subTitle;
  final bool doPop;

  const GuideScreenMobile({super.key, this.anim, this.title, this.subTitle, this.doPop = true});

  @override
  ConsumerState createState() => _GuideScreenMobileState();
}

class _GuideScreenMobileState extends ConsumerState<GuideScreenMobile> {
  AnimationController? animationController;
  Timer? timer;
  Timer? startTimer;

  @override
  void initState() {
    if (widget.doPop) {
      timer = null;
      startTimer = null;
      timer = Timer(const Duration(seconds: 5), () {
        animationController?.reverse(from: 0.6).then((value) {
              Navigator.pop(context);
            }) ??
            Navigator.pop(context);
      });

      startTimer = Timer(const Duration(seconds: 2), () {
        // final displayAdsScreenWatch = ref.watch(displayAdsScreenController);
        /// TODO Migration
        // if (displayAdsScreenWatch.socketManager.isSocketError) {
        //   if (startTimer?.isActive ?? false) {
        //     startTimer?.cancel();
        //   }
        //   ref.read(navigationStackController).pop();
        // }
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: FadeBoxTransition(
        onPopCall: (animationController) {
          this.animationController = animationController;
        },
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 100.w, vertical: 50.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Lottie.asset(
                  width: context.width,
                  widget.anim ?? Assets.animation.animMapdata.keyName,
                  // fit: BoxFit.cover,
                ),
                SizedBox(
                  width: double.infinity,
                  height: 18.h,
                ),
                CommonText(
                  title: widget.title ?? '',
                  textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 42.sp),
                ).paddingOnly(bottom: 22.h),
                CommonText(
                  title: widget.subTitle ?? '',
                  textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 37.sp),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
