import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/ui/display_ads/mobile/helper/category_list.dart';
import 'package:odigo_display/ui/display_ads/mobile/helper/keyboard_for_store.dart';
import 'package:odigo_display/ui/display_ads/mobile/helper/search_store_list_tile.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_form_field.dart';

class HelpInfoDashboardWidget extends ConsumerStatefulWidget {
  const HelpInfoDashboardWidget({super.key});

  @override
  ConsumerState<HelpInfoDashboardWidget> createState() => _HelpInfoDashboardWidgetState();
}

class _HelpInfoDashboardWidgetState extends ConsumerState<HelpInfoDashboardWidget> {
  ScrollController? scrollController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    scrollController = ScrollController();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      scrollController?.addListener(() {
        final helpInfoDashboardRead = ref.watch(helpInfoDashboardController);
        helpInfoDashboardRead.stopRobotForFewSeconds(context, ref: ref);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final helpInfoDashboardWatch = ref.watch(helpInfoDashboardController);

    return Positioned.fill(
      top: 80.h,
      child: Visibility(
        visible: helpInfoDashboardWatch.isSearchModeOn,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Odigo Logo
                // CommonSVG(
                //   strIcon: Assets.svgs.svgOdigoLogo.path,
                //   height: context.height * 0.02,
                //   width: context.width * 0.19,
                // ),
                Image.asset(
                  Assets.images.odigo.path,
                  height: context.height * 0.05,
                  width: context.width * 0.19,
                ).alignAtCenter(),
                InkWell(
                  onTap: () {
                    ref.read(navigationStackController).push(const NavigationStackItem.howToUse());
                  },
                  child: Row(
                    children: [
                      CommonSVG(strIcon: Assets.svgs.svgHowToUse.path, height: 43.h, width: 43.h).paddingOnly(right: 10.w),
                      CommonText(
                        title: LocaleKeys.keyHowtoUse.localized,
                        // title: "How to Use?",
                        textStyle: TextStyles.semiBold.copyWith(fontSize: 20.sp),
                      ),
                    ],
                  ),
                )
              ],
            ).paddingSymmetric(horizontal: 25.w),
            CommonText(
              title: LocaleKeys.keySmartAdvertisingRobot.localized,
              textStyle: TextStyles.semiBold.copyWith(fontSize: 20.sp),
            ).paddingSymmetric(horizontal: 25.w),
            SizedBox(height: context.height * 0.01),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const CategoryList(),

                  SizedBox(height: context.height * 0.01),

                  // Search Destination Field
                  CommonInputFormField(
                    textEditingController: helpInfoDashboardWatch.searchTxtCTR,
                    readOnly: true,
                    // showGlassForm: true,
                    fieldHeight: context.height * 0.05,
                    onTap: () => helpInfoDashboardWatch.updateSearchMode(true),
                    validator: (p0) => null,
                    borderColor: AppColors.transparent,
                    backgroundColor: AppColors.clr383838,
                    cursorColor: Colors.white,
                    borderRadius: BorderRadius.circular(54.r),
                    contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                    // hintText: 'Search your destination here',
                    // hintTextStyle: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 22.sp),
                    fieldTextStyle: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 22.sp),
                    prefixWidget: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [CommonText(title: LocaleKeys.keyWhere.localized, textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 16.sp)).paddingOnly(bottom: 6.h), CommonText(title: LocaleKeys.keySearchDestinations.localized, textStyle: TextStyles.light.copyWith(color: AppColors.white.withOpacity(0.5), fontSize: 18.sp))],
                    ).paddingOnly(left: 40.w, right: 40.w),
                    suffixWidget: InkWell(
                      onTap: () {
                        if (helpInfoDashboardWatch.searchTxtCTR.text.isNotEmpty) {
                          helpInfoDashboardWatch.searchTxtCTR.clear();
                          helpInfoDashboardWatch.getSearchStores();
                          helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref);
                        }
                      },
                      child: helpInfoDashboardWatch.searchTxtCTR.text.isNotEmpty
                          ? const Icon(
                              Icons.close,
                              color: AppColors.white,
                            ).paddingOnly(top: context.height * 0.012, bottom: context.height * 0.012, right: context.width * 0.02)
                          : Container(
                              height: context.height * 0.05,
                              width: context.width * 0.14,
                              margin: EdgeInsets.only(top: context.height * 0.012, bottom: context.height * 0.012, right: context.width * 0.02),
                              padding: EdgeInsets.symmetric(vertical: context.height * 0.005),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(50.r),
                                color: AppColors.white,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CommonSVG(strIcon: Assets.svgs.svgSearchIcon.path),
                                  SizedBox(width: context.width * 0.005),
                                  CommonText(
                                    title: LocaleKeys.keySearch.localized,
                                    textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 16.sp),
                                    maxLines: 1,
                                    textAlign: TextAlign.center,
                                    textOverflow: TextOverflow.clip,
                                  ),
                                  // Icon(Icons.arrow_right_alt, color: AppColors.white, size: 25.sp)
                                ],
                              ),
                            ),
                    ),
                    // suffixWidget: InkWell(
                    //   onTap: () {
                    //     helpInfoDashboardWatch.updateSearchMode(false);
                    //     if (!helpInfoDashboardWatch.isSearchModeOn) {
                    //       helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref);
                    //     }
                    //     if (helpInfoDashboardWatch.searchTxtCTR.text.isNotEmpty) {
                    //       helpInfoDashboardWatch.updateSelectedCategory(null);
                    //     } else {
                    //       helpInfoDashboardWatch.updateSearchView(!helpInfoDashboardWatch.isSearchModeOn);
                    //       if (helpInfoDashboardWatch.searchTimer?.isActive ?? false) {
                    //         helpInfoDashboardWatch.searchTimer?.cancel();
                    //         helpInfoDashboardWatch.searchTimer = null;
                    //       }
                    //       helpInfoDashboardWatch.cancelRobotStopTimer();
                    //       /// TODO Migration
                    //       ref.watch(displayAdsController).resumeNavigation();
                    //       // helpInfoDashboardWatch.socketManager.resumeNavigation();
                    //     }
                    //   },
                    //   child: Icon(Icons.close, size: 30.h, color: AppColors.white).paddingOnly(right: 15.w),
                    // ),
                  ),
                  SizedBox(height: context.height * 0.01),
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  Container(
                                    height: context.height * 0.23,
                                    decoration: BoxDecoration(
                                      color: AppColors.clr383838,
                                      borderRadius: BorderRadius.circular(23.r),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(23.r),
                                      child: helpInfoDashboardWatch.searchStoresList.isNotEmpty
                                          ? ListView.separated(
                                              controller: scrollController,
                                              itemCount: helpInfoDashboardWatch.searchStoresList.length,
                                              padding: EdgeInsets.zero,
                                              shrinkWrap: true,
                                              itemBuilder: (context, index) {
                                                final store = helpInfoDashboardWatch.searchStoresList[index];
                                                return SearchStoreListTile(
                                                  store: store,
                                                  color: AppColors.clr383838,
                                                  borderColor: AppColors.transparent,
                                                  onStoreSelect: () {
                                                    ///TODO add audio : "Follow me"
                                                    // newDashboardWatch.socketManager.audioPlayer(audioFile: AppAssets.followMe);.
                                                  },
                                                );
                                              },
                                              separatorBuilder: (BuildContext context, int index) {
                                                return const Divider(color: AppColors.white);
                                              },
                                            ).paddingOnly(bottom: 20.h)
                                          : Center(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Lottie.asset(
                                                    Assets.animation.animNoStore.path,
                                                    height: context.height * 0.08,
                                                    fit: BoxFit.cover,
                                                  ),
                                                  SizedBox(height: context.height * 0.005),
                                                  CommonText(
                                                    title: LocaleKeys.keyNoSearchFound.localized,
                                                    textStyle: TextStyles.semiBold.copyWith(fontSize: 23.sp),
                                                  ),
                                                  SizedBox(height: context.height * 0.005),
                                                  CommonText(
                                                    title: LocaleKeys.keyNoSearchFoundDescription.localized,
                                                    textStyle: TextStyles.light.copyWith(fontSize: 17.sp),
                                                  )
                                                ],
                                              ),
                                            ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 20.w),
                            Expanded(
                              child: SizedBox(
                                height: context.height * 0.23,
                                child: const KeyboardForStore(),
                              ),
                            ),
                          ],
                        ),
                        InkWell(
                          onTap: () {
                            helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref, isCloseButtonTap: true);
                          },
                          child: Container(
                            height: 88,
                            width: 88,
                            margin: EdgeInsets.only(top: 16.h),
                            decoration: BoxDecoration(color: AppColors.clr8F8D8C, shape: BoxShape.circle, border: Border.all(color: AppColors.white, width: 1.h)),
                            child: Icon(
                              Icons.close,
                              color: AppColors.white,
                              size: 60.h,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ).paddingSymmetric(horizontal: 25.w),
            ),
            Container(
              decoration: const BoxDecoration(
                color: AppColors.black,
                gradient: LinearGradient(
                  colors: [Colors.transparent, Colors.transparent, Colors.black, Colors.black],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 0, 0.6, 1],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [CommonSVG(strIcon: Assets.svgs.svgKodyRobotsIcon.path, height: context.height * 0.1, width: context.width * 0.22), Image.asset(Assets.images.kodyRobotsQr.path)],
              ).paddingOnly(bottom: context.width * 0.05, left: context.width * 0.05, right: context.width * 0.05, top: context.height * 0.01),
            )
          ],
        ),
      ),
    );
  }
}
