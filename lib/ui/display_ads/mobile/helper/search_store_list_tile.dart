import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_display/framework/controller/map/map_controller.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/framework/controller/operator/store_controller.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/new_logic/model/elevator_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/framework/utils/extension/graph_extension.dart';
import 'package:odigo_display/framework/utils/helper/voice_helper.dart';
import 'package:odigo_display/ui/routing/delegate.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class SearchStoreListTile extends ConsumerWidget with BaseConsumerWidget {
  final StoreData store;
  final Function() onStoreSelect;
  final bool isFromMore;
  final Color? color;
  final Color? borderColor;
  final bool isFromCategoryDetails;

  const SearchStoreListTile({super.key, required this.store, required this.onStoreSelect, this.isFromMore = false, this.color, this.borderColor, this.isFromCategoryDetails = false});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final helpInfoDashboardWatch = ref.watch(helpInfoDashboardController);
    final displayScreenWatch = ref.watch(displayAdsController);
    final mapWatch = ref.watch(mapController);
    return InkWell(
      onTap: () async {
        showLog('store.storeImagePath store.storeImagePath ${store.storeImagePath}');
        RobotMetaData? robotMetaData = await ref.read(storeController).operatorRepository.getRobotMetaData();
        String floorNumber = (robotMetaData?.floorNumber ?? '').toString();
        showLog('check emergency ${RobotSession.emergencyStop}');
        showLog('store.floorNumber store.floorNumber store.floorNumber${store.floorNumber} == $floorNumber');
        if (!RobotSession.emergencyStop) {
          helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref, isFromMore: isFromMore);
          List<ElevatorData> elevatorNames = await ref.read(storeController).operatorRepository.getElevatorPointList();
          await displayScreenWatch.addInteraction(
            storeUuid: store.storeUuid,
            storeName: store.name,
            vendorUuid: store.vendorUuid,
          );

          showLog('elevator points $elevatorNames');
          if (store.floorNumber.toString() != floorNumber) {
            showStoreInfoDialog(globalNavigatorKey.currentContext!, LocaleKeys.keyAreYouSure.localized, '${LocaleKeys.keyYouAreReadyToVisit.localized} ${store.name} ${LocaleKeys.keyOn.localized} ${store.floorNumber} ${LocaleKeys.keyFloor.localized} ?', LocaleKeys.keyIfYouDontNeedAssistanceIWillContinueOnMyWayIn.localized, LocaleKeys.keyYesSure.localized, LocaleKeys.keyNoThanks.localized, store, helpInfoDashboardWatch.confirmationDialogKey, (isPositive) async {
              if (isPositive) {
                helpInfoDashboardWatch.searchTimer?.cancel();
                helpInfoDashboardWatch.searchTimer = null;
                if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                  helpInfoDashboardWatch.robotStopTimer?.cancel();
                  helpInfoDashboardWatch.robotStopTimer = null;
                }

                helpInfoDashboardWatch.updateSearchView(false);
                helpInfoDashboardWatch.updateSettingsView(false);
                showLog("<<<<<<<<<<<<<<<str>>>>>>>>>>>>>>>  ${elevatorNames.map((element) => element.name).toList().toString()}");

                /// TODO Migration
                if (elevatorNames.isNotEmpty) {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.nearestElevator);
                  if (isFromCategoryDetails) {
                    ref.read(navigationStackController).pop();
                  }
                  displayScreenWatch.startPointNavigation(ref: ref, name: elevatorNames.first.name ?? '');
                } else {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.takeStairs);
                }
                await Future.delayed(const Duration(milliseconds: 2000), () {
                  /// TODO Migration
                  // if (!helpInfoDashboardWatch.socketManager.isSocketError) {
                  //   onStoreSelect.call();
                  // }
                });
              } else {
                print('check functions ');
                globalRef?.read(helpInfoDashboardController).navigationStartInterruptionTime(navigationType: 'STORE', storeData: store, isRequestSubmitted: false);
                if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                  helpInfoDashboardWatch.robotStopTimer?.cancel();
                  helpInfoDashboardWatch.robotStopTimer = null;
                }
              }
            });
          } else {
            /// on Tap text
            showStoreInfoDialog(globalNavigatorKey.currentContext!, LocaleKeys.keyAreYouSure.localized, 'You’re Ready To Visit ${store.name} on ${store.floorNumber} floor ? ', 'If you don’t need assistance, I’ll continue on my way in', LocaleKeys.keyYesSure.localized, LocaleKeys.keyNoThanks.localized, store, helpInfoDashboardWatch.confirmationDialogKey, (isPositive) async {
              if (isPositive) {
                await mapWatch.getRobotCurrentLocation();
                helpInfoDashboardWatch.searchTimer?.cancel();
                helpInfoDashboardWatch.searchTimer = null;
                if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                  helpInfoDashboardWatch.robotStopTimer?.cancel();
                  helpInfoDashboardWatch.robotStopTimer = null;
                }
                helpInfoDashboardWatch.updateSearchView(false);
                helpInfoDashboardWatch.updateSettingsView(false);
                Iterable<LocationPointData>? storeList = store.locationPoints?.where((element) => element.storeData.target?.storeId == store.storeId);
                showLog('main location $storeList');
                if (storeList?.isNotEmpty ?? false) {
                  Offset pointLocation = Offset(store.locationPoints?[0].xaPoint ?? 0.0, store.locationPoints?[0].yaPoint ?? 0.0);
                  double dist = pointLocation.calculateDistance(Offset(mapWatch.currentPosition[0], mapWatch.currentPosition[1])).abs();
                  showLog("OFFSET ${Offset(mapWatch.currentPosition[0], mapWatch.currentPosition[1])}");
                  showLog("calculateDistance >> ${dist}");
                  if (dist <= 0.5) {
                    VoiceHelper.instance.playAudioFromAsset(AppAssets.alreadyAtStore);
                  } else {
                    int storeLocationIndex = displayScreenWatch.wayPointList.indexWhere((element) => element.name.toLowerCase() == storeList?.first.fieldValue?.toLowerCase());
                    showLog('storeLocationIndex $storeLocationIndex');
                    if (storeLocationIndex != -1) {
                      VoiceHelper.instance.playAudioFromAsset(AppAssets.followMe);
                    }
                    showLog('storeList?.first.fieldValue storeList?.first.fieldValue  111 ${storeList?.first.fieldValue}');
                    if (isFromCategoryDetails) {
                      ref.read(navigationStackController).pop();
                    }
                    displayScreenWatch.startPointNavigation(name: storeList?.first.fieldValue ?? '', ref: ref, storeData: storeList?.first.storeData.target);
                  }
                  helpInfoDashboardWatch.updateStartStoreNavigation(true);
                } else {
                  bool isDialogClose = false;
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.locationNotFound);
                  // newDashboardWatch.socketManager.audioPlayer(audioFile: AppAssets.locationNotFound);
                  showMessageDialog(context, 'Point not found', () {
                    isDialogClose = true;
                  });
                  print('else condition 1 $isDialogClose');

                  Future.delayed(Duration(seconds: stopRobotSeconds), () {
                    print('dialog close');
                    if (!isDialogClose) {
                      Navigator.of(globalNavigatorKey.currentContext!).pop();
                    }
                    displayScreenWatch.startCruiseMode(ref: globalRef!);
                  });
                }

                await Future.delayed(const Duration(milliseconds: 2000), () {
                  /// TODO Migration
                  // if (!helpInfoDashboardWatch.socketManager.isSocketError) {
                  //   onStoreSelect.call();
                  // }
                });
              } else {
                print('check functions 111');
                globalRef?.read(helpInfoDashboardController).navigationStartInterruptionTime(navigationType: 'STORE', storeData: store, isRequestSubmitted: false);
                // if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                //   helpInfoDashboardWatch.robotStopTimer?.cancel();
                //   helpInfoDashboardWatch.robotStopTimer = null;
                // }
              }
            });
          }
          Future.delayed(Duration(seconds: stopRobotSeconds), () {
            // if (helpInfoDashboardWatch.confirmationDialogKey.currentContext != null) {
            //   Navigator.pop(helpInfoDashboardWatch.confirmationDialogKey.currentContext!);
            // }
          });
        } else {
          VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceEmergencyPressed);
          showWidgetMessageDialog(
            context,
            title: LocaleKeys.keyEmergencyPressed.localized,
            message: LocaleKeys.keyEmergencyPressedDescription.localized,
            icon: Assets.svgs.svgEmergencyDialog.path,
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(color: color ?? AppColors.clr212121, borderRadius: BorderRadius.circular(70.r), border: Border.all(color: borderColor ?? AppColors.clr5E5E5E)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              // mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        height: context.width * 0.06,
                        width: context.width * 0.06,
                        margin: EdgeInsets.only(right: context.width * 0.01),
                        decoration: const BoxDecoration(shape: BoxShape.circle),
                        child: ClipOval(
                          child: ((store.storeImagePath?.isNotEmpty ?? false) && File(store.storeImagePath!).existsSync())
                              ? Image.file(
                                  File(store.storeImagePath?.replaceAll(' ', '') ?? ''),
                                  fit: BoxFit.cover,
                                  height: 50.h,
                                  width: 50.w,
                                )
                              : Container(
                                  color: AppColors.white,
                                  child: Icon(
                                    Icons.store,
                                    size: 40.h,
                                    color: AppColors.black,
                                  ),
                                ),
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonText(
                              title: '${store.name}',
                              textStyle: TextStyles.semiBold.copyWith(fontSize: 24.sp),
                            ).paddingOnly(left: 8.w),
                            CommonText(
                              title: store.categoryData.map((element) => element.name).toList().join(' | '),
                              textStyle: TextStyles.regular.copyWith(fontSize: 24.sp),
                              textOverflow: TextOverflow.ellipsis,
                            ).paddingOnly(left: 8.w).alignAtCenterLeft(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 20.w),
                CommonText(
                  title: '${store.floorNumber} F',
                  textStyle: TextStyles.semiBold.copyWith(fontSize: 24.sp),
                ),
                SizedBox(
                  width: 20.w,
                ),
                InkWell(
                  onTap: () async {
                    RobotMetaData? robotMetaData = await ref.read(storeController).operatorRepository.getRobotMetaData();
                    String floorNumber = (robotMetaData?.floorNumber ?? '').toString();
                    List<ElevatorData> elevatorNames = await ref.read(storeController).operatorRepository.getElevatorPointList();
                    await displayScreenWatch.addInteraction(
                      storeUuid: store.storeUuid,
                      storeName: store.name,
                      vendorUuid: store.vendorUuid,
                    );
                    showLog('store..floorNumber store.floorNumber${store.floorNumber} == $floorNumber');
                    if (!RobotSession.emergencyStop) {
                      if (store.floorNumber.toString() != floorNumber) {
                        showStoreInfoDialog(context, LocaleKeys.keyAreYouSure.localized, 'You’re Ready To Visit ${store.name} on ${store.floorNumber} floor ? ', 'If you don’t need assistance, I’ll continue on my way in', LocaleKeys.keyYesSure.localized, LocaleKeys.keyNoThanks.localized, store, helpInfoDashboardWatch.confirmationDialogKey, (isPositive) async {
                          if (isPositive) {
                            helpInfoDashboardWatch.searchTimer?.cancel();
                            helpInfoDashboardWatch.searchTimer = null;
                            if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                              helpInfoDashboardWatch.robotStopTimer?.cancel();
                              helpInfoDashboardWatch.robotStopTimer = null;
                            }
                            helpInfoDashboardWatch.updateSearchView(false);
                            helpInfoDashboardWatch.updateSettingsView(false);
                            VoiceHelper.instance.playAudioFromAsset(AppAssets.followMe);
                            showLog("<<<<<<<<<<<<<<<str>>>>>>>>>>>>>>>  ${elevatorNames.map((element) => element.name).toList().toString()}");

                            Navigator.of(context).pop();

                            /// TODO Migration
                            // helpInfoDashboardWatch.socketManager.startElevatorNavigation(elevatorNames.map((element) => element.name??'').toList());

                            await Future.delayed(const Duration(milliseconds: 2000), () {
                              /// TODO Migration
                              // if (!helpInfoDashboardWatch.socketManager.isSocketError) {
                              //   onStoreSelect.call();
                              // }
                            });
                          } else {
                            globalRef?.read(helpInfoDashboardController).navigationStartInterruptionTime(navigationType: 'STORE', storeData: store, isRequestSubmitted: false);
                            if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                              helpInfoDashboardWatch.robotStopTimer?.cancel();
                              helpInfoDashboardWatch.robotStopTimer = null;
                            }
                          }
                        });
                      } else {
                        //on tap of icon
                        helpInfoDashboardWatch.updateDialogShownCondition(true);
                        showStoreInfoDialog(context, LocaleKeys.keyAreYouSure.localized, 'You’re Ready To Visit ${store.name} on ${store.floorNumber} floor ? ', 'If you don’t need assistance, I’ll continue on my way in', LocaleKeys.keyYesSure.localized, LocaleKeys.keyNoThanks.localized, store, helpInfoDashboardWatch.confirmationDialogKey, (isPositive) async {
                          if (isPositive) {
                            helpInfoDashboardWatch.searchTimer?.cancel();
                            helpInfoDashboardWatch.searchTimer = null;
                            if (helpInfoDashboardWatch.robotStopTimer?.isActive ?? false) {
                              helpInfoDashboardWatch.robotStopTimer?.cancel();
                              helpInfoDashboardWatch.robotStopTimer = null;
                            }
                            helpInfoDashboardWatch.updateDialogShownCondition(false);
                            helpInfoDashboardWatch.updateSearchView(false);
                            helpInfoDashboardWatch.updateSettingsView(false);
                            Iterable<LocationPointData>? storeList = store.locationPoints?.where((element) => element.storeData.target?.storeId == store.storeId);
                            showLog('storeList storeList storeList storeList storeList storeList storeList storeList storeList $storeList');
                            if (storeList?.isNotEmpty ?? false) {
                              showLog('storeList?.first.fieldValue storeList?.first.fieldValue ${storeList?.first.fieldValue}');
                              int storeLocationIndex = displayScreenWatch.wayPointList.indexWhere((element) => element.name.toLowerCase() == storeList?.first.fieldValue?.toLowerCase());
                              showLog('storeLocationIndex $storeLocationIndex');
                              if (storeLocationIndex != -1) {
                                VoiceHelper.instance.playAudioFromAsset(AppAssets.followMe);
                              }
                              if (isFromCategoryDetails) {
                                ref.read(navigationStackController).pop();
                              }

                              /// TODO Migration
                              displayScreenWatch.startPointNavigation(name: storeList?.first.fieldValue ?? '', ref: ref, storeData: storeList?.first.storeData.target);
                              // helpInfoDashboardWatch.socketManager.startStoreNavigation(store, storeList?.first.fieldValue ?? '');
                              helpInfoDashboardWatch.updateStartStoreNavigation(true);
                            } else {
                              bool isDialogClose = false;
                              helpInfoDashboardWatch.updateDialogShownCondition(false);

                              /// TODO Migration
                              VoiceHelper.instance.playAudioFromAsset(AppAssets.locationNotFound);
                              // newDashboardWatch.socketManager.audioPlayer(audioFile: AppAssets.locationNotFound);
                              showMessageDialog(context, 'Point not found', () {
                                isDialogClose = true;
                              });
                              print('else condition 2 $isDialogClose');
                              Future.delayed(Duration(seconds: stopRobotSeconds), () {
                                print('dialog close');
                                if (!isDialogClose) {
                                  Navigator.of(globalNavigatorKey.currentContext!).pop();
                                }
                                displayScreenWatch.startCruiseMode(ref: globalRef!);
                              });
                            }

                            await Future.delayed(const Duration(milliseconds: 2000), () {
                              /// TODO Migration
                              // if (!helpInfoDashboardWatch.socketManager.isSocketError) {
                              //   onStoreSelect.call();
                              // }
                            });
                          } else {
                            globalRef?.read(helpInfoDashboardController).navigationStartInterruptionTime(navigationType: 'STORE', storeData: store, isRequestSubmitted: false);
                          }
                        });
                      }
                      helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref, isFromMore: isFromMore);
                      Future.delayed(Duration(seconds: stopRobotSeconds), () {
                        // if (helpInfoDashboardWatch.confirmationDialogKey.currentContext != null) {
                        //   Navigator.pop(helpInfoDashboardWatch.confirmationDialogKey.currentContext!);
                        // }
                      });
                    } else {
                      VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceEmergencyPressed);
                      showWidgetMessageDialog(
                        context,
                        title: LocaleKeys.keyEmergencyPressed.localized,
                        message: LocaleKeys.keyEmergencyPressedDescription.localized,
                        icon: Assets.svgs.svgEmergencyDialog.path,
                      );
                    }
                  },
                  child: CommonSVG(
                    strIcon: Assets.svgs.svgNavigateLocation.path,
                    height: context.height * 0.018,
                  ).paddingOnly(right: 15.w),
                ),
              ],
            ).paddingOnly(left: 15.w, top: 9.w, right: 6.w, bottom: 10.w).paddingOnly(left: 4.w, right: 10.w, top: 4.w),
          ],
        ),
      ),
    );
  }
}
