import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:lottie/lottie.dart';
import 'package:odigo_display/framework/controller/deployment/deployment_controller.dart';

// import 'package:kiosk_mode/kiosk_mode.dart';
import 'package:odigo_display/framework/controller/download/download_controller.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/framework/provider/network/api_end_points.dart';
import 'package:odigo_display/framework/repository/new_logic/model/download_ad_model.dart';
import 'package:odigo_display/framework/utils/extension/double_extension.dart';
import 'package:odigo_display/ui/display_ads/mobile/helper/guide_screen.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_appbar_web.dart';
import 'package:odigo_display/ui/utils/widgets/empty_state_widget.dart';

class DownloadScreenMobile extends ConsumerStatefulWidget {
  const DownloadScreenMobile({super.key, this.forcefullyCallSyncApi});

  final bool? forcefullyCallSyncApi;

  @override
  ConsumerState createState() => _DownloadScreenMobileState();
}

class _DownloadScreenMobileState extends ConsumerState<DownloadScreenMobile> {
  /// Download & Install New Apk File
  var methodChannel = const MethodChannel("storageChannel");
  var mainServiceChannel = const MethodChannel('odigoAndroidChannel');

  Future<void> _downloadNewApkMethod(String url) async {
    final checkDeploymentWatch = ref.watch(checkDeploymentController);

    String? apkFilePath = await checkDeploymentWatch.downloadApk(url);
    if (apkFilePath != '') {
      try {
        final String result = await mainServiceChannel.invokeMethod('sendApkName', {'data': apkFilePath});
        showLog('Received from Native: ${result.runtimeType} $result');
        if (result == 'true') {
          showLog('Check result $result');

          RobotSession.apkVersion = checkDeploymentWatch.checkDeploymentApkState.success?.data?.version ?? RobotSession.apkVersion;

          globalRef?.read(downloadController).newLogicRepository.clearAllData();
        }
      } on PlatformException catch (e) {
        showLog("Error installing APK: $e");
      } catch (e, s) {
        showLog("Error From Download New Apk---\n$e\n--StackTrack--\n$s");
      }
    } else {
      checkDeploymentWatch.newAppDownload = false;
      checkDeploymentWatch.isDownloadingError = true;
      checkDeploymentWatch.updateUi();
      showMessageDialog(context, 'Downloading APK failed \n $url', () => null);
    }
  }

  /// Check APK Version and Installation
  Future<void> checkApkVersionAndInstallation() async {
    final checkDeploymentWatch = ref.read(checkDeploymentController);
    final downloadWatch = ref.read(downloadController);

    try {
      await checkDeploymentWatch.checkDeploymentApi(context).then((value) async {
        showLog('Check version status for deployment ${value?.status}');
        if (value?.status == ApiEndPoints.apiStatus_200) {
          showLog('RobotSession.apkVersion ${RobotSession.apkVersion}, ${value?.data?.version} ');
          if ((value?.data?.version != null) && (value?.data?.version != RobotSession.apkVersion) && (RobotSession.apkVersion < (value?.data?.version ?? 0))) {
            if (value?.data?.buildUrl != null && value?.data?.buildUrl != '') {
              final operatorCTR = ref.read(operatorController);
              await operatorCTR.deductWalletBalanceApi(context, isFromDownloading: true);
              await operatorCTR.uploadInteractionDataAPI(context, isFromDownloading: true);

              await _downloadNewApkMethod(value?.data?.buildUrl ?? '');
            }
          } else {
            showLog('checkDeploymentWatch.isDownloadingError checkDeploymentWatch.isDownloadingError ${checkDeploymentWatch.isDownloadingError}');
            if (mounted && checkDeploymentWatch.newAppDownload == false && checkDeploymentWatch.isDownloadingError == false) {
              downloadWatch.initCall(context, ref);
              await downloadWatch.checkSequenceApi(context, ref, forcefullyCallSyncApi: widget.forcefullyCallSyncApi);
            }
          }
        } else {
          // showWidgetDialog(context, errorDialogWidget());

          if (mounted) {
            downloadWatch.initCall(context, ref);
            await downloadWatch.checkSequenceApi(context, ref, forcefullyCallSyncApi: widget.forcefullyCallSyncApi);
          }
        }
      });
    } on PlatformException catch (e) {
      showLog("Error sending data to native: ${e.message}");
    } catch (e, s) {
      showLog("Error From Check Apk Version And Installation---\n$e\n--StackTrack--\n$s");
    }
  }

  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final downloadWatch = ref.read(downloadController);
      final watchOperatorController = ref.read(operatorController);
      final checkDeploymentWatch = ref.read(checkDeploymentController);
      downloadWatch.totalAdDownloadedCount = 0;
      downloadWatch.totalAssetDownloadedCount = 0;
      await downloadWatch.createAssetAd();

      if (mounted) {
        await watchOperatorController.getLanguageListAPI(context);
      }

      /// File Download Permission
      // await methodChannel.invokeMethod('askInstallPermission');

      /// Downloads file & Install once get Permission
      // await _downloadNewApkMethod();
      /// Check APK Version and Installation
      await checkApkVersionAndInstallation();

      /// Syncing new Data From Server
      /// (Sequence Api)
      // if (mounted && checkDeploymentWatch.newAppDownload == false) {
      //   downloadWatch.initCall(context, ref);
      //   downloadWatch.checkSequenceApi(context, ref, forcefullyCallSyncApi: widget.forcefullyCallSyncApi);
      // }
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final downloadWatch = ref.watch(downloadController);
    final checkDeploymentWatch = ref.watch(checkDeploymentController);

    return Scaffold(
      appBar: CommonAppbar(
        titleName: LocaleKeys.keyDownloads.localized,
        actions: [
          if (downloadWatch.showRetryAll())
            InkWell(
              onTap: () {
                downloadWatch.retryAll(context, ref);
              },
              child: Icon(CupertinoIcons.refresh_circled_solid, color: AppColors.white, size: 50.r).paddingOnly(right: 25.w, bottom: 10.h, top: 10.h),
            ),
        ],
      ),
      backgroundColor: AppColors.primary,
      body: Stack(
        children: [
          Column(
            children: [
              Container(
                alignment: Alignment.center,
                height: context.height * 0.05,
                margin: EdgeInsets.symmetric(horizontal: context.width * 0.03),
                decoration: BoxDecoration(
                  color: AppColors.clr212121,
                  borderRadius: BorderRadius.circular(32.r),
                ),
                child: Row(
                  children: List.generate(downloadWatch.downloadTabList.length, (index) {
                    return Expanded(
                      child: InkWell(
                        onTap: () {
                          downloadWatch.updateSelectedDownloadTab((downloadWatch.downloadTabList[index]));
                        },
                        child: Container(
                          height: context.height,
                          padding: EdgeInsets.symmetric(horizontal: context.width * 0.02),
                          alignment: Alignment.center,
                          decoration: (downloadWatch.selectedDownloadTab == downloadWatch.downloadTabList[index])
                              ? BoxDecoration(
                                  border: Border.all(color: AppColors.white, width: 2.h),
                                  borderRadius: BorderRadius.circular(32.r),
                                )
                              : null,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CommonText(
                                title: (downloadWatch.downloadTabList[index]).localized,
                                textStyle: TextStyles.medium.copyWith(
                                  fontSize: 40.sp,
                                  fontFamily: TextStyles.manropeFontFamily,
                                  color: (downloadWatch.selectedDownloadTab == downloadWatch.downloadTabList[index]) ? AppColors.white : AppColors.clr545454,
                                ),
                              ),
                              CommonText(
                                title: downloadWatch.downloadTabList[index] == LocaleKeys.keyAdvertisements ? '(${downloadWatch.totalAdDownloadedCount}/${downloadWatch.uniqueAdDetails.length})' : '(${downloadWatch.totalAssetDownloadedCount}/${downloadWatch.assetDownloadList.length})',
                                textStyle: TextStyles.medium.copyWith(
                                  fontSize: 40.sp,
                                  fontFamily: TextStyles.manropeFontFamily,
                                  color: (downloadWatch.selectedDownloadTab == downloadWatch.downloadTabList[index]) ? AppColors.white : AppColors.clr545454,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
              SizedBox(height: context.height * 0.03),
              Expanded(
                child: downloadWatch.selectedDownloadTab == LocaleKeys.keyAdvertisements
                    ? downloadWatch.uniqueAdDetails.isNotEmpty
                        ? AbsorbPointer(
                            absorbing: downloadWatch.showProcessingDialog,
                            child: ListView.separated(
                              padding: EdgeInsets.only(bottom: 200.h),
                              itemCount: downloadWatch.uniqueAdDetails.length,
                              itemBuilder: (BuildContext context, int index) {
                                DownloadAdModel? adDetail = downloadWatch.uniqueAdDetails[index];
                                if (adDetail.adDetails == null) {
                                  return const Offstage();
                                } else {
                                  var fileName = adDetail.adDetails?.isFileNotFound == true
                                      ? 'Common Ad'
                                      : (adDetail.adDetails!.adUrl ?? 'NA').contains('?subDir')
                                          ? adDetail.adDetails!.adUrl!.split('?subDir').firstOrNull?.split('/').last ?? ''
                                          : '';
                                  return Container(
                                    margin: EdgeInsets.symmetric(horizontal: context.width * 0.040),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: context.width * 0.030,
                                      vertical: context.height * 0.020,
                                    ),
                                    //todo: put proper condition
                                    decoration: BoxDecoration(color: (!adDetail.isDownloadFailed) ? AppColors.clr212121 : AppColors.redFF3E3E, borderRadius: BorderRadius.circular(50.r)),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            CommonSVG(
                                              strIcon: Assets.svgs.svgZipFile.keyName,
                                              height: context.width * 0.05,
                                              width: context.width * 0.05,
                                            ).paddingOnly(right: context.width * 0.025),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  CommonText(
                                                    title: fileName,
                                                    textStyle: TextStyles.extraBold.copyWith(fontSize: 46.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                                                  ).paddingOnly(bottom: context.height * 0.015),
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(40.r),
                                                      color: AppColors.white,
                                                    ),
                                                    padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
                                                    child: CommonText(
                                                      title: (adDetail.downloadPercentage != 0 && adDetail.downloadPercentage != 100 && (!adDetail.isDownloadFailed)) ? '${(adDetail.downloadSize * (adDetail.downloadPercentage / 100)).getFileSizeString} / ${adDetail.downloadSize.getFileSizeString}' : adDetail.downloadSize.getFileSizeString,
                                                      textStyle: TextStyles.extraBold.copyWith(fontSize: 24.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                                                    ),
                                                  ).paddingOnly(bottom: context.height * 0.015),
                                                  CommonText(
                                                    title: adDetail.uuid.toString(),
                                                    textStyle: TextStyles.medium.copyWith(fontSize: 30.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                                                  ).paddingOnly(bottom: context.height * 0.005),
                                                ],
                                              ),
                                            ),
                                            SizedBox(width: context.width * 0.05),
                                            adDetail.isDownloadFailed
                                                ? InkWell(
                                                    onTap: () async {
                                                      adDetail.isRetried = true;
                                                      adDetail.isDownloadFailed = false;
                                                      downloadWatch.notifyListeners();
                                                      adDetail.downloadTaskId = await FlutterDownloader.retry(taskId: adDetail.downloadTaskId ?? '');
                                                      if (!adDetail.isUnzipFailed) {
                                                        await FlutterDownloader.cancel(taskId: adDetail.downloadTaskId ?? '');
                                                      }
                                                      var id = await FlutterDownloader.enqueue(
                                                        url: adDetail.adDetails!.adUrl!,
                                                        savedDir: adDetail.directoryPath,
                                                        headers: {},
                                                        showNotification: false,
                                                        openFileFromNotification: false,
                                                        fileName: fileName,
                                                        // timeout: downloadTimeout,
                                                        allowCellular: isAllowCellular,
                                                      );
                                                      adDetail.downloadTaskId = id;
                                                      adDetail.isDownloadFailed = false;
                                                      adDetail.isRetried = true;
                                                      downloadWatch.notifyListeners();
                                                    },
                                                    child: Icon(CupertinoIcons.refresh_thick, color: AppColors.white, size: 60.sp),
                                                  )
                                                : adDetail.isRetried || (adDetail.downloadPercentage > 0 && adDetail.downloadPercentage < 100)
                                                    ? Stack(
                                                        alignment: Alignment.center,
                                                        children: [
                                                          SizedBox(
                                                            height: context.height * 0.04,
                                                            width: context.height * 0.04,
                                                            child: CircularProgressIndicator(
                                                              value: (adDetail.isRetried
                                                                  ? null
                                                                  : adDetail.downloadPercentage < 0
                                                                      ? 0
                                                                      : (adDetail.downloadPercentage / 100)),
                                                              color: AppColors.green,
                                                              strokeWidth: 8.sp,
                                                              backgroundColor: AppColors.clrE5E5E5,
                                                            ),
                                                          ),
                                                          CommonText(
                                                            title: '${(adDetail.downloadPercentage < 0 ? 0 : adDetail.downloadPercentage.toStringAsFixed(0))}%',
                                                            textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontFamily: TextStyles.manropeFontFamily, fontSize: 26.sp),
                                                          )
                                                        ],
                                                      )
                                                    : adDetail.downloadPercentage != 100
                                                        ? LoadingAnimationWidget.waveDots(color: AppColors.white, size: 40.h).alignAtCenterLeft().paddingOnly(left: 5.w)
                                                        : (!adDetail.isUnzipped)
                                                            ? !adDetail.isUnZipping
                                                                ? InkWell(
                                                                    onTap: () async {
                                                                      adDetail.isRetried = true;
                                                                      downloadWatch.notifyListeners();
                                                                      adDetail.downloadTaskId = await FlutterDownloader.enqueue(
                                                                        url: adDetail.adDetails!.adUrl!,
                                                                        savedDir: adDetail.directoryPath,
                                                                        headers: {},
                                                                        showNotification: false,
                                                                        openFileFromNotification: false,
                                                                        fileName: fileName,
                                                                        // timeout: downloadTimeout,
                                                                        allowCellular: isAllowCellular,
                                                                      );
                                                                    },
                                                                    child: Icon(CupertinoIcons.refresh_thick, color: AppColors.redFF3E3E, size: 60.sp),
                                                                  )
                                                                : Lottie.asset(Assets.animation.animUnzip.path, height: context.height * 0.05)
                                                            : Icon(Icons.download_done, color: AppColors.white, size: 80.sp),
                                            SizedBox(width: context.width * 0.05),
                                          ],
                                        ),
                                        // CommonText(
                                        //   title: adDetail.filePath,
                                        //   textStyle: TextStyles.medium.copyWith(fontSize: 30.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                                        //   maxLines: 3,
                                        // ).paddingOnly(left: context.width * 0.075),
                                      ],
                                    ),
                                  );
                                }
                              },
                              separatorBuilder: (BuildContext context, int index) {
                                return SizedBox(height: context.height * 0.031);
                              },
                            ),
                          )
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Expanded(child: SizedBox()),
                              const EmptyStateWidget(),
                              const Expanded(child: SizedBox()),
                              CommonButton(
                                onTap: () async {
                                  await downloadWatch.checkSequenceApi(context, ref, forcefullyCallSyncApi: widget.forcefullyCallSyncApi);
                                },
                                buttonText: LocaleKeys.keyRefresh.localized,
                                buttonTextColor: AppColors.black,
                                backgroundColor: AppColors.white,
                                width: context.width * 0.4,
                              ),
                              SizedBox(height: context.height * 0.02),
                            ],
                          )
                    : downloadWatch.assetDownloadList.isNotEmpty
                        ? AbsorbPointer(
                            absorbing: downloadWatch.showProcessingDialog,
                            child: ListView.separated(
                              itemCount: downloadWatch.assetDownloadList.length,
                              itemBuilder: (BuildContext context, int index) {
                                DownloadAssetModel? assetModel = downloadWatch.assetDownloadList[index];
                                return Container(
                                  margin: EdgeInsets.symmetric(horizontal: context.width * 0.030),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: context.width * 0.030,
                                    vertical: context.height * 0.020,
                                  ),
                                  decoration: BoxDecoration(color: (!assetModel.isDownloadFailed) ? AppColors.clr212121 : AppColors.redFF3E3E, borderRadius: BorderRadius.circular(50.r)),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          CommonSVG(
                                            strIcon: Assets.svgs.svgZipFile.keyName,
                                            height: context.width * 0.05,
                                            width: context.width * 0.05,
                                          ).paddingOnly(right: context.width * 0.025),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                CommonText(
                                                  title: assetModel.name,
                                                  textStyle: TextStyles.extraBold.copyWith(fontSize: 46.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                                                ).paddingOnly(bottom: context.height * 0.015),
                                                Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(40.r),
                                                    color: AppColors.white,
                                                  ),
                                                  padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
                                                  child: CommonText(
                                                    title: (assetModel.downloadPercentage != 0 && assetModel.downloadPercentage != 100 && (!assetModel.isDownloadFailed)) ? '${(assetModel.downloadSize * (assetModel.downloadPercentage / 100)).getFileSizeString} / ${assetModel.downloadSize.getFileSizeString}' : assetModel.downloadSize.getFileSizeString,
                                                    textStyle: TextStyles.extraBold.copyWith(fontSize: 24.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                                                  ),
                                                ).paddingOnly(bottom: context.height * 0.015),
                                                CommonText(
                                                  title: assetModel.uuid.toString(),
                                                  textStyle: TextStyles.medium.copyWith(fontSize: 30.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                                                ).paddingOnly(bottom: context.height * 0.005),
                                              ],
                                            ),
                                          ),
                                          SizedBox(width: context.width * 0.05),
                                          assetModel.isDownloadFailed
                                              ? InkWell(
                                                  onTap: () async {
                                                    assetModel.isRetried = true;
                                                    assetModel.isDownloadFailed = false;
                                                    downloadWatch.notifyListeners();
                                                    await FlutterDownloader.cancel(taskId: assetModel.downloadTaskId ?? '');
                                                    var id = await FlutterDownloader.enqueue(
                                                      url: assetModel.downloadUrl,
                                                      savedDir: assetModel.directoryPath,
                                                      headers: {},
                                                      showNotification: false,
                                                      openFileFromNotification: false,
                                                      fileName: assetModel.fileName,
                                                      // timeout: downloadTimeout,
                                                      allowCellular: isAllowCellular,
                                                    );
                                                    assetModel.downloadTaskId = id;
                                                    assetModel.isDownloadFailed = false;
                                                    assetModel.isRetried = true;

                                                    downloadWatch.notifyListeners();
                                                  },
                                                  child: Icon(CupertinoIcons.refresh_thick, color: AppColors.white, size: 60.sp),
                                                )
                                              : assetModel.isRetried || (assetModel.downloadPercentage > 0 && assetModel.downloadPercentage < 100)
                                                  ? Stack(
                                                      alignment: Alignment.center,
                                                      children: [
                                                        SizedBox(
                                                          height: context.height * 0.04,
                                                          width: context.height * 0.04,
                                                          child: CircularProgressIndicator(
                                                            value: (assetModel.isRetried ? null : (assetModel.downloadPercentage / 100)),
                                                            color: AppColors.green,
                                                            strokeWidth: 8.sp,
                                                            backgroundColor: AppColors.clrE5E5E5,
                                                          ),
                                                        ),
                                                        CommonText(
                                                          title: '${assetModel.downloadPercentage.toStringAsFixed(0)}%',
                                                          textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontFamily: TextStyles.manropeFontFamily, fontSize: 26.sp),
                                                        )
                                                      ],
                                                    )
                                                  : assetModel.downloadPercentage != 100
                                                      ? LoadingAnimationWidget.waveDots(color: AppColors.white, size: 40.h).alignAtCenterLeft().paddingOnly(left: 5.w)
                                                      : Icon(Icons.download_done, color: AppColors.white, size: 80.sp),
                                          SizedBox(width: context.width * 0.05),
                                        ],
                                      ),
                                      // Row(
                                      //   children: [
                                      //     Expanded(
                                      //       child: CommonText(
                                      //         title: assetModel.filePath,
                                      //         textStyle: TextStyles.medium.copyWith(fontSize: 30.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                                      //         maxLines: 3,
                                      //       ).paddingOnly(left: context.width * 0.075),
                                      //     ),
                                      //   ],
                                      // ),
                                    ],
                                  ),
                                );
                              },
                              separatorBuilder: (BuildContext context, int index) {
                                return SizedBox(height: context.height * 0.031);
                              },
                            ),
                          )
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Expanded(child: SizedBox()),
                              const EmptyStateWidget(),
                              const Expanded(child: SizedBox()),
                              CommonButton(
                                onTap: () async {
                                  await downloadWatch.checkSequenceApi(context, ref, forcefullyCallSyncApi: widget.forcefullyCallSyncApi);
                                },
                                buttonText: LocaleKeys.keyRefresh.localized,
                                buttonTextColor: AppColors.black,
                                backgroundColor: AppColors.white,
                                width: context.width * 0.4,
                              ),
                              SizedBox(height: context.height * 0.02),
                            ],
                          ),
              )
            ],
          ),
          if (downloadWatch.syncedDataState.isLoading)
            SizedBox(
              height: context.height,
              child: GuideScreenMobile(
                anim: Assets.animation.animLoadingData.path,
                title: LocaleKeys.keyFetchingData.localized,
                subTitle: LocaleKeys.keyFetchingDataDesc.localized,
                doPop: false,
              ),
            ),
          if (downloadWatch.showProcessingDialog)
            SizedBox(
              height: context.height,
              child: GuideScreenMobile(
                anim: Assets.animation.animProcessingData.path,
                title: LocaleKeys.keyProcessingData.localized,
                subTitle: LocaleKeys.keyProcessingDataDesc.localized,
                doPop: false,
              ),
            ),
          (checkDeploymentWatch.newAppDownload == true)
              ? showDownloadFileDialog(
                  context,
                  'Updating App',
                  'Please wait till we update app to latest version',
                  downloadPercentage: checkDeploymentWatch.downloadProgress.toStringAsFixed(0),
                )
              : const Offstage(),

          /// Skip Button
          if (downloadWatch.isShownSkipButton && downloadWatch.lastSync != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(child: SizedBox(height: context.height * 0.02)),

                /// Skip button
                Center(
                  child: CommonButton(
                    backgroundColor: AppColors.white,
                    borderColor: AppColors.white,
                    buttonTextColor: AppColors.black,
                    onTap: () {
                      globalRef?.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.displayAds(startRouteNavigation: true, isFromWaitingScreen: false));
                    },
                    buttonText: '${LocaleKeys.keySkip.localized} Last sync at : ${DateTime.fromMillisecondsSinceEpoch(int.parse(downloadWatch.lastSync ?? '0'))}',
                  ),
                ).paddingSymmetric(horizontal: 30.w),
                SizedBox(height: context.height * 0.02),
              ],
            ),
        ],
      ),
    );
  }

  errorDialogWidget() {
    final downloadWatch = ref.read(downloadController);

    return Container(
      // height: context.height * 0.7,
      // width: context.width,
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: context.height * 0.05),
          CommonSVG(
            strIcon: Assets.svgs.svgAbnormalCommunication.path,
            height: context.width * 0.13,
            width: context.width * 0.13,
          ),
          SizedBox(height: context.height * 0.03),
          Column(
            children: [
              CommonText(
                title: LocaleKeys.keyAlert.localized,
                textAlign: TextAlign.center,
                textStyle: TextStyles.bold.copyWith(color: AppColors.red),
              ),
              SizedBox(height: context.height * 0.02),
              CommonText(
                title: LocaleKeys.keyPleaseContactTheAdminToDownloadNewApp.localized,
                maxLines: 10,
                textAlign: TextAlign.center,
              ),
            ],
          ).paddingSymmetric(horizontal: context.width * 0.13),
          const Divider(color: AppColors.black).paddingSymmetric(vertical: context.height * 0.01),
          // SizedBox(width: 50.w),
          CommonButton(
            // height: context.height * 0.03,
            onTap: () async {
              Navigator.pop(context);
              downloadWatch.initCall(context, ref);
              await downloadWatch.checkSequenceApi(context, ref, forcefullyCallSyncApi: widget.forcefullyCallSyncApi);
            },
            borderRadius: BorderRadius.circular(100.r),
            buttonText: LocaleKeys.keyConfirm.localized,
            buttonTextStyle: TextStyles.regular.copyWith(color: AppColors.black, fontSize: 35.sp),
            backgroundColor: AppColors.white,
          ).paddingSymmetric(vertical: 15.h)
        ],
      ),
    );
  }
}
