import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/scheduler.dart';
import 'package:odigo_display/framework/controller/export/export_controller.dart';
import 'package:odigo_display/framework/controller/operator/store_controller.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/app_colors.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_appbar_web.dart';
import 'package:odigo_display/ui/utils/widgets/dialog_progressbar.dart';

class ExportScreenMobile extends ConsumerStatefulWidget {
  const ExportScreenMobile({Key? key}) : super(key: key);

  @override
  ConsumerState<ExportScreenMobile> createState() => _ExportScreenMobileState();
}

class _ExportScreenMobileState extends ConsumerState<ExportScreenMobile> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final exportScreenWatch = ref.read(exportController);
      exportScreenWatch.disposeController(isNotify: true);
      exportScreenWatch.getStoreList();
      exportScreenWatch.addExportData();
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final exportScreenWatch = ref.watch(exportController);
    return Stack(
      children: [
        Scaffold(
          backgroundColor: AppColors.primary,
          body: _bodyWidget(),
        ),
        exportScreenWatch.isStoreListLoad
            ? const Center(
                child: SizedBox(
                    height: 40,
                    width: 40,
                    child: CircularProgressIndicator(
                      color: AppColors.white,
                    )),
              )
            : const SizedBox(),
      ],
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final exportScreenWatch = ref.read(exportController);
    return Column(
      children: [
        SizedBox(
          height: 100.h,
        ),
        Row(
          children: [
            IconButton(
              onPressed: () {
                ref.read(navigationStackController).pop();
              },
              icon: Icon(
                Icons.keyboard_backspace,
                size: 50.h,
                color: AppColors.white,
              ),
            ),
            SizedBox(
              width: 10.w,
            ),
            CommonText(
              title: 'Export',
              textStyle: TextStyles.semiBold.copyWith(fontSize: 48.sp, color: AppColors.white),
            ),
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        ...List.generate(
          exportScreenWatch.exportList.length,
          (index) {
            return InkWell(
              onTap: () async {
                ref.read(navigationStackController).push(NavigationStackItem.exportDetailsScreen(storeList: exportScreenWatch.storeList));
              },
              child: Container(
                height: 100.h,
                width: context.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: AppColors.primary,
                  border: Border.all(color: AppColors.white),
                ),
                child: CommonText(
                  title: exportScreenWatch.exportList[index].title,
                  textStyle: TextStyles.medium,
                ).paddingAll(16.h),
              ),
            );
          },
        )
      ],
    ).paddingAll(20.h);
  }
}
