import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/scheduler.dart';
import 'package:odigo_display/framework/controller/export/export_controller.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class ExportScreenWeb extends ConsumerStatefulWidget {
  const ExportScreenWeb({Key? key}) : super(key: key);

  @override
  ConsumerState<ExportScreenWeb> createState() => _ExportScreenWebState();
}

class _ExportScreenWebState extends ConsumerState<ExportScreenWeb> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final exportScreenWatch = ref.watch(exportController);
      exportScreenWatch.disposeController(isNotify: true);
      exportScreenWatch.getStoreList();
      exportScreenWatch.addExportData();
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final exportScreenWatch = ref.watch(exportController);
    return Stack(
      children: [
        Scaffold(
          backgroundColor: AppColors.primary,
          body: _bodyWidget(),
        ),
        exportScreenWatch.isStoreListLoad
            ? const Center(
          child: SizedBox(
              height: 40,
              width: 40,
              child: CircularProgressIndicator(
                color: AppColors.white,
              )),
        )
            : const SizedBox(),
      ],
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final exportScreenWatch = ref.read(exportController);
    return Column(
      children: [
        SizedBox(
          height: 100.h,
        ),
        Row(
          children: [
            IconButton(
              onPressed: () {
                ref.read(navigationStackController).pop();
              },
              icon: Icon(
                Icons.keyboard_backspace,
                size: 50.h,
                color: AppColors.white,
              ),
            ),
            SizedBox(
              width: 10.w,
            ),
            CommonText(
              title: LocaleKeys.keyExport.localized,
              textStyle: TextStyles.semiBold.copyWith(fontSize: 48.sp, color: AppColors.white),
            ),
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        ...List.generate(
          exportScreenWatch.exportList.length,
              (index) {
            return InkWell(
              onTap: () async {
                ref.read(navigationStackController).push(NavigationStackItem.exportDetailsScreen(storeList: exportScreenWatch.storeList));
              },
              child: Container(
                height: 100.h,
                width: context.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: AppColors.primary,
                  border: Border.all(color: AppColors.white),
                ),
                child: CommonText(
                  title: exportScreenWatch.exportList[index].title,
                  textStyle: TextStyles.medium,
                ).paddingAll(16.h),
              ),
            );
          },
        )
      ],
    ).paddingAll(20.h);
  }
}
