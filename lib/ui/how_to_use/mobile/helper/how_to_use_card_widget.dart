import 'package:odigo_display/ui/utils/theme/theme.dart';

class HowToUseCardWidget extends StatelessWidget with BaseStatelessWidget {
  final String titleText;
  final String descText;
  final String imagePath;

  const HowToUseCardWidget({super.key, required this.titleText, required this.descText, required this.imagePath});

  @override
  Widget buildPage(BuildContext context) {
    return Container(
      width: context.width,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: AppColors.clr191919,
        borderRadius: BorderRadius.circular(33.r),
        border: Border.all(color: AppColors.clr343434),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          /// Text Column
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                /// Title Text
                CommonText(
                  title: titleText,
                  textStyle: TextStyles.bold.copyWith(
                    fontSize: 30.sp,
                    color: AppColors.white,
                    fontFamily: TextStyles.manropeFontFamily,
                  ),
                ),
                SizedBox(height: context.height * 0.01),

                /// Desc Text
                CommonText(
                  title: descText,
                  maxLines: 2,
                  textStyle: TextStyles.light.copyWith(
                    fontSize: 20.sp,
                    color: AppColors.white,
                    fontFamily: TextStyles.manropeFontFamily,
                  ),
                ),
              ],
            ),
          ),

          /// Image
          Flexible(
            child: Image.asset(
              imagePath,
              height: context.height * 0.2,
              width: context.width * 0.28,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ).paddingSymmetric(horizontal: context.width * 0.03),
    );
  }
}
