import 'dart:async';

import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/framework/controller/how_to_use/how_to_use_controller.dart';
import 'package:odigo_display/ui/how_to_use/mobile/helper/how_to_use_card_widget.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';

class HowToUseMobile extends ConsumerStatefulWidget {
  const HowToUseMobile({super.key});

  @override
  ConsumerState<HowToUseMobile> createState() => _HowToUseMobileState();
}

class _HowToUseMobileState extends ConsumerState<HowToUseMobile> with BaseConsumerStatefulWidget {
  Timer? timer;
  ScrollController? scrollController;

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      scrollController = ScrollController();
      SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
        final helpInfoDashboardWatch = ref.read(helpInfoDashboardController);
        helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref, isFromMore: true,);
        scrollController?.addListener(() {
          final helpInfoDashboardRead = ref.watch(helpInfoDashboardController);
          helpInfoDashboardRead.stopRobotForFewSeconds(context, ref: ref, isFromMore: true);
        });
      });
      super.initState();
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    timer = null;
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    final howToUseWatch = ref.watch(howToUseController);
    return Scaffold(
      body: Container(
        color: AppColors.clr0F0F0F,
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// Top Back Icon
                const Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CommonBackWidget(
                      color: AppColors.white,
                    ),
                  ],
                ),

                CommonText(
                  title: LocaleKeys.keyHowToUseOdigo.localized,
                  textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                ),
                SizedBox(height: context.height * 0.01),
                CommonText(
                  title: LocaleKeys.keyHowToUseOdigoDesc.localized,
                  textStyle: TextStyles.light.copyWith(fontSize: 20.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
                ),
                SizedBox(height: context.height * 0.015),

                /// How To Use Card Widget
                Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: howToUseWatch.howToUseDataList.length,
                    itemBuilder: (BuildContext context, int index) {
                      HowToUseDataModel howToUseData = howToUseWatch.howToUseDataList[index];
                      return HowToUseCardWidget(
                        titleText: howToUseData.titleText,
                        descText: howToUseData.descText,
                        imagePath: howToUseData.imagePath,
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return SizedBox(height: context.height * 0.025);
                    },
                  ),
                ),
              ],
            ).paddingSymmetric(vertical: context.height * 0.05, horizontal: context.width * 0.025),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [CommonSVG(strIcon: Assets.svgs.svgKodyRobotsIcon.path, height: context.height * 0.1, width: context.width * 0.22), Image.asset(Assets.images.kodyRobotsQr.path)],
              ).paddingOnly(left: context.width * 0.03, right: context.width * 0.03, bottom: context.height * 0.01),
            ),
          ],
        ),
      ),
    );
  }
}
