import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class MainOperatorScreenTile extends ConsumerWidget with BaseConsumerWidget {
  final String icon;
  final String text;
  final String subTitle;
  final GestureTapCallback onTap;
  final double? height;
  final double? width;

  const MainOperatorScreenTile({
    super.key,
    required this.icon,
    required this.text,
    required this.subTitle,
    required this.onTap,
    this.height,
    this.width,
  });

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final watchOperatorController = ref.watch(operatorController);
    return AbsorbPointer(
      absorbing: watchOperatorController.isTapOnScreen,
      child: InkWell(
        onTap: () {
          if (watchOperatorController.isTapOnScreen) return;

          watchOperatorController.updateTapEvent();
          onTap.call();
        },
        child: <PERSON>ack(
          children: [
            CommonSVG(strIcon: icon, height: height ?? context.height * 0.4, width: width ?? context.width * 0.4).alignAtBottomRight(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonText(
                  title: text.localized.toUpperCase(),
                  textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp, fontFamily: TextStyles.manropeFontFamily),
                ).paddingOnly(bottom: context.height * 0.005, left: context.width * 0.060, top: context.height * 0.010),
                CommonText(
                  title: subTitle.localized,
                  maxLines: 2,
                  textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 34.sp, fontFamily: TextStyles.manropeFontFamily),
                ).paddingOnly(left: context.width * 0.060)
              ],
            ),
          ],
        ),
      ),
    );
  }
}
