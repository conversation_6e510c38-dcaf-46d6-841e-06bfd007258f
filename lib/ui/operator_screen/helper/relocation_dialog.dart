import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/delivery/delivery_controller.dart';
import 'package:odigo_display/framework/controller/navigation/navigation_controller.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class RelocationDialog extends ConsumerWidget {
  const RelocationDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final newDashboardWatch = ref.watch(operatorController);
    return Dialog(
      backgroundColor: AppColors.white,
      child: Container(
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.04, vertical: context.height * 0.015),
          height: context.height * 0.4,
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: CommonText(
                      title: 'Select Point',
                      textStyle: TextStyles.semiBold.copyWith(fontSize: 40.sp, color: AppColors.black),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: CommonSVG(
                      strIcon: Assets.svgs.svgClose.path,
                      boxFit: BoxFit.fill,
                      height: context.height * 0.02,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 22.h,),
              newDashboardWatch.productionPointList.isNotEmpty || !ref.read(deliveryController).wayPointsState.isLoading ?
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      child: GridView.builder(
                        itemCount: newDashboardWatch.productionPointList.length,
                        shrinkWrap: true,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisExtent: context.width * 0.13
                        ),
                        itemBuilder: (BuildContext context, int index) {
                          return InkWell(
                            onTap: () {
                              newDashboardWatch.updateSelectedProductionPoint(newDashboardWatch.productionPointList[index]);
                            },
                            child: Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25.r),
                                  color: newDashboardWatch.selectedProductionPoint == newDashboardWatch.productionPointList[index].name ? AppColors.black : AppColors.white,
                                  border: Border.all(color: AppColors.black)),
                              margin: EdgeInsets.symmetric(horizontal: context.width * 0.005, vertical: context.height * 0.005),
                              padding: EdgeInsets.symmetric(horizontal: context.width * 0.02, vertical: context.height * 0.01),
                              child: CommonText(
                                title: newDashboardWatch.productionPointList[index].name ?? '',
                                textStyle: TextStyles.medium.copyWith(
                                  color: newDashboardWatch.selectedProductionPoint == newDashboardWatch.productionPointList[index].name ? AppColors.white : AppColors.black,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 22.h,),
                    CommonButton(
                      height: context.height * 0.05,
                      width: context.width * 0.3,
                      buttonText: LocaleKeys.keyRelocate.localized,
                      borderRadius: BorderRadius.circular(35.r),
                      onTap: () {
                        /// TODO Migration
                        // newDashboardWatch.socketManager.relocate(newDashboardWatch.selectedProductionPoint);
                        Navigator.pop(context);
                        ref.read(navigationController).relocateByPointName(newDashboardWatch.selectedProductionPoint ?? '');
                      },
                    ).alignAtBottomCenter(),
                  ],
                ),
              ) : const Center(
                child: CircularProgressIndicator(),
              )
            ],
          )
      ),
    );
  }
}
