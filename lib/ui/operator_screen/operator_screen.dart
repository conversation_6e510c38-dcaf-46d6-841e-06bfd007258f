import 'package:flutter/material.dart';
import 'package:odigo_display/ui/operator_screen/mobile/operator_screen_mobile.dart';
import 'package:odigo_display/ui/utils/helper/base_widget.dart';

class OperatorScreen extends StatelessWidget with BaseStatelessWidget {
  final bool isFromWaitingScreen;
  const OperatorScreen({Key? key,required this.isFromWaitingScreen}) : super(key: key);

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    return OperatorScreendMobile(isFromWaitingScreen: isFromWaitingScreen);
  }
}

