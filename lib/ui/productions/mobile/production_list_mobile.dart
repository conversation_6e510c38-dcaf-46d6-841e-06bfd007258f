import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/operator/manage_navigation_controller.dart';
import 'package:odigo_display/ui/utils/anim/fade_box_transition.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';

class ProductionListMobile extends ConsumerStatefulWidget {
  const ProductionListMobile({Key? key}) : super(key: key);

  @override
  ConsumerState<ProductionListMobile> createState() =>
      _ProductionListMobileState();
}

class _ProductionListMobileState extends ConsumerState<ProductionListMobile> {

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      hideBottomMenu();
      final manageNavigationWatch = ref.read(manageNavigationController);
      manageNavigationWatch.getProductionList();
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final manageNavigationWatch = ref.watch(manageNavigationController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// top close icon
            const Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CommonBackWidget(
                  color: AppColors.white,
                ),
              ],
            ),
            SizedBox(height: 15.h),

            /// stores
            CommonText(
              title: LocaleKeys.keyProductionList.localized,
              textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp),
            ),
            SizedBox(height: 17.h),

            /// explore nearby stores
            CommonText(
              title: LocaleKeys.keyViewAvailablePoints.localized,
              textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 30.sp),
            ),
            SizedBox(height: 38.h),

            /// route list
            Expanded(
              child: FadeBoxTransition(
                delay: 200,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: manageNavigationWatch.productionList.length,
                        itemBuilder: (context, index) {
                          var route = manageNavigationWatch.productionList[index];
                          return InkWell(
                            onTap: () {
                              final manageNavigationWatch = ref.watch(manageNavigationController);
                              manageNavigationWatch.updateProductionPoint(route.name??'');
                            },
                            child: Container(
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(50.r), border: Border.all(width: 1.w, color: AppColors.grey3A3A3A), color: AppColors.clr212121),
                              padding: EdgeInsets.all(40.h),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      CommonText(
                                        title: route.name ?? '',
                                        textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 46.sp),
                                      ),
                                      CommonSVG(
                                        strIcon: route.name?.toUpperCase() == (manageNavigationWatch.selectedProduction ?? '').toUpperCase() ? Assets.svgs.svgSelectedRoute.keyName : Assets.svgs.svgUnselectedRoute.keyName,
                                        height: 50.h,
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ).paddingOnly(bottom: 30.h),
                          );
                        },
                      ),
                      SizedBox(
                        height: 100.h,
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }




}
