// import 'package:flutter/scheduler.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:odigo_display/framework/controller/robot_list/robot_list_controller.dart';
// import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/server_robot_list_response_model.dart';
// import 'package:odigo_display/framework/utils/extension/context_extension.dart';
// import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
// import 'package:odigo_display/ui/routing/stack.dart';
// import 'package:odigo_display/ui/utils/const/static_id_constants.dart';
// import 'package:odigo_display/ui/utils/theme/theme.dart';
// import 'package:odigo_display/ui/utils/widgets/common_button.dart';
// import 'package:odigo_display/ui/utils/widgets/common_searchbar.dart';
// import 'package:odigo_display/ui/utils/widgets/empty_state_widget.dart';
//
// class RobotListMobile extends ConsumerStatefulWidget {
//   const RobotListMobile({super.key});
//
//   @override
//   ConsumerState createState() => _RobotListMobileState();
// }
//
// class _RobotListMobileState extends ConsumerState<RobotListMobile> {
//   @override
//   void initState() {
//     SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
//       final robotListRead = ref.read(robotListController);
//       await robotListRead.getRobotListApi();
//     });
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final robotListWatch = ref.watch(robotListController);
//     return Scaffold(
//       backgroundColor: AppColors.black,
//       body: Padding(
//         padding: EdgeInsets.symmetric(vertical: context.height * 0.02, horizontal: context.width * 0.05),
//         child: robotListWatch.robotListState.isLoading
//             ? const Center(
//                 child: CircularProgressIndicator(color: AppColors.white),
//               )
//             : Stack(
//                 children: [
//                   Column(
//                     children: [
//                       SizedBox(height: context.height * 0.02),
//                       CommonSearchBar(
//                         controller: robotListWatch.searchRobotController,
//                         height: context.height * 0.05,
//                         onChanged: (text) {
//                           robotListWatch.searchRobot();
//                         },
//                       ),
//                       SizedBox(height: context.height * 0.02),
//                       if (robotListWatch.robotList.isEmpty)
//                         Expanded(
//                           child: Column(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               const EmptyStateWidget(),
//                               CommonButton(
//                                 onTap: () async {
//                                   await robotListWatch.getRobotListApi();
//                                 },
//                                 buttonText: LocaleKeys.keyRefresh.localized,
//                                 buttonTextColor: AppColors.black,
//                                 backgroundColor: AppColors.white,
//                                 width: context.width * 0.4,
//                               ),
//                             ],
//                           ),
//                         )
//                       else
//                         Expanded(
//                           child: ListView.separated(
//                             padding: EdgeInsets.zero,
//                             itemCount: robotListWatch.robotList.length,
//                             itemBuilder: (context, index) {
//                               ServerRobotData? robotData = robotListWatch.robotList[index];
//                               return InkWell(
//                                 onTap: () {
//                                   robotListWatch.updateSelectRobotUuid(
//                                     robotData.uuid!,
//                                     robotData.deviceDetails!.where((element) => element.robotDeviceType == RobotDeviceType.ADS_PANEL).firstOrNull!.applicationId!,
//                                     robotData.deviceDetails!.where((element) => element.robotDeviceType == RobotDeviceType.ADS_PANEL).firstOrNull!.packageId!,
//                                   );
//                                 },
//                                 child: Container(
//                                   decoration: BoxDecoration(
//                                     color: AppColors.white,
//                                     borderRadius: BorderRadius.circular(20.r),
//                                     border: robotData.uuid == robotListWatch.selectedRobotUuid ? Border.all(color: Colors.blue, width: 10.w) : null,
//                                   ),
//                                   padding: EdgeInsets.symmetric(
//                                     vertical: context.height * 0.02,
//                                     horizontal: context.width * 0.05,
//                                   ),
//                                   child: Column(
//                                     crossAxisAlignment: CrossAxisAlignment.start,
//                                     children: [
//                                       CommonText(
//                                         title: 'UUID: ${robotData.uuid}',
//                                         clrfont: AppColors.black,
//                                       ),
//                                       SizedBox(height: context.height * 0.001),
//                                       CommonText(
//                                         title: 'Serial Number: ${robotData.serialNumber}',
//                                         clrfont: AppColors.black,
//                                       ),
//                                       SizedBox(height: context.height * 0.001),
//                                       CommonText(
//                                         title: 'HostName: ${robotData.hostName}',
//                                         clrfont: AppColors.black,
//                                       ),
//                                       SizedBox(height: context.height * 0.001),
//                                       CommonText(
//                                         title: 'Navigation Version: ${robotData.navigationVersion}',
//                                         clrfont: AppColors.black,
//                                       ),
//                                       SizedBox(height: context.height * 0.02),
//                                       SizedBox(
//                                         height: context.height * 0.3,
//                                         child: ListView.separated(
//                                           padding: EdgeInsets.zero,
//                                           itemCount: robotData.deviceDetails?.length ?? 0,
//                                           physics: const NeverScrollableScrollPhysics(),
//                                           itemBuilder: (context, index) {
//                                             DeviceDetail? deviceDetail = robotData.deviceDetails?[index];
//                                             if (deviceDetail == null) {
//                                               return const Offstage();
//                                             } else {
//                                               return Container(
//                                                 width: context.width * 0.25,
//                                                 decoration: BoxDecoration(color: AppColors.black, borderRadius: BorderRadius.circular(20.r)),
//                                                 padding: EdgeInsets.symmetric(vertical: context.height * 0.02, horizontal: context.width * 0.05),
//                                                 child: Column(
//                                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                                   children: [
//                                                     CommonText(
//                                                       title: deviceDetail.robotDeviceType!.name,
//                                                       clrfont: AppColors.white,
//                                                     ),
//                                                     CommonText(
//                                                       title: 'Android ID:${deviceDetail.applicationId}',
//                                                       clrfont: AppColors.white,
//                                                     ),
//                                                   ],
//                                                 ),
//                                               );
//                                             }
//                                           },
//                                           separatorBuilder: (BuildContext context, int index) {
//                                             return SizedBox(height: context.height * 0.01);
//                                           },
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               );
//                             },
//                             separatorBuilder: (BuildContext context, int index) {
//                               return SizedBox(height: context.height * 0.02);
//                             },
//                           ),
//                         ),
//                     ],
//                   ),
//                   Positioned(
//                     bottom: context.height * 0.02,
//                     left: context.width * 0.25,
//                     right: context.width * 0.25,
//                     child: CommonButton(
//                       height: context.height * 0.07,
//                       backgroundColor: Colors.blue,
//                       buttonText: robotListWatch.selectedAndroidId != null ? LocaleKeys.keyNext.localized : LocaleKeys.keySkip.localized,
//                       onTap: () {
//                         if (robotListWatch.selectedAndroidId != null) {
//                           staticAndroidId = robotListWatch.selectedAndroidId!;
//                           staticPackageName = robotListWatch.selectedPackageName!;
//                           ref.read(navigationStackController).push(const NavigationStackItem.signInScreen());
//                         } else {
//                           staticAndroidId = null;
//                           staticPackageName = null;
//                           ref.read(navigationStackController).push(const NavigationStackItem.signInScreen());
//                         }
//                       },
//                     ),
//                   )
//                 ],
//               ),
//       ),
//     );
//   }
// }
