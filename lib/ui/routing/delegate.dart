import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/ui/about_device/about_device.dart';
import 'package:odigo_display/ui/advertisement_history/advertisement_history_details.dart';
import 'package:odigo_display/ui/advertisement_history/advertisment_history.dart';
import 'package:odigo_display/ui/canvas_map/manage_map.dart';
import 'package:odigo_display/ui/change_color/change_color.dart';
import 'package:odigo_display/ui/destination_robot/destination_robot_list.dart';
import 'package:odigo_display/ui/display_ads/display_ads_screen.dart';
import 'package:odigo_display/ui/display_ads/mobile/helper/guide_screen.dart';
import 'package:odigo_display/ui/download_screen/download_screen.dart';
import 'package:odigo_display/ui/export/export_screen.dart';
import 'package:odigo_display/ui/export_details/export_details_screen.dart';
import 'package:odigo_display/ui/how_to_use/how_to_use.dart';
import 'package:odigo_display/ui/manage_navigation/manage_navigation.dart';
import 'package:odigo_display/ui/map/map.dart';
import 'package:odigo_display/ui/map/map_selection.dart';
import 'package:odigo_display/ui/operator_screen/mobile/operator_screen_mobile.dart';
import 'package:odigo_display/ui/productions/production_list.dart';
import 'package:odigo_display/ui/routing/navigation_stack_keys.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/settings/settings_screen.dart';
import 'package:odigo_display/ui/sign_in/initialize_enc_screen.dart';
import 'package:odigo_display/ui/sign_in/sign_in_screen.dart';
import 'package:odigo_display/ui/splash/splash.dart';
import 'package:odigo_display/ui/storage/storage.dart';
import 'package:odigo_display/ui/store/category_details.dart';
import 'package:odigo_display/ui/store/media_preview.dart';
import 'package:odigo_display/ui/store/store_details.dart';
import 'package:odigo_display/ui/store/store_info.dart';
import 'package:odigo_display/ui/store/store_list.dart';
import 'package:odigo_display/ui/store/stores.dart';
import 'package:odigo_display/ui/waiting_screen/waiting_screen.dart';
import 'package:odigo_display/ui/wifi/wifi_management.dart';

final globalNavigatorKey = GlobalKey<NavigatorState>();

@injectable
class MainRouterDelegate extends RouterDelegate<NavigationStack> with ChangeNotifier, PopNavigatorRouterDelegateMixin {
  final NavigationStack stack;

  @override
  void dispose() {
    stack.removeListener(notifyListeners);
    super.dispose();
  }

  MainRouterDelegate(@factoryParam this.stack) : super() {
    stack.addListener(notifyListeners);
  }

  @override
  final navigatorKey = globalNavigatorKey;

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return Navigator(
        key: navigatorKey,
        pages: _pages(ref),

        /// callback when a page is popped.
        onPopPage: (route, result) {
          /// let the OS handle the back press if there was nothing to pop
          if (!route.didPop(result)) {
            return false;
          }

          /// if we are on root, let OS close app
          if (stack.items.length == 1) return false;

          /// if we are on root, let OS close app
          if (stack.items.isEmpty) return false;

          /// otherwise, pop the stack and consume the event
          stack.pop();
          return true;
        },
      );
    });
  }

  List<Page> _pages(WidgetRef ref) => stack.items
      .mapIndexed((e, i) => e.when(
            splash: () => const MaterialPage(child: Splash(), key: ValueKey(Keys.splash)),
            // dashboard: () => const MaterialPage(child: Offstage(), key: ValueKey(Keys.dashboard)),
            newDashboard: (isFromWaitingScreen) => MaterialPage(child: OperatorScreendMobile(isFromWaitingScreen: isFromWaitingScreen), key: const ValueKey(Keys.newDashboard)),
            storage: () => const MaterialPage(child: Storage(), key: ValueKey(Keys.storage)),
            storeDetails: (storeData, isAgency) => MaterialPage(child: StoreDetails(storeData: storeData, isAgency: isAgency), key: const ValueKey(Keys.storeDetails)),
            storeInfo: (storeData) => MaterialPage(child: StoreInfo(storeData: storeData), key: const ValueKey(Keys.storeInfo)),
            storeList: (isEntity) => MaterialPage(child: StoreList(isEntity: isEntity), key: const ValueKey(Keys.storeList)),
            manageNavigation: (isFromWaitingScreen) => MaterialPage(child: ManageNavigation(isFromWaitingScreen: isFromWaitingScreen), key: const ValueKey(Keys.manageNavigation)),
            productionList: () => const MaterialPage(child: ProductionList(), key: ValueKey(Keys.productionList)),
            downloadScreen: (forcefullyCallSyncApi) => MaterialPage(child: DownloadScreen(forcefullyCallSyncApi: forcefullyCallSyncApi), key: const ValueKey(Keys.downloadScreen)),
            displayAds: (startRouteNavigation, isFromWaitingScreen) =>
                MaterialPage(child: DisplayAdsScreen(startRouteNavigation: startRouteNavigation, isFromWaitingScreen: isFromWaitingScreen), key: const ValueKey(Keys.displayAds)),
            mediaPreview: (isImage, mediaList) => MaterialPage(child: MediaPreview(isImage: isImage, mediaList: mediaList), key: const ValueKey(Keys.mediaPreview)),
            waitingScreen: (setAlarm) => MaterialPage(child: WaitingScreen(setAlarm: setAlarm), key: const ValueKey(Keys.waitingScreen)),
            initializeEncScreen: () => const MaterialPage(child: InitializeEncScreen(), key: ValueKey(Keys.initializeEncScreen)),
            // initializeEncScreen: () => const MaterialPage(child: ManageMap(), key: ValueKey(Keys.initializeEncScreen)),
            signInScreen: () => const MaterialPage(child: SignInScreen(), /*key: ValueKey(Keys.signInScreen)*/),
            availableWiFi: (isPop) => MaterialPage(child: WifiManagement(isPop: isPop), key: const ValueKey(Keys.availableWiFi)),
            stores: () => const MaterialPage(child: Categories(categoriesList: []), key: ValueKey(Keys.stores)),
            storeGuide: () => const MaterialPage(child: GuideScreenMobile(), key: ValueKey(Keys.storeGuide), fullscreenDialog: true),
            aboutDevice: () => const MaterialPage(child: AboutDevice(), key: ValueKey(Keys.aboutDevice)),
            advertisementHistory: () => const MaterialPage(child: AdvertisementHistory(), key: ValueKey(Keys.advertisementHistory)),
            advertisementHistoryDetails: (sequence) => MaterialPage(child: AdvertisementHistoryDetails(sequence: sequence), key: const ValueKey(Keys.advertisementHistoryDetails)),
            changeColor: () => const MaterialPage(child: ChangeColor(), key: ValueKey(Keys.changeColor)),
            // robotList: () => const MaterialPage(child: , key: ValueKey(Keys.robotList)),
            destinationRobotList: () => const MaterialPage(child: DestinationRobotList(), key: ValueKey(Keys.destinationRobotList)),
            mapSelection: () => const MaterialPage(child: MapSelection(), key: ValueKey(Keys.mapSelection)),
            manageMap: () => const MaterialPage(child: ManageMap(), key: ValueKey(Keys.mapSelection)),
            howToUse: () => const MaterialPage(child: HowToUse(), key: ValueKey(Keys.howToUse)),
            categoryDetails: (categoryName, storeList) =>  MaterialPage(child: CategoryDetails(categoryName: categoryName ?? '', storeList:  storeList ?? []), key: ValueKey(Keys.categoryDetails)),
            robotMap: (hashError, isBuildingNew, onPopCallBack) => MaterialPage(child: RobotMap(isBuildingNew: isBuildingNew ?? false, onPopCallBack: onPopCallBack), key: const ValueKey(Keys.robotMap)),
            settings: () => const MaterialPage(child: SettingsScreen(), key: ValueKey(Keys.settings)),
            exportScreen: () => const MaterialPage(child: ExportScreen(), key: ValueKey(Keys.exportScreen)),
            exportDetailsScreen: (storeList) => MaterialPage(child: ExportDetailsScreen(storeList: storeList), key: const ValueKey(Keys.exportDetailsScreen)),
          ))
      .toList();

  @override
  NavigationStack get currentConfiguration => stack;

  @override
  Future<bool> popRoute() async {
    return true;
  }

  @override
  Future<void> setNewRoutePath(NavigationStack configuration) async {
    stack.items = configuration.items;
  }
}

extension _IndexedIterable<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }
}
