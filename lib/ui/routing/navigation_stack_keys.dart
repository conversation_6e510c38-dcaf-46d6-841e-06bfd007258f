abstract class Keys {

  static const splash = 'splash';
  static const login = 'login';
  static const dashboard = 'dashboard';
  static const newDashboard = 'newDashboard';
  static const storage = 'storage';
  static const storeList = 'storeList';
  static const storeDetails = 'storeDetails';
  static const storeInfo = 'storeInfo';
  static const manageNavigation = 'manageNavigation';
  static const productionList = 'productionList';
  static const downloadScreen = 'downloadScreen';
  static const mediaPreview = 'mediaPreview';
  static const displayAds = 'displayAds';


  static const waitingScreen = 'waitingScreen';
  static const initializeEncScreen = 'initializeEncScreen';
  static const signInScreen = 'signInScreen';
  static const availableWiFi = 'availableWiFi';
  static const stores = 'stores';
  static const storeGuide = 'storeGuide';
  static const aboutDevice = 'aboutDevice';
  static const advertisementHistory = 'advertisementHistory';
  static const advertisementHistoryDetails = 'advertisementHistoryDetails';
  static const changeColor = 'changeColor';
  static const robotList = 'robotList';
  static const destinationRobotList = 'destinationRobotList';
  static const mapSelection = 'mapSelection';
  static const robotMap = 'robotMap';
  static const manageMap = 'manageMap';
  static const howToUse = 'howToUse';
  static const categoryDetails = 'categoryDetails';

  static const settings = 'settings';

  static const exportScreen = 'exportScreen';
  static const exportDetailsScreen = 'exportDetailsScreen';


}