import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/framework/controller/settings/settings_screen_controller.dart';
import 'package:odigo_display/framework/repository/robot_meta_data_repository/model/get_language_list_response_model.dart';
import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/framework/utils/extension/context_extension.dart';
import 'package:odigo_display/framework/utils/extension/extension.dart';
import 'package:odigo_display/framework/utils/extension/string_extension.dart';
import 'package:odigo_display/framework/utils/helper/voice_helper.dart';
import 'package:odigo_display/ui/operator_screen/helper/controlbar_slider.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/app_assets.dart';
import 'package:odigo_display/ui/utils/theme/app_colors.dart';
import 'package:odigo_display/ui/utils/theme/app_strings.g.dart';
import 'package:odigo_display/ui/utils/theme/text_style.dart';
import 'package:odigo_display/ui/utils/widgets/common_form_field_dropdown.dart';
import 'package:odigo_display/ui/utils/widgets/common_text.dart';
import 'package:odigo_display/ui/wifi/web/helper/robot_session.dart';

class SettingsScreenWeb extends ConsumerStatefulWidget {
  const SettingsScreenWeb({Key? key}) : super(key: key);

  @override
  ConsumerState<SettingsScreenWeb> createState() => _SettingsScreenWebState();
}

class _SettingsScreenWebState extends ConsumerState<SettingsScreenWeb> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final settingsScreenWatch = ref.read(settingsScreenController);
      final watchOperatorController = ref.read(operatorController);
      showLog('SessionRepository.session.cruiseModeSpeed ${SessionRepository.session.cruiseModeSpeed}');
      watchOperatorController.updateCruiseModeSpeed(speed: RobotSession.cruiseModeRobotSpeed);
      watchOperatorController.updateNavigationModeSpeed(speed: RobotSession.navigationModeRobotSpeed);
      watchOperatorController.updateVideoSoundVolume(volume: RobotSession.videoSoundRobotVolume);
      watchOperatorController.updatePromptSoundVolume(volume: RobotSession.promptVolumeRobotVolume);
      settingsScreenWatch.disposeController(isNotify: true);
      watchOperatorController.getLanguageLocally();
      showLog('RobotSession.selectedLanguage RobotSession.selectedLanguage${RobotSession.selectedLanguage}');

      if (RobotSession.selectedLanguage != '') {
        final languageJson = jsonDecode(RobotSession.selectedLanguage);
        final languageModel = LanguageModel.fromJson(languageJson);
        watchOperatorController.updateLanguage(languageModel);
        // if (RobotSession.selectedLanguageCode == 'en') {
        //   RobotSession.selectedLanguageCode = 'en';
        // } else {
        //   RobotSession.selectedLanguageCode = 'ar';
        // }
      } else {
        if (watchOperatorController.languageListState.success?.data?.isNotEmpty ?? false) {
          int languageIndex = watchOperatorController.languageListState.success!.data!.indexWhere((element) => element.code == 'en');
          // if (languageIndex != -1) {
          //   RobotSession.selectedLanguageCode = 'en';
          // } else {
          //   RobotSession.selectedLanguageCode = 'ar';
          // }
          watchOperatorController.updateLanguage(watchOperatorController.languageListState.success!.data![languageIndex]);
        }
      }
      showLog('testing check ${watchOperatorController.languageModel}');
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final watchOperatorController = ref.watch(operatorController);
    final settingsScreenWatch = ref.watch(settingsScreenController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final settingsScreenWatch = ref.read(settingsScreenController);
    final watchOperatorController = ref.read(operatorController);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            IconButton(
              onPressed: () {
                ref.read(navigationStackController).pop();
              },
              icon: Icon(
                Icons.arrow_back,
                size: 40.h,
                color: AppColors.white,
              ),
            ),
            SizedBox(
              width: 20.w,
            ),
            CommonText(
              title: LocaleKeys.keySettings.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 40.sp,
                color: AppColors.white,
              ),
            ),
          ],
        ).paddingOnly(top: 20.h),

        Row(
          children: [
            CommonText(
              title: LocaleKeys.keyLanguage.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 35.sp,
              ),
            ),
            const Spacer(),
            // CommonDropdownInputFormField(
            //   items: watchOperatorController.languageListState.success?.data?.map<DropdownMenuItem<LanguageModel>>((LanguageModel value) {
            //     return DropdownMenuItem<LanguageModel>(
            //       value: value,
            //       child: Padding(
            //         padding: EdgeInsets.symmetric(horizontal: 4.w),
            //         child: Text(value.name.toString() ?? '', style: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 18.sp)),
            //       ),
            //     );
            //   }).toList(),
            //   borderColor: AppColors.clr616161,
            //   defaultValue: watchOperatorController.languageModel,
            //   backgroundColor: AppColors.black,
            //   borderRadius: BorderRadius.circular(20.r),
            //   borderWidth: 2.w,
            //   fieldWidth: 200.w,
            //   onChanged: (LanguageModel? language) {
            //     if (language != null) {
            //       watchOperatorController.updateLanguage(language);
            //       showLog('watchOperatorController.languageModel?.code ${watchOperatorController.languageModel?.code}');
            //       context.setLocale(Locale(watchOperatorController.languageModel?.code ?? ''));
            //     }
            //   },
            // ),

            SizedBox(
              width: 200.w,
              child: DropdownButtonFormField<LanguageModel>(
                isExpanded: true,
                isDense: true,
                value: watchOperatorController.languageModel,
                items: watchOperatorController.languageListState.success?.data?.map<DropdownMenuItem<LanguageModel>>((LanguageModel value) {
                  return DropdownMenuItem<LanguageModel>(
                    value: value,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      child: Text(
                        value.name ?? '',
                        style: TextStyles.regular.copyWith(
                          color: AppColors.white,
                          fontSize: 18.sp,
                        ),
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (LanguageModel? language) {
                  if (language != null) {
                    watchOperatorController.updateLanguage(language);
                    context.setLocale(Locale(language.code ?? ''));
                  }
                },
                decoration: InputDecoration(
                  filled: true,
                  fillColor: AppColors.black,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.r),
                    borderSide: BorderSide(
                      color: AppColors.clr616161,
                      width: 2.w,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.r),
                    borderSide: BorderSide(
                      color: AppColors.clr616161,
                      width: 2.w,
                    ),
                  ),
                ),
                dropdownColor: AppColors.black,
                iconEnabledColor: AppColors.white,
              ),
            )
          ],
        ).paddingOnly(top: 50.h),

        CommonText(
          title: LocaleKeys.keyMediaVolume.localized,
          textStyle: TextStyles.bold.copyWith(
            fontSize: 35.sp,
          ),
        ).paddingOnly(top: 50.h),

        /// Volume
        Row(
          children: [
            CommonText(
              title: LocaleKeys.keyVideoSound.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
            const Spacer(),
            CommonText(
              title: '${(watchOperatorController.videoSoundVolume.toDouble() * 100).toInt()}',
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
          ],
        ).paddingOnly(top: 20.h, right: 35.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 45.w, vertical: 20.h),
          child: RotatedBox(
            quarterTurns: 1,
            child: ControlBarSlider(
              value: watchOperatorController.videoSoundVolume.toDouble(),
              min: 0,
              max: 1,
              height: context.height * 0.04,
              onChanged: (value) {
                if (watchOperatorController.videoSoundVolume > value) {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.volumeDecreased);
                } else {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.volumeIncreased);
                }
                if (value < 0.1) {
                  watchOperatorController.updateVideoSoundVolume(volume: 0.1);
                } else {
                  watchOperatorController.updateVideoSoundVolume(volume: value);
                }
                settingsScreenWatch.notifyListeners();
              },
              onChangeEnd: (value) {
                if (value < 0.1) {
                  watchOperatorController.updateVideoSoundVolume(volume: 0.1);
                } else {
                  watchOperatorController.updateVideoSoundVolume(volume: value);
                }
                settingsScreenWatch.notifyListeners();
              },
            ),
          ),
        ),
        Row(
          children: [
            CommonText(
              title: LocaleKeys.keyPromptVolume.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
            const Spacer(),
            CommonText(
              title: '${(watchOperatorController.promptVolume.toDouble() * 100).toInt()}',
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
          ],
        ).paddingOnly(top: 20.h, right: 35.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 45.w, vertical: 20.h),
          child: RotatedBox(
            quarterTurns: 1,
            child: ControlBarSlider(
              value: watchOperatorController.promptVolume.toDouble(),
              min: 0,
              max: 1,
              height: context.height * 0.04,
              // percentageValue: (watchOperatorController.volume * 100).round(),
              onChanged: (value) {
                if (watchOperatorController.promptVolume > value) {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.volumeDecreased);
                } else {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.volumeIncreased);
                }
                if (value < 0.1) {
                  watchOperatorController.updatePromptSoundVolume(volume: 0.1);
                } else {
                  watchOperatorController.updatePromptSoundVolume(volume: value);
                }
                settingsScreenWatch.notifyListeners();
              },
              onChangeEnd: (value) {
                if (value < 0.1) {
                  watchOperatorController.updatePromptSoundVolume(volume: 0.1);
                } else {
                  watchOperatorController.updatePromptSoundVolume(volume: value);
                }
                settingsScreenWatch.notifyListeners();
              },
            ),
          ),
        ),

        CommonText(
          title: LocaleKeys.keySpeed.localized,
          textStyle: TextStyles.bold.copyWith(
            fontSize: 35.sp,
          ),
        ).paddingOnly(top: 50.h),

        /// Speed
        Row(
          children: [
            CommonText(
              title: LocaleKeys.keyCruiseMode.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
            const Spacer(),
            CommonText(
              title: '${(watchOperatorController.cruiseModeSpeed.toDouble() * 100).toInt()}',
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
          ],
        ).paddingOnly(top: 20.h, right: 35.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 45.w, vertical: 20.h),
          child: RotatedBox(
            quarterTurns: 1,
            child: ControlBarSlider(
              value: watchOperatorController.cruiseModeSpeed.toDouble(),
              min: 0,
              max: 1,
              height: context.height * 0.04,
              // percentageValue: (watchOperatorController.volume * 100).round(),
              onChanged: (value) {
                if (watchOperatorController.cruiseModeSpeed > value) {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.speedDecreased);
                } else {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.speedIncreased);
                }
                if (value < 0.1) {
                  watchOperatorController.updateCruiseModeSpeed(speed: 0.1);
                } else {
                  watchOperatorController.updateCruiseModeSpeed(speed: value);
                }
              },
              onChangeEnd: (value) {
                if (value < 0.1) {
                  watchOperatorController.updateCruiseModeSpeed(speed: 0.1);
                } else {
                  watchOperatorController.updateCruiseModeSpeed(speed: value);
                }
              },
            ),
          ),
        ),
        Row(
          children: [
            CommonText(
              title: LocaleKeys.keyNavigation.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
            const Spacer(),
            CommonText(
              title: '${(watchOperatorController.navigationModeSpeed.toDouble() * 100).toInt()}',
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
          ],
        ).paddingOnly(top: 20.h, right: 35.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 45.w, vertical: 20.h),
          child: RotatedBox(
            quarterTurns: 1,
            child: ControlBarSlider(
              value: watchOperatorController.navigationModeSpeed.toDouble(),
              min: 0,
              max: 1,
              height: context.height * 0.04,
              // percentageValue: (watchOperatorController.volume * 100).round(),
              onChanged: (value) {
                if (watchOperatorController.navigationModeSpeed > value) {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.speedDecreased);
                } else {
                  VoiceHelper.instance.playAudioFromAsset(AppAssets.speedIncreased);
                }
                if (value < 0.1) {
                  watchOperatorController.updateNavigationModeSpeed(speed: 0.1);
                } else {
                  watchOperatorController.updateNavigationModeSpeed(speed: value);
                }
              },
              onChangeEnd: (value) {
                if (value < 0.1) {
                  watchOperatorController.updateNavigationModeSpeed(speed: 0.1);
                } else {
                  watchOperatorController.updateNavigationModeSpeed(speed: value);
                }
              },
            ),
          ),
        ),

        CommonText(
          title: LocaleKeys.keyBattery.localized,
          textStyle: TextStyles.bold.copyWith(
            fontSize: 35.sp,
          ),
        ).paddingOnly(top: 50.h),
        Row(
          children: [
            CommonText(
              title: LocaleKeys.keyMinimumOperatingBatteryPercentage.localized,
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
            const Spacer(),
            CommonText(
              title: '${(RobotSession.batteryPercentage).toInt()}',
              textStyle: TextStyles.bold.copyWith(
                fontSize: 25.sp,
              ),
            ),
          ],
        ).paddingOnly(top: 20.h, right: 35.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 45.w, vertical: 20.h),
          child: RotatedBox(
            quarterTurns: 1,
            child: ControlBarSlider(
              value: RobotSession.batteryPercentage / 100,
              min: 0,
              max: 1,
              height: context.height * 0.04,
              // bottomIcon: watchOperatorController.volumeImage,
              onChanged: (value) {
                if (value < 0.1) {
                  RobotSession.batteryPercentage = (0.1 * 100).toInt();
                } else {
                  RobotSession.batteryPercentage = (value * 100).toInt();
                }
                settingsScreenWatch.notifyListeners();
              },
            ),
          ),
        ),
      ],
    ).paddingAll(40.h);
  }
}
