import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:odigo_display/framework/controller/sign_in/sign_in_screen_controller.dart';
import 'package:odigo_display/framework/controller/splash/splash_controller.dart';
import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_form_field.dart';

class InitializeEncScreenMobile extends ConsumerStatefulWidget {
  const InitializeEncScreenMobile({Key? key}) : super(key: key);

  @override
  ConsumerState<InitializeEncScreenMobile> createState() => _InitializeEncScreenMobileState();
}

class _InitializeEncScreenMobileState extends ConsumerState<InitializeEncScreenMobile> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final signInScreenWatch = ref.watch(signInScreenController);
      if (SessionRepository.session.baseUrlGet != '') {
        signInScreenWatch.baseUrlController.text = SessionRepository.session.baseUrlGet;
      }

      signInScreenWatch.baseUrlController.text = "https://service.odigo.kodyinfotech.com/odigo";
      signInScreenWatch.androidIdController.text = '9408811661';
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.clr212121,
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final signInScreenWatch = ref.watch(signInScreenController);
    return Column(
      children: [
        Expanded(
          flex: 2,
          child: Stack(
            children: [
              ListView(
                children: [
                  Center(child: Image.asset(Assets.images.icLoginLogo.keyName, height: context.height * 0.35, width: context.width * 0.35)),
                  CommonText(
                    title: LocaleKeys.keyEnterServerDetails.localized,
                    textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 43.sp),
                  ).paddingOnly(bottom: context.height * 0.01),
                  Column(
                    children: [
                      SizedBox(height: context.height * 0.010),
                      SizedBox(height: context.height * 0.030),
                      CommonInputFormField(
                        textEditingController: signInScreenWatch.baseUrlController,
                        textInputType: TextInputType.text,
                        cursorColor: AppColors.white,
                        labelText: 'Base Url Ip (With Port Number)',
                        validator: (value) {
                          if (value == null) {
                            return 'Base Url Ip is required';
                          } else {
                            return null;
                          }
                        },
                      ),
                      CommonInputFormField(
                        textEditingController: signInScreenWatch.androidIdController,
                        textInputType: TextInputType.text,
                        cursorColor: AppColors.white,
                        labelText: 'Android id',
                        validator: (value) {
                          if (value == null) {
                            return 'Android id is required';
                          } else {
                            return null;
                          }
                        },
                      ),
                      SizedBox(height: context.height * 0.050),
                    ],
                  ),
                  InkWell(
                    onTap: () async {
                      if (signInScreenWatch.baseUrlController.text.isNotEmpty /*&& signInScreenWatch.socketIpAddressController.text.isNotEmpty*/) {
                        await signInScreenWatch.updateIpMethod();

                        if (mounted) {
                          /// Getting Encryption Started
                          await ref.watch(splashController).getBackendEncryptionKey(context).then(
                            (value) async {
                              Future.delayed(
                                const Duration(seconds: 3),
                                () {
                                  showLog("SessionRepository.session.frontEndPublicKey ${SessionRepository.session.frontEndPublicKey}");
                                  showLog("SessionRepository.session.frontEndPublicKey ${SessionRepository.session.frontEndPrivateKey}");
                                  showLog("ref.watch(splashController).isSecretApiLoading ${ref.watch(splashController).isSecretApiLoading}");
                                  if (SessionRepository.session.frontEndPublicKey != null && SessionRepository.session.frontEndPrivateKey != null) {
                                    if (signInScreenWatch.androidIdController.text.isNotEmpty) {
                                      RobotSession.androidId = signInScreenWatch.androidIdController.text;
                                    }
                                    ref.read(navigationStackController).push(const NavigationStackItem.signInScreen());
                                  }
                                },
                              );
                            },
                          );
                        }
                      }
                    },
                    child: Container(
                      height: 151.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(18.r),
                        border: Border.all(color: AppColors.transparent, width: 0),
                      ),
                      child: ref.watch(splashController).isSecretApiLoading == true
                          ? Center(child: LoadingAnimationWidget.waveDots(color: AppColors.black, size: 70.h))
                          : Row(
                              //mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 12),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          LocaleKeys.keyNext.localized,
                                          textAlign: TextAlign.center,
                                          maxLines: 1,
                                          style: TextStyles.medium.copyWith(fontSize: 48.sp, color: AppColors.black),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                  SizedBox(height: context.height * 0.050),
                  CommonText(
                    title: "${LocaleKeys.keyROSIP.localized} : ${RobotSession.hostIpAddress.toString()}",
                    textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 43.sp),
                  ).alignAtCenter(),
                ],
              ).paddingSymmetric(horizontal: context.width * 0.06),
            ],
          ),
        ),
      ],
    );
  }
}
