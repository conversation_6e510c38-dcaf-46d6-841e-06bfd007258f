import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/sign_in/sign_in_screen_controller.dart';

class SignInScreenWeb extends ConsumerStatefulWidget {
  const SignInScreenWeb({Key? key}) : super(key: key);

  @override
  ConsumerState<SignInScreenWeb> createState() => _SignInScreenWebState();
}

class _SignInScreenWebState extends ConsumerState<SignInScreenWeb> {

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final signInScreenWatch = ref.watch(signInScreenController);
      //signInScreenWatch.disposeController(isNotify : true);
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    return Container();
  }


}
