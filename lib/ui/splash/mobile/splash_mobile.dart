import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kiosk_mode/kiosk_mode.dart';
import 'package:odigo_display/framework/controller/canvas_map/canvas_map_api_class.dart';
import 'package:odigo_display/framework/controller/canvas_map/canvas_map_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_display/framework/controller/download/download_controller.dart';
import 'package:odigo_display/framework/controller/hardware_controller/hardware_controller.dart';
import 'package:odigo_display/framework/controller/navigation/navigation_controller.dart';
import 'package:odigo_display/framework/controller/operator/operator_controller.dart';
import 'package:odigo_display/framework/controller/robot_action/robot_action_event.dart';
import 'package:odigo_display/framework/controller/robot_action/robot_event_manager.dart';
import 'package:odigo_display/framework/controller/sign_in/sign_in_screen_controller.dart';
import 'package:odigo_display/framework/controller/splash/splash_controller.dart';
import 'package:odigo_display/framework/controller/wifi/wifi_controller.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';
import 'package:odigo_display/framework/provider/network/network.dart';
import 'package:odigo_display/framework/repository/map/model/map_variables.dart';
import 'package:odigo_display/framework/repository/session/repository/session_api_repository.dart';
import 'package:odigo_display/framework/utils/extension/datetime_extension.dart';
import 'package:odigo_display/framework/utils/helper/voice_helper.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class SplashMobile extends ConsumerStatefulWidget {
  const SplashMobile({Key? key}) : super(key: key);

  @override
  ConsumerState<SplashMobile> createState() => _SplashMobileState();
}

class _SplashMobileState extends ConsumerState<SplashMobile> with BaseConsumerStatefulWidget {
  int count = 0;

  StreamSubscription<CpuTemperature>? cpuTemperatureStream;

  ///Init Override
  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        hideBottomMenu();
        final splashWatch = ref.watch(splashController);
        final signInScreenWatch = ref.watch(signInScreenController);
        final hardwareRead = ref.read(hardwareController);
        final navigationRead = ref.read(navigationController);
        final wifiRead = ref.read(wifiController);
// final mapRead = ref.read(mapController);
        final dashboardControllerRead = ref.read(operatorController);

        /// Dispose
        splashWatch.disposeController(isNotify: true);
        splashWatch.fillListOfDateTimeDataFromHive();
        splashWatch.addDateTimeData(DateTime.now().toStringWithCustomDate('dd/mm/yyyy hh:mm:ss a'));

        showLog('initializeBatteryChannel started');

        /// Initialize All Channels
        await hardwareRead.initializeBatteryChannel();
        showLog('initializeBatteryChannel completed');

        showLog('initializeNavigationChannel started');
        await navigationRead.initializeNavigationChannel();
        showLog('initializeNavigationChannel completed');

        showLog('initializeWifiChannel started');
        await wifiRead.initializeWifiChannel();
        showLog('initializeWifiChannel completed');

// await mapRead.initializeMapChannel();

        if (globalRef != null) {
          showLog('getBatteryPercentage started');

          /// Get Battery Percentage
          await hardwareRead.getBatteryPercentage(globalRef!);
          showLog('getBatteryPercentage completed');

          showLog('getAndroidId started');

          /// Get Android ID
          await hardwareRead.getAndroidId();
          showLog('getAndroidId completed');
        }

        showLog('initializeAudioPLayer started');
        await VoiceHelper.instance.initializeAudioPLayer();
        showLog('initializeAudioPLayer completed');

        await stopKioskMode();
        dashboardControllerRead.speedImage = await loadSvgRoot(Assets.svgs.svgSpeedometer.path);
        dashboardControllerRead.volumeImage = await loadSvgRoot(Assets.svgs.svgVolumemeter.keyName);
        if (SessionRepository.session.aesSalt != '' && SessionRepository.session.aesSecretKey != '' && SessionRepository.session.baseUrlGet != '') {
          signInScreenWatch.updateIpMethod();
          signInScreenWatch.updateLoadingStatus(false);
        }

        showLog('getAppVersion started');

        /// Get App Version
        await getAppVersion();
        showLog('getAppVersion completed');

        showLog('getConnectedWifi started');

        /// Get Connected Wifi
        await wifiRead.getConnectedWifi();
        showLog('getConnectedWifi completed');

        final dioClient = getIt<DioClient>();
        if (SessionRepository.session.baseUrlGet.isNotEmpty) {
          dioClient.updateBaseUrl(SessionRepository.session.baseUrlGet);
        } else {
          showLog('IP NOT FOUND ${SessionRepository.session.baseUrlGet}');
        }
        final canvasMapWatch = ref.read(canvasMapController);
        showLog('CanvasMapApiClass started');
        await CanvasMapApiClass.api.initDio();
        showLog('CanvasMapApiClass completed');
        cpuTemperatureStream = canvasMapWatch.robotEventManger.cpuTemperatureStream.stream.listen((event) {
          canvasMapWatch.temperature = event;
          canvasMapWatch.notifyListeners();
        });
        canvasMapWatch.dasherImage = await MapVariables.map.loadSvgRoot(Assets.svgs.svgDasherImage.path);
        canvasMapWatch.chargingPointImage = await MapVariables.map.loadSvgRoot(Assets.svgs.svgMarkChargingPoint.path);
        canvasMapWatch.productionPointImage = await MapVariables.map.loadSvgRoot(Assets.svgs.svgOdigoProdcutionPoint.path);
        canvasMapWatch.deliveryPointImage = await MapVariables.map.loadSvgRoot(Assets.svgs.svgOdigoLocationPoint.path);
        canvasMapWatch.routeImage = await MapVariables.map.loadSvgRoot(Assets.svgs.svgRoutePoint.path);

// RobotSession.hostIpAddress = '*************';
// ref.read(navigationStackController).pushRemove(const NavigationStackItem.displayAds(startRouteNavigation: false, isFromWaitingScreen: false));

        Future.delayed(
          const Duration(milliseconds: 500),
          () async {
            if (RobotSession.selectedLanguageCode == '') {
              RobotSession.selectedLanguageCode = 'en';
            }
            showLog('navigation started');
            showLog("RobotSession.hostIpAddress >>>${RobotSession.hostIpAddress}");
            final lastSynced = SessionRepository.session.getLastSync;

            /// IF get ip from ROS then redirecting to the sign in/download screen
            if ((RobotSession.hostIpAddress == null || RobotSession.hostIpAddress == '' || RobotSession.hostIpAddress == '*********' || (RobotSession.hostIpAddress?.startsWith('127.0') ?? false)) && lastSynced == null) {
              /// else wifi screen for connection
              showLog('Navigate To Available Wifi RobotSession.hostIpAddress : ${RobotSession.hostIpAddress}--');
              ref.read(navigationStackController).pushRemove(const NavigationStackItem.availableWiFi());
            } else {
              setNavigationRedirection();
            }
            showLog('navigation completed');
          },
        );
      },
    );
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    globalContext = context;
    ref.watch(canvasMapController);
    return Scaffold(
      backgroundColor: AppColors.black,
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    return Center(
      child: Image.asset(Assets.images.odigo.path, height: 200).alignAtCenter(),
    );
  }

  void setNavigationRedirection() async {
    showLog('setNavigationRedirection Called');
    String? token = SessionRepository.session.token;
// showLog("SessionRepository.session.frontEndPublicKey ${SessionRepository.session.aesSecretKey}");
// showLog("SessionRepository.session.frontEndPublicKey ${SessionRepository.session.aesSalt}");
// showLog("SessionRepository.session.frontEndPublicKey ${SessionRepository.session.baseUrlGet}");

    /// Check Token
    if ((token?.isEmpty ?? false) || token == null || token == 'null') {
      /// Check AES SALT and SECRET KEY
      if (SessionRepository.session.aesSalt != '' && SessionRepository.session.aesSecretKey != '') {
        await ref.watch(signInScreenController).updateIpMethod();
        showLog('Navigate to signInScreen');
        ref.read(navigationStackController).pushRemove(const NavigationStackItem.signInScreen());
      } else {
        showLog('Navigate to initializeEncScreen');
        ref.read(navigationStackController).pushRemove(const NavigationStackItem.initializeEncScreen());
      }
    } else {
// await ref.read(authLoginController).saveSettingsApi(RobotSession.hostIpAddress ?? '', RobotSession.wifiName ?? '', isPutRequest: false);

      /// Check AES SALT and SECRET KEY
      if (SessionRepository.session.aesSalt != '' && SessionRepository.session.aesSecretKey != '') {
// isFrontDisplay = SessionRepository.session.displayType == 'ADS_PANEL';
        await ref.watch(signInScreenController).updateIpMethod();
        showLog('Map name  ${RobotSession.mapName}');

        /// MAP Syncing Flow
        if (RobotSession.mapName != null && RobotSession.mapName != '') {
          if (SessionRepository.session.loginData?.lastSync != null) {
            await checkLastSyncLogic();
          } else {
            showLog('Navigate to downloadScreen');
            ref.read(navigationStackController).pushRemove(const NavigationStackItem.downloadScreen());
          }
        } else {
          /// Map Syncing Flow
          showLog('Navigate to destinationRobotList');
          ref.read(navigationStackController).pushRemove(const NavigationStackItem.destinationRobotList());
        }
      } else {
        /// Encryption initialization
        showLog('Navigate to initializeEncScreen from setNavigationRedirection');
        ref.read(navigationStackController).pushRemove(const NavigationStackItem.initializeEncScreen());
      }
    }
  }

  /// Check Last sync for Failure Case
  Future<void> checkLastSyncLogic() async {
    /// Get last Sync TimeStamp and if we have data For Current day directly navigate to Display ads screen
    showLog('checkLastSyncLogic Called');
    final lastSynced = SessionRepository.session.getLastSync;
    if (lastSynced != null && lastSynced != '') {
      final lastSyncedDateTime = DateTime.fromMillisecondsSinceEpoch(int.parse(lastSynced));
      showLog('lastSyncedDateTime $lastSyncedDateTime');
      final now = DateTime.now();
      final difference = now.difference(lastSyncedDateTime);

      /// Same Day
      if (now.year == lastSyncedDateTime.year && now.month == lastSyncedDateTime.month && now.day == lastSyncedDateTime.day) {
        ref.read(downloadController).updateIsShownSkipButton(true);

        await ref.read(displayAdsController).getDefaultAdsList();
        await ref.read(displayAdsController).getDefaultAssetAd();
        await ref.read(displayAdsController).getDestinationDetails();
        await ref.read(displayAdsController).initializeCipher();
        showLog('Navigate to displayAds');
        ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.displayAds(startRouteNavigation: true, isFromWaitingScreen: false));
// ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.downloadScreen());
        ref.read(downloadController).notifyListeners();
      }

      /// Old Days
      else if (difference.isNegative) {
        showLog('Navigate to Download Screen from difference.isNegative');
        ref.read(downloadController).updateIsShownSkipButton(false);
        ref.read(navigationStackController).pushRemove(const NavigationStackItem.downloadScreen());
        commonToaster('Invalid lastSynced date: $lastSynced (It is in the future)');
      } else {
        showLog('Navigate to Download Screen from Else ');
        ref.read(downloadController).updateIsShownSkipButton(false);
        ref.read(navigationStackController).pushRemove(const NavigationStackItem.downloadScreen());
      }
    } else {
      showLog('Navigate to Download Screen from ${LocaleKeys.keyLastSyncedDateIsNull.localized}');
      ref.read(downloadController).updateIsShownSkipButton(false);
      ref.read(navigationStackController).pushRemove(const NavigationStackItem.downloadScreen());
      commonToaster(LocaleKeys.keyLastSyncedDateIsNull.localized);
    }
    ref.read(downloadController).notifyListeners();
  }
}
