import 'package:odigo_display/ui/utils/theme/theme.dart';

class StoreGridTile extends StatelessWidget with BaseStatelessWidget {
  final String name;
  final int totalAds;
  final String totalStorage;

  const StoreGridTile({
    super.key,
    required this.name,
    required this.totalAds,
    required this.totalStorage,
  });

  @override
  Widget buildPage(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.clrf9f9f9,
        borderRadius: BorderRadius.circular(15.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      child: Row(
        children: [
          CommonSVG(
            strIcon: Assets.svgs.svgStore.path,
            height: MediaQuery.sizeOf(context).height * 0.05,
          ),
          SizedBox(width: MediaQuery.sizeOf(context).width * 0.02),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonText(
                title: name,
                textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 15.sp),
              ),
              CommonText(
                title: '$totalAds Ads',
                textStyle: TextStyles.regular.copyWith(color: AppColors.black, fontSize: 10.sp),
              ),
              CommonText(
                title: '$totalStorage Used',
                textStyle: TextStyles.regular.copyWith(color: AppColors.black, fontSize: 10.sp),
              ),
            ],
          )
        ],
      ),
    );
  }
}
