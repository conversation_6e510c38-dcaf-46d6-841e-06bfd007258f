import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/framework/controller/operator/storage_controller.dart';
import 'package:odigo_display/framework/controller/operator/store_controller.dart';
import 'package:odigo_display/framework/provider/network/api_end_points.dart';
import 'package:odigo_display/ui/storage/mobile/helper/storage_guage.dart';
import 'package:odigo_display/ui/storage/mobile/helper/storage_store_list_tile.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/const/app_enums.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_display/ui/utils/widgets/dialog_progressbar.dart';
import 'package:odigo_display/ui/utils/widgets/empty_state_widget.dart';

class StorageMobile extends ConsumerStatefulWidget {
  const StorageMobile({super.key});

  @override
  ConsumerState createState() => _StorageMobileState();
}

class _StorageMobileState extends ConsumerState<StorageMobile> with BaseConsumerStatefulWidget, SingleTickerProviderStateMixin {
  double totalPercentage = 0;
  double percentage = 0;

  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 400));
      await initCall();
    });
    super.initState();
  }

  /// init call
  Future<void> initCall() async {
    final storageWatch = ref.read(storageController);
    final storeWatch = ref.read(storeController);
    storeWatch.disposeController();
    hideBottomMenu();
    await storageWatch.getStorageFromNative();
    storeWatch.updateSelectedEntityType(EntityTypes.VENDOR);
    totalPercentage = (((storageWatch.storageModel?.usedStorage ?? 1) / (storageWatch.storageModel?.totalStorage ?? 1))) * 100;
    animationController?.forward(from: 0);
    animationController?.addListener(animationListener);
  }

  AnimationController? animationController;

  void animationListener() {
    percentage = totalPercentage * (animationController?.value ?? 1);
    ref.read(storageController).notifyListeners();
  }

  @override
  void dispose() {
    animationController?.removeListener(animationListener);
    animationController = null;
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    final storageWatch = ref.watch(storageController);
    final storeWatch = ref.watch(storeController);
    final helpInfoWatch = ref.watch(helpInfoDashboardController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CommonBackWidget(
                      color: AppColors.white,
                    ),
                  ],
                ),
                SizedBox(height: 35.h),

                /// stores
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      title: LocaleKeys.keySTORAGE.localized,
                      textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp, fontFamily: TextStyles.manropeFontFamily),
                    ),
                    InkWell(
                      onTap: () {
                        showLogoDialog(context, LocaleKeys.keyAreYouSure.localized, LocaleKeys.keyRemoveOlderAdvertisementMediaFiles.localized, LocaleKeys.keyYesSure.localized, LocaleKeys.keyNoThanks.localized, (isPositive) async {
                          if (isPositive) {
                            storageWatch.updateIsLoading(true);
                            await storageWatch.deleteArchiveAdsAPI(context, ref);
                            if (storageWatch.deleteArchiveAdsState.success?.status == ApiEndPoints.apiStatus_200) {
                              if (storageWatch.deleteArchiveAdsState.success?.data?.isNotEmpty ?? false) {
                                await storageWatch.deleteArchiveAds(ref, storageWatch.deleteArchiveAdsState.success?.data ?? []);
                                initCall();
                              }
                            }
                            storageWatch.updateIsLoading(false);
                          }
                        });
                      },
                      child: CommonText(
                        title: LocaleKeys.keyFreeUpSpace.localized,
                        textStyle: TextStyles.bold.copyWith(color: AppColors.red, fontSize: 40.sp, fontFamily: TextStyles.manropeFontFamily, decoration: TextDecoration.underline, decorationColor: AppColors.red, decorationThickness: 4),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 17.h),

                /// explore nearby stores
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      title: LocaleKeys.keyExploreNearbyStores.localized,
                      textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 30.sp, fontFamily: TextStyles.manropeFontFamily),
                    ),
                  ],
                ),
                SizedBox(height: 38.h),
                Expanded(
                  child: ListView(
                    physics: storeWatch.isListLoading ? const NeverScrollableScrollPhysics() : null,
                    children: [
                      SizedBox(
                        height: context.height * 0.3,
                        child: MemoryGauge(
                          totalPercentage: percentage,
                          totalMemory: '${storageWatch.storageModel?.totalStorage.toStringAsFixed(2)} GB',
                        ),
                      ),
                      Row(
                        children: [
                          memoryWidget(
                            LocaleKeys.keyUsedMemory,
                            '${storageWatch.storageModel?.usedStorage.toStringAsFixed(2)} GB',
                          ),
                          const Expanded(child: SizedBox()),
                          memoryWidget(
                            LocaleKeys.keyFreeMemory,
                            '${storageWatch.storageModel?.availableStorage.toStringAsFixed(2)} GB',
                          ),
                        ],
                      ),
                      SizedBox(height: 38.h),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: context.width * 0.03),
                        decoration: BoxDecoration(
                          color: AppColors.clr212121,
                          borderRadius: BorderRadius.circular(32.r),
                        ),
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: List.generate(
                            storeWatch.entityList.length,
                            (index) {
                              return InkWell(
                                onTap: () {
                                  storeWatch.disposeController();
                                  storeWatch.updateSelectedEntityType(storeWatch.entityList[index]);
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: context.height * 0.015),
                                  decoration: storeWatch.selectedEntity == storeWatch.entityList[index]
                                      ? BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(width: 2.h, color: AppColors.white),
                                          ),
                                        )
                                      : null,
                                  child: CommonText(
                                    title: '${storeWatch.entityList[index].name.toLowerCase() == EntityTypes.AGENCY.name.toLowerCase() ? LocaleKeys.keyAgency.localized.toUpperCase() : LocaleKeys.keyStore.localized.toUpperCase()} ${(storeWatch.totalCount != 0) && (storeWatch.selectedEntity == storeWatch.entityList[index]) ? '(${storeWatch.totalCount})' : ''}',
                                    textStyle: TextStyles.medium.copyWith(
                                      fontSize: 40.sp,
                                      fontFamily: TextStyles.manropeFontFamily,
                                      color: storeWatch.selectedEntity == storeWatch.entityList[index] ? AppColors.white : AppColors.clr545454,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      SizedBox(height: 38.h),
                      if ((storeWatch.storeList.isNotEmpty || storeWatch.vendorList.isNotEmpty || storeWatch.agencyList.isNotEmpty || storeWatch.clientList.isNotEmpty))
                        if (storeWatch.selectedEntity == EntityTypes.VENDOR)
                          ...List.generate(
                            storeWatch.vendorList.length,
                            (int index) {
                              return StorageStoreListTile(
                                name: storeWatch.vendorList[index].vendorName,
                                assignedAdCount: storeWatch.vendorList[index].assignAdDetails.length,
                                memoryUsed: storeWatch.vendorList[index].storageUsedSize ?? 0,
                              );
                            },
                          )
                        else if (storeWatch.selectedEntity == EntityTypes.CLIENT)
                          ...List.generate(
                            storeWatch.clientList.length,
                            (int index) {
                              return StorageStoreListTile(
                                name: storeWatch.clientList[index].clientName,
                                assignedAdCount: storeWatch.clientList[index].assignAdDetails.length,
                                memoryUsed: storeWatch.clientList[index].storageUsedSize ?? 0,
                              );
                            },
                          )
                        else if (storeWatch.selectedEntity == EntityTypes.AGENCY)
                          ...List.generate(
                            storeWatch.agencyList.length,
                            (int index) {
                              return StorageStoreListTile(
                                name: storeWatch.agencyList[index].agencyName,
                                assignedAdCount: storeWatch.agencyList[index].assignAdDetails.length,
                                memoryUsed: storeWatch.agencyList[index].storageUsedSize ?? 0,
                              );
                            },
                          )
                        else
                          ...List.generate(
                            storageWatch.storeList.length,
                            (int index) {
                              return StorageStoreListTile(
                                name: storeWatch.storeList[index].name ?? '',
                                assignedAdCount: storeWatch.storeList[index].assignAdDetails.length,
                                memoryUsed: storeWatch.storeList[index].storageUsedSize ?? 0,
                              );
                            },
                          )
                      else
                        storeWatch.isListLoading
                            ? Container(
                                height: context.height * 0.4,
                                alignment: Alignment.center,
                                child: SizedBox(
                                  height: context.height * 0.05,
                                  width: context.height * 0.05,
                                  child: const CircularProgressIndicator(color: AppColors.white),
                                ),
                              )
                            : const Center(child: EmptyStateWidget())
                    ],
                  ),
                )
              ],
            ),
          ),
          DialogProgressBar(isLoading: storageWatch.isLoading)
        ],
      ),
    );
  }

  Widget memoryWidget(String title, String value) {
    return Column(
      children: [
        CommonText(
          title: title.localized,
          textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 38.sp),
        ),
        CommonText(
          title: value,
          textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 46.sp),
        ),
      ],
    );
  }
}
