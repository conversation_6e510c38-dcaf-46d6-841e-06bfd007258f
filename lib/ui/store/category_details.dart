import 'package:flutter/material.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/ui/store/mobile/category_details_mobile.dart';
import 'package:odigo_display/ui/utils/helper/base_widget.dart';
import 'package:responsive_builder/responsive_builder.dart';

class CategoryDetails extends StatelessWidget with BaseStatelessWidget {
  final String categoryName;
  final List<StoreData> storeList;

  const CategoryDetails({super.key, required this.categoryName, required this.storeList});

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) {
        return CategoryDetailsMobile(
          categoryName: categoryName,
          storeList: storeList,
        );
      },
    );
  }
}
