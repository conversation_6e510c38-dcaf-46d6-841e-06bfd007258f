import 'package:flutter/material.dart';
import 'package:odigo_display/ui/store/mobile/media_preview_mobile.dart';

class MediaPreview extends StatelessWidget{
  final bool isImage;
  final List<String?> mediaList;
  const MediaPreview({Key? key, required this.mediaList, required this.isImage}) : super(key: key);

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return MediaPreviewMobile(mediaList: mediaList,isImage: isImage);
  }


}

