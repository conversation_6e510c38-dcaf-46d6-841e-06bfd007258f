import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';

final mediaPreviewController = ChangeNotifierProvider(
  (ref) => getIt<MediaPreviewController>(),
);

@injectable
class MediaPreviewController extends ChangeNotifier {
  String? mediaPath = '';

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;
    mediaPath = '';
    if (isNotify) {
      notifyListeners();
    }
  }

  updateWidget() {
    notifyListeners();
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  ///Progress Indicator
  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }
}
