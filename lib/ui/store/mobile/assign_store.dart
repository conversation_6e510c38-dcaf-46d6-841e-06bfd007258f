import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/operator/store_controller.dart';
import 'package:odigo_display/framework/repository/meal_delivery/model/delivery_points_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';
import 'package:odigo_display/ui/utils/widgets/common_form_field.dart';
import 'package:odigo_display/ui/utils/widgets/empty_state_widget.dart';

class AssignStoreMobile extends ConsumerStatefulWidget {
  final String locationType;

  const AssignStoreMobile({super.key, required this.locationType});

  @override
  ConsumerState createState() => _AssignStoreMobileState();
}

class _AssignStoreMobileState extends ConsumerState<AssignStoreMobile> with BaseConsumerWidgetStateFullWidget {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final storeWatch = ref.read(storeController);
      await storeWatch.getStoreListApi();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    final storeWatch = ref.watch(storeController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: context.height * 0.03),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CommonBackWidget(
                  color: AppColors.white,
                  popCall: false,
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            SizedBox(height: 20.h),
            CommonText(
              title: LocaleKeys.keyAssignStores.localized.toUpperCase(),
              textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp, fontFamily: TextStyles.manropeFontFamily),
            ),
            SizedBox(height: 17.h),

            /// explore nearby stores
            CommonText(
              title: LocaleKeys.keyAssignStoresToRobotPoints.localized.capitalizeFirstLetterOfSentence,
              textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 30.sp, fontFamily: TextStyles.manropeFontFamily),
            ),
            SizedBox(height: 20.h),
            Expanded(
              child: storeWatch.newStoreList.isNotEmpty
                  ? Row(
                      children: [
                        Expanded(
                            child: Container(
                          decoration: BoxDecoration(border: Border.all(color: AppColors.clr5E5E5E), borderRadius: BorderRadius.circular(20.r)),
                          child: ListView.builder(
                            padding: EdgeInsets.zero,
                            itemBuilder: (context, index) {
                              StoreData storeData = storeWatch.newStoreList[index];
                              return _newStoreListTile(
                                storeUuid: storeData.uuid,
                                name: storeData.name,
                                storeImageUrl: storeData.storeImageUrl,
                                isStoreSelected: storeData.uuid == storeWatch.selectedNewStore?.uuid,
                                alreadyMapped: storeWatch.assignedStoreList.where((e) => e.uuid == storeData.uuid).firstOrNull != null,
                                onSelect: (storeUuid) async {
                                  storeWatch.selectedNewStore = storeData;
                                  await storeWatch.getRobotPointsApi();
                                },
                              );
                            },
                            itemCount: storeWatch.newStoreList.length,
                          ),
                        )),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: context.width * 0.03),
                            child: storeWatch.selectedNewStore == null || storeWatch.wayPointList.isEmpty
                                ? storeWatch.showLoadingDialog
                                    ? showLoadingDialog(context, LocaleKeys.keyFetchingPoints.localized)
                                    : EmptyStateWidget(title: LocaleKeys.keyYouHaveNoNewStoresToAssign.localized)
                                : Column(
                                    children: [
                                      CommonInputFormField(
                                        textEditingController: storeWatch.searchStoreController,
                                        validator: (str) => null,
                                        fieldHeight: context.height * 0.05,
                                        borderRadius: BorderRadius.circular(20.r),
                                        backgroundColor: AppColors.black,
                                        hintText: LocaleKeys.keySearchYourBrandHere.localized,
                                        hintTextStyle: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 24.sp),
                                        fieldTextStyle: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 24.sp),
                                        borderColor: AppColors.black,
                                        contentPadding: EdgeInsets.only(left: context.width * 0.01),
                                        onChanged: (str) {
                                          storeWatch.searchWaypoints();
                                        },
                                        suffixWidget: InkWell(
                                          onTap: () {
                                            storeWatch.searchStoreController.clear();
                                            storeWatch.searchWaypoints();
                                          },
                                          child: Icon(CupertinoIcons.clear, color: AppColors.white, size: 35.sp),
                                        ),
                                        cursorColor: AppColors.white,
                                      ),
                                      SizedBox(height: context.height * 0.02),
                                      Expanded(
                                        child: GridView.builder(
                                          padding: EdgeInsets.zero,
                                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 1,
                                            mainAxisExtent: context.height * 0.05,
                                            mainAxisSpacing: context.width * 0.02,
                                            crossAxisSpacing: context.width * 0.02,
                                          ),
                                          itemBuilder: (context, index) {
                                            Waypoint waypointData = storeWatch.searchWaypointList[index];
                                            return _wayPointListTile(
                                              waypoint: waypointData,
                                              alreadyMapped: storeWatch.assignedWayPointList.where((e) => e.name == waypointData.name).firstOrNull != null,
                                              onSelect: (Waypoint? waypoint) async {
                                                showConfirmationDialog(context, LocaleKeys.keyAddLocationPoint.localized, LocaleKeys.keyAreYouSure.localized, LocaleKeys.keyYes.localized, LocaleKeys.keyNo.localized, (po) async {
                                                  if (po) {
                                                    GlobalKey globalKey = GlobalKey();
                                                    showCommonSwitchingDialog(context: context, iconWidget: CommonSVG(strIcon: Assets.svgs.svgStoreList.path), text: LocaleKeys.keyMappingStores.localized, key: globalKey);
                                                    await storeWatch.getStoreDetailsApi();
                                                    if (globalKey.currentContext != null) {
                                                      Navigator.pop(globalKey.currentContext!);
                                                    }
                                                    showCommonSwitchingDialog(context: context, iconWidget: CommonSVG(strIcon: Assets.svgs.svgStoreList.path), text: LocaleKeys.keyMappingStores.localized, key: globalKey);
                                                    await storeWatch.addLocationApi(this.context, tappedPoint: waypointData, pointType: widget.locationType);
                                                    if (globalKey.currentContext != null) {
                                                      Navigator.pop(globalKey.currentContext!);
                                                    }
                                                  }
                                                });
                                              },
                                            );
                                          },
                                          itemCount: storeWatch.searchWaypointList.length,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        )
                      ],
                    )
                  : storeWatch.showLoadingDialog
                      ? showLoadingDialog(context, LocaleKeys.keyFetchingStores.localized)
                      : Center(
                          child: EmptyStateWidget(title: LocaleKeys.keyYouHaveNoNewStoresToAssign.localized),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  /// store list tile
  Widget _newStoreListTile({
    required String? storeUuid,
    required String? name,
    String? storeImageUrl,
    bool alreadyMapped = false,
    bool isStoreSelected = false,
    Function(String? storeUuid)? onSelect,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: context.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            color: isStoreSelected ? AppColors.white : AppColors.transparent,
          ),
          child: InkWell(
            onTap: () async {
              if (!alreadyMapped) {
                onSelect?.call(storeUuid);
              }
            },
            child: Opacity(
              opacity: alreadyMapped ? 0.2 : 1,
              child: Row(
                children: [
                  Expanded(
                    child: CommonText(
                      title: name ?? '',
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      textStyle: TextStyles.regular.copyWith(color: isStoreSelected ? AppColors.black : AppColors.white, fontSize: 30.sp),
                    ),
                  ),
                  if (!isStoreSelected) const Icon(Icons.keyboard_arrow_right, color: AppColors.clr707070),
                ],
              ),
            ),
          ).paddingSymmetric(horizontal: context.width * 0.02, vertical: context.height * 0.01),
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: context.width * 0.02),
          height: 0.2,
          width: context.width,
          color: AppColors.clr707070,
        )
      ],
    );
  }

  /// way point list tile
  Widget _wayPointListTile({
    required Waypoint? waypoint,
    bool alreadyMapped = false,
    Function(Waypoint? waypoint)? onSelect,
  }) {
    return InkWell(
      onTap: () async {
        if (!alreadyMapped) {
          onSelect?.call(waypoint);
        }
      },
      child: Opacity(
        opacity: alreadyMapped ? 0.2 : 1,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            color: AppColors.clr212121,
            border: Border.all(color: AppColors.grey3A3A3A, width: 1.w),
          ),
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.015, vertical: context.height * 0.003),
          alignment: Alignment.center,
          child: CommonText(
            title: waypoint?.name ?? '',
            maxLines: 2,
            textAlign: TextAlign.center,
            textStyle: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 24.sp),
          ),
        ),
      ),
    );
  }
}
