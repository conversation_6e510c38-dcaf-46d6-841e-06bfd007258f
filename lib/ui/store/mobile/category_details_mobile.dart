import 'dart:async';

import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/framework/controller/navigation/navigation_controller.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/utils/helper/voice_helper.dart';
import 'package:odigo_display/ui/display_ads/mobile/helper/search_store_list_tile.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';

class CategoryDetailsMobile extends ConsumerStatefulWidget {
  final String categoryName;
  final List<StoreData> storeList;

  const CategoryDetailsMobile({required this.categoryName, required this.storeList, super.key});

  @override
  ConsumerState<CategoryDetailsMobile> createState() => _CategoryDetailsMobileState();
}

class _CategoryDetailsMobileState extends ConsumerState<CategoryDetailsMobile> with BaseConsumerStatefulWidget {
  Timer? timer;
  ScrollController? scrollController;

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      scrollController = ScrollController();
      SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
        final helpInfoDashboardWatch = ref.read(helpInfoDashboardController);
        helpInfoDashboardWatch.stopRobotForFewSeconds(context, ref: ref, isFromMore: false, isFromMoreDetails: true);
        scrollController?.addListener(() {
          final helpInfoDashboardRead = ref.watch(helpInfoDashboardController);
          helpInfoDashboardRead.stopRobotForFewSeconds(context, ref: ref, isFromMore: false, isFromMoreDetails: true);
        });
      });
      super.initState();
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    timer = null;
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    final helpInfoDashboardWatch = ref.watch(helpInfoDashboardController);
    return Scaffold(
      backgroundColor: AppColors.clr0F0F0F,
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Top Back Icon
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CommonBackWidget(
                    color: AppColors.white,
                    // onTap: () {
                    //   print('On tap called');
                    //   ref.read(navigationStackController).pop();
                    // },
                  ),
                ],
              ).paddingSymmetric(horizontal: 30.w, vertical: 40.h),

              /// main body
              Text(
                '${LocaleKeys.keySTORES.localized} ${helpInfoDashboardWatch.searchStoresList.length}',
                style: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
              ).paddingSymmetric(horizontal: 58.w),
              SizedBox(height: context.height * 0.01),
              Text(
                LocaleKeys.keyExploreNearbyStores.localized,
                style: TextStyles.light.copyWith(fontSize: 30.sp, color: AppColors.white, fontFamily: TextStyles.manropeFontFamily),
              ).paddingSymmetric(horizontal: 58.w),
              SizedBox(height: context.height * 0.015),

              Expanded(
                child: ListView.separated(
                  controller: scrollController,
                  itemCount: helpInfoDashboardWatch.searchStoresList.length,
                  itemBuilder: (BuildContext context, int index) {
                    return SearchStoreListTile(
                      isFromCategoryDetails: true,
                      store: helpInfoDashboardWatch.searchStoresList[index],
                      onStoreSelect: () {
                        ///TODO add audio : "Follow me"
                        VoiceHelper.instance.playAudioFromAsset(AppAssets.followMe);
                        // newDashboardWatch.socketManager.audioPlayer(audioFile: AppAssets.followMe);.
                        Navigator.pop(context);
                      },
                      isFromMore: true,
                    ).paddingSymmetric(horizontal: 24.w);
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return SizedBox(height: 20.h);
                  },
                ).paddingOnly(bottom: 10.h),
              ),
              SizedBox(
                height: 150.h,
              ),
            ],
          ).paddingSymmetric(horizontal: 30.w),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.black,
                gradient: LinearGradient(
                  colors: [Colors.transparent, Colors.black, Colors.black],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  // stops: [0.01, 1],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [CommonSVG(strIcon: Assets.svgs.svgKodyRobotsIcon.path, height: context.height * 0.1, width: context.width * 0.22), Image.asset(Assets.images.kodyRobotsQr.path)],
              ).paddingOnly(left: context.width * 0.03, right: context.width * 0.03, bottom: context.height * 0.01, top: context.height * 0.03),
            ),
          )
        ],
      ),
    );
  }
}
