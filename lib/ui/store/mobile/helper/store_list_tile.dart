import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class StoreListTile extends ConsumerWidget with BaseConsumerWidget {
  final String name;
  final int totalAds;
  final String totalBalance;
  final StoreData storePointsOnDestination;

  const StoreListTile({
    super.key,
    required this.name,
    required this.totalAds,
    required this.totalBalance,
    required this.storePointsOnDestination,
  });

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.clrf9f9f9,
        borderRadius: BorderRadius.circular(15.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      child: Row(
        children: [
          CommonSVG(
            strIcon: Assets.svgs.svgStore.path,
            height: MediaQuery.sizeOf(context).height * 0.07,
          ),
          SizedBox(width: MediaQuery.sizeOf(context).width * 0.03),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonText(
                  title: name,
                  textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 15.sp),
                ),
                CommonText(
                  title: '$totalAds Ads',
                  textStyle: TextStyles.regular.copyWith(color: AppColors.black, fontSize: 10.sp),
                ),
                CommonText(
                  title: 'Wallet: $totalBalance\$',
                  textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 10.sp),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              if (storePointsOnDestination.assignAdDetails.isNotEmpty ?? false) {
                ref.read(navigationStackController).push(NavigationStackItem.storeDetails(storeData: storePointsOnDestination,isAgency: false));
              }
            },
            child: Container(
              padding: EdgeInsets.all(5.r),
              decoration: const BoxDecoration(color: AppColors.clrCCCCCC, shape: BoxShape.circle),
              child: Icon(CupertinoIcons.chevron_right, color: AppColors.black, size: 25.r),
            ),
          ),
        ],
      ),
    );
  }
}
