import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/utils/extension/context_extension.dart';
import 'package:odigo_display/framework/utils/extension/extension.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/store/media_preview_controller.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/helper/base_widget.dart';
import 'package:odigo_display/ui/utils/theme/app_colors.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:video_player/video_player.dart';

class MediaPreviewMobile extends ConsumerStatefulWidget {
  final bool isImage;
  final List<String?>? mediaList;

  const MediaPreviewMobile({Key? key, required this.mediaList, required this.isImage}) : super(key: key);

  @override
  ConsumerState<MediaPreviewMobile> createState() => _MediaPreviewMobileState();
}

class _MediaPreviewMobileState extends ConsumerState<MediaPreviewMobile> with BaseConsumerStatefulWidget {
  ChewieController? chewieController;
  String? image;
  VideoPlayerController? videoPlayerController;

  ///Init Override
  @override
  void initState() {
    super.initState();
    final mediaPreviewWatch = ref.read(mediaPreviewController);
    mediaPreviewWatch.disposeController(isNotify: true);
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final displayAdsScreenWatch = ref.watch(displayAdsController);
      hideBottomMenu();

      if (!widget.isImage) {
        if (widget.mediaList != null && (widget.mediaList?.first?.isNotEmpty ?? false)) {
          await displayAdsScreenWatch.decryptFile(widget.mediaList!.first ?? '');
          mediaPreviewWatch.mediaPath = widget.mediaList!.first!.decryptedFilePath;
          mediaPreviewWatch.updateWidget();
          loadVideo(mediaPreviewWatch.mediaPath, ref);
        }
      } else {
        if (widget.mediaList != null && (widget.mediaList?.first?.isNotEmpty ?? false)) {
          await displayAdsScreenWatch.decryptFile(widget.mediaList!.first ?? '');
          mediaPreviewWatch.mediaPath = widget.mediaList!.first!.decryptedFilePath;
          mediaPreviewWatch.updateWidget();
        }
      }
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();

    videoPlayerController?.dispose();
  }

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    final mediaPreviewWatch = ref.watch(mediaPreviewController);
    return Scaffold(
      backgroundColor: AppColors.black,
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final mediaPreviewWatch = ref.read(mediaPreviewController);
    return Stack(
      children: [
        (widget.mediaList != null && widget.mediaList!.isNotEmpty)
            ? widget.isImage
                ? widget.mediaList!.first!.isNotEmpty
                    ? (widget.mediaList?.length ?? 0) > 1
                        ? PageView.builder(
                            itemCount: widget.mediaList?.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              showLog('widget.mediaList?[index] ${widget.mediaList?[index]}');
                              showLog('widget.mediaList?[index] ${File(widget.mediaList?[index].decryptedFilePath ?? '').existsSync()}');
                              return Image.file(
                                File(widget.mediaList?[index].decryptedFilePath ?? ''),
                                height: context.height,
                                width: context.width,
                                fit: BoxFit.contain,
                              ).alignAtCenter();
                            },
                          )
                        : Image.file(
                            File(mediaPreviewWatch.mediaPath ?? ''),
                            height: context.height,
                            width: context.width,
                            fit: BoxFit.contain,
                          ).alignAtCenter()
                    : const SizedBox()
                : chewieController != null
                    ? Chewie(
                        controller: chewieController!,
                      )
                    : const SizedBox()
            : const SizedBox(),
        InkWell(
          onTap: () async {
            chewieController?.dispose();
            videoPlayerController?.dispose();
            chewieController = null;
            videoPlayerController = null;
            image = null;
            Future.delayed(const Duration(milliseconds: 500), () {
              ref.read(navigationStackController).pop();
            });
          },
          child: Container(
            padding: EdgeInsets.all(10.r),
            margin: EdgeInsets.only(top: context.height * 0.03, left: 50.w),
            decoration: const BoxDecoration(color: AppColors.white, shape: BoxShape.circle),
            child: Icon(CupertinoIcons.back, color: AppColors.black, size: 45.r),
          ),
        ),
      ],
    );
  }

  Future<void> loadVideo(String? url, WidgetRef ref) async {
    final mediaPreviewWatch = ref.watch(mediaPreviewController);
    videoPlayerController = VideoPlayerController.file(File(url ?? ''));

    await videoPlayerController?.initialize();
    mediaPreviewWatch.updateWidget();
    chewieController = ChewieController(
      videoPlayerController: videoPlayerController!,
      autoPlay: true,
      looping: false,
      customControls: const Offstage(),
      allowFullScreen: true,
      fullScreenByDefault: true,
    );

    /// Video Player Controller
    videoPlayerController?.setLooping(false);

    mediaPreviewWatch.updateWidget();
    videoPlayerController?.addListener(() {
      if (videoPlayerController?.value.duration == videoPlayerController?.value.position) {
        showLog('Video Ended');
        videoPlayerController?.dispose();
      }
    });
  }
}
