import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:odigo_display/framework/controller/operator/store_controller.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/framework/repository/new_logic/model/synced_data_response_model.dart';
import 'package:odigo_display/ui/routing/navigation_stack_item.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/store/mobile/helper/advertisement_list_tile.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_back_widget.dart';

class StoreDetailsMobile extends ConsumerStatefulWidget {
  final StoreData storeData;
  final bool isAgency;

  const StoreDetailsMobile({super.key, required this.storeData, required this.isAgency});

  @override
  ConsumerState createState() => _StoreDetailsMobileState();
}

class _StoreDetailsMobileState extends ConsumerState<StoreDetailsMobile> with BaseConsumerStatefulWidget {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      hideBottomMenu();
      final storeWatch = ref.read(storeController);
      storeWatch.getRobotMetaData();
      if (widget.isAgency) {
        storeWatch.getAdListForAgency(widget.storeData.uuid ?? '');
      }
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    final storeWatch = ref.watch(storeController);
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        alignment: Alignment.topCenter,
        children: [
          /// main body
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// top bar
              SizedBox(height: 246.h),

              /// list scroll view
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 170.h,
                        width: double.infinity,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CommonText(
                            title: widget.storeData.name ?? '',
                            textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                          ),
                          SizedBox(height: 9.h, width: double.infinity),
                          if (widget.storeData.categoryData.isNotEmpty && widget.storeData.categoryData.first.name != null)
                            CommonText(
                              title: widget.storeData.categoryData.isNotEmpty ? widget.storeData.categoryData.first.name ?? '' : '',
                              textStyle: TextStyles.thin.copyWith(fontSize: 38.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                            ),
                        ],
                      ),
                      SizedBox(height: 44.h),

                      // wallet balance
                      if (widget.storeData.walletBalance != null)
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.black,
                            borderRadius: BorderRadius.circular(43.r),
                          ),
                          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 20.h),
                          child: Row(
                            children: [
                              SizedBox(height: 87.h, child: Lottie.asset(Assets.animation.animWalletdata.keyName).paddingOnly(right: 33.w)),
                              Expanded(
                                child: CommonText(
                                  title: LocaleKeys.keyWalletBalance.localized,
                                  textStyle: TextStyles.regular.copyWith(fontSize: 40.sp, color: AppColors.white),
                                ),
                              ),

                              /// TODO Currency Change
                              CommonText(
                                // title: '${widget.storeData.walletBalance}'.withCurrency,
                                title: storeWatch.destinationModel?.destinationCurrency != null ? ('${storeWatch.destinationModel?.destinationCurrency} ${widget.storeData.walletBalance}') : (widget.storeData.walletBalance?.withCurrency ?? ''),
                                textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.white),
                              ),
                            ],
                          ),
                        ).paddingOnly(bottom: 50.h),
                      if (widget.isAgency == true)
                        if (storeWatch.agencyAdList.isNotEmpty)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              /// odigo_displays list
                              CommonText(
                                title: LocaleKeys.keyAdvertisements.localized,
                                textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                              ).paddingOnly(top: 20.h, bottom: 20.h),

                              /// grid
                              GridView.builder(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2, mainAxisSpacing: 13.h, crossAxisSpacing: 13.h, childAspectRatio: 1),
                                itemCount: storeWatch.agencyAdList.length,
                                itemBuilder: (context, index) {
                                  AdDetails? assignAdDetail = storeWatch.agencyAdList[index];
                                  showLog("assignAdDetail ${assignAdDetail.mediaList}");

                                  String adsMediaName = '';
                                  if (assignAdDetail.mediaList.isNotEmpty) {
                                    adsMediaName = assignAdDetail.mediaList.first.split('/').last;
                                  } else {
                                    adsMediaName = '-';
                                  }
                                  return AdvertisementListTile(
                                    thumbnailImage: assignAdDetail.logo ?? '',
                                    name: adsMediaName,
                                    type: assignAdDetail.mediaType ?? '',
                                    typeDesc: '${assignAdDetail.duration} Sec',
                                    count: storeWatch.getFullyPlayedAdCount(assignAdDetail.uuid!).toString(),
                                    mediaList: assignAdDetail.mediaList,
                                    onTap: () async {
                                      ref.read(navigationStackController).push(NavigationStackItem.mediaPreview(isImage: assignAdDetail.mediaType?.toLowerCase() == 'image'.toLowerCase() ? true : false, mediaList: assignAdDetail.mediaList ?? []));
                                    },
                                  );
                                },
                              ).paddingOnly(bottom: 50.h)
                            ],
                          )
                        else
                          const Offstage(),
                      if (widget.isAgency == true)
                        Column(
                          children: [
                            Visibility(
                              visible: storeWatch.clientList.isNotEmpty,
                              child: CommonText(
                                title: LocaleKeys.keyClients.localized,
                                textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                              ).alignAtCenterLeft().paddingOnly(top: 50.h, left: 20.w),
                            ),
                            ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: storeWatch.clientList.length,
                              shrinkWrap: true,
                              padding: EdgeInsets.only(top: 20.h),
                              itemBuilder: (context, index) {
                                return Column(
                                  children: [
                                    InkWell(
                                      onTap: () async {
                                        await storeWatch.getAdListForClient(storeWatch.clientList[index].clientUuid);
                                        for (final img in storeWatch.adsListData) {
                                          for (final media in img.mediaList) {
                                            if (File(media.decryptedFilePath).existsSync()) {
                                            } else {
                                              storeWatch.decryptFile(media);
                                            }
                                          }
                                        }
                                        bool isDecryptedAds = false;
                                        for (final img in storeWatch.adsListData) {
                                          for (final media in img.mediaList) {
                                            isDecryptedAds = File(media.decryptedFilePath).existsSync();

                                            if (!isDecryptedAds) {
                                              break;
                                            }
                                            showLog('File(media.decryptedFilePath).existsSync() ${File(media.decryptedFilePath).existsSync()}');
                                          }
                                        }

                                        storeWatch.updateVisibility(index);
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(43.r), border: Border.all(color: AppColors.black, width: 1)),
                                        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                SizedBox(
                                                  height: 87.h,
                                                  child: Icon(Icons.person, size: 60.h, color: AppColors.black),
                                                ),
                                                SizedBox(width: 20.w),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: CommonText(
                                                              title: storeWatch.clientList[index].clientName.capsFirstLetterOfSentence ?? '',
                                                              textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black),
                                                            ),
                                                          ),

                                                          /// TODO Currency Change
                                                          CommonText(
                                                            // title: '${storeWatch.clientList[index].walletBalance}'.withCurrency,
                                                            title: storeWatch.destinationModel?.destinationCurrency != null ? ('${storeWatch.destinationModel?.destinationCurrency} ${storeWatch.clientList[index].walletBalance}') : (storeWatch.clientList[index].walletBalance.toString().withCurrency ?? ''),
                                                            textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black),
                                                          ),
                                                        ],
                                                      ).paddingOnly(bottom: 10.h),
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: CommonText(
                                                              title: getAdsString(storeWatch.getAdCountForClient(clientUuid: storeWatch.clientList[index].clientUuid)),
                                                              textStyle: TextStyles.medium.copyWith(fontSize: 40.sp, color: AppColors.black),
                                                            ),
                                                          ),
                                                          if (storeWatch.getAdCountForClient(clientUuid: storeWatch.clientList[index].clientUuid ?? '') != 0)
                                                            RotatedBox(
                                                              quarterTurns: storeWatch.currentIndex == index ? 2 : 0,
                                                              child: Icon(
                                                                Icons.expand_circle_down,
                                                                color: AppColors.black,
                                                                size: 80.r,
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            if (storeWatch.currentIndex == index && storeWatch.adsListData.isNotEmpty)
                                              SizedBox(
                                                height: context.height * 0.17,
                                                child: ListView.builder(
                                                  physics: const NeverScrollableScrollPhysics(),
                                                  padding: EdgeInsets.zero,
                                                  itemCount: storeWatch.adsListData.length,
                                                  itemBuilder: (context, adIndex) {
                                                    AdDetails adDetail = storeWatch.adsListData[adIndex];
                                                    return AdvertisementListTile(
                                                      thumbnailImage: adDetail.logo ?? '',
                                                      name: adDetail.mediaList.first.split('/').last,
                                                      type: adDetail.mediaType ?? '',
                                                      typeDesc: '${adDetail.duration} Sec',
                                                      count: storeWatch.getFullyPlayedAdCount(adDetail.uuid!).toString(),
                                                      mediaList: adDetail.mediaList,
                                                      onTap: () async {
                                                        for (final img in storeWatch.adsListData) {
                                                          for (final media in img.mediaList) {
                                                            if (File(media.decryptedFilePath).existsSync()) {
                                                            } else {
                                                              storeWatch.decryptFile(media);
                                                            }
                                                          }
                                                        }
                                                        bool isDecryptedAds = false;
                                                        for (final img in storeWatch.adsListData) {
                                                          for (final media in img.mediaList) {
                                                            isDecryptedAds = File(media.decryptedFilePath).existsSync();

                                                            if (!isDecryptedAds) {
                                                              break;
                                                            }
                                                            showLog('File(media.decryptedFilePath).existsSync() ${File(media.decryptedFilePath).existsSync()}');
                                                          }
                                                        }

                                                        if (isDecryptedAds) {
                                                          ref.read(navigationStackController).push(
                                                                NavigationStackItem.mediaPreview(isImage: adDetail.mediaType?.toLowerCase() == 'image'.toLowerCase() ? true : false, mediaList: adDetail.mediaList),
                                                              );
                                                        }
                                                      },
                                                    ).paddingOnly(bottom: 40.h, top: context.height * 0.02);
                                                  },
                                                ),
                                              )
                                            else
                                              const Offstage(),
                                          ],
                                        ),
                                      ).paddingOnly(bottom: 20.h),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        )
                      else
                        Column(
                          children: [
                            Visibility(
                              visible: storeWatch.storeList.isNotEmpty,
                              child: CommonText(
                                title: LocaleKeys.keyStores.localized,
                                textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                              ).alignAtCenterLeft().paddingOnly(top: 20.h, left: 20.w),
                            ),
                            ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: storeWatch.storeList.length,
                              shrinkWrap: true,
                              padding: EdgeInsets.only(top: 20.h),
                              itemBuilder: (context, index) {
                                return Column(
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        storeWatch.updateVisibility(index);
                                        storeWatch.getAdsListForStore(storeWatch.storeList[index].storeUuid ?? '');
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(43.r), border: Border.all(color: AppColors.black, width: 1)),
                                        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                (storeWatch.storeList[index].storeImagePath?.isNotEmpty ?? false)
                                                    ? Image.file(
                                                        File(storeWatch.storeList[index].storeImagePath!),
                                                        height: 95.h,
                                                        gaplessPlayback: true,
                                                      )
                                                    : Icon(Icons.store, size: 60.h, color: AppColors.black),
                                                SizedBox(width: 20.w),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: CommonText(
                                                              title: storeWatch.storeList[index].name?.capsFirstLetterOfSentence ?? '',
                                                              textStyle: TextStyles.bold.copyWith(fontSize: 40.sp, color: AppColors.black),
                                                            ),
                                                          ),
                                                        ],
                                                      ).paddingOnly(bottom: 10.h),
                                                      CommonText(
                                                        title: getAdsString(storeWatch.getAdCountForStore(storeUuid: storeWatch.storeList[index].storeUuid ?? '')),
                                                        textStyle: TextStyles.medium.copyWith(fontSize: 40.sp, color: AppColors.black),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                if (storeWatch.getAdCountForStore(storeUuid: storeWatch.storeList[index].storeUuid ?? '') != 0)
                                                  RotatedBox(
                                                    quarterTurns: storeWatch.currentIndex == index ? 2 : 0,
                                                    child: Icon(
                                                      Icons.expand_circle_down,
                                                      color: AppColors.black,
                                                      size: 80.r,
                                                    ),
                                                  ),
                                              ],
                                            ),
                                            if (storeWatch.currentIndex == index && storeWatch.adsListData.isNotEmpty)
                                              Column(
                                                children: [
                                                  SizedBox(height: context.height * 0.02),
                                                  SizedBox(
                                                    height: context.height * 0.17,
                                                    child: ListView.builder(
                                                      padding: EdgeInsets.zero,
                                                      physics: const NeverScrollableScrollPhysics(),
                                                      itemCount: storeWatch.adsListData.length,
                                                      itemBuilder: (context, adIndex) {
                                                        return AdvertisementListTile(
                                                          thumbnailImage: storeWatch.adsListData[adIndex].logo ?? '',
                                                          name: storeWatch.adsListData[adIndex].mediaList.first.split('/').last,
                                                          type: storeWatch.adsListData[adIndex].mediaType ?? '',
                                                          typeDesc: '${storeWatch.adsListData[adIndex].duration} Sec',
                                                          count: storeWatch.getFullyPlayedAdCount(storeWatch.adsListData[adIndex].uuid!).toString(),
                                                          mediaList: storeWatch.adsListData[adIndex].mediaList,
                                                          onTap: () async {
                                                            ref.read(navigationStackController).push(NavigationStackItem.mediaPreview(isImage: storeWatch.adsListData[adIndex].mediaType?.toLowerCase() == 'image'.toLowerCase() ? true : false, mediaList: storeWatch.adsListData[adIndex].mediaList));
                                                          },
                                                        ).paddingOnly(bottom: 40.h);
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              )
                                            else
                                              const Offstage()
                                          ],
                                        ),
                                      ).paddingOnly(bottom: 20.h),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                    ],
                  ).paddingSymmetric(horizontal: 20.w),
                ),
              ),
              SizedBox(
                height: 25.h,
              ),
            ],
          ),

          /// top bar
          Positioned(
            top: kReleaseMode ? 0 : 10,
            left: 0,
            right: 0,
            child: Container(
              height: 286.h,
              decoration: BoxDecoration(color: AppColors.clrE5E5E5, borderRadius: BorderRadius.only(bottomLeft: Radius.circular(78.r), bottomRight: Radius.circular(78.r))),
              padding: EdgeInsets.only(left: 50.w, right: 50.w, top: context.height * 0.03),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Container(
                  //   decoration: BoxDecoration(color: AppColors.greenC0FFAA, borderRadius: BorderRadius.circular(34.r)),
                  //   padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                  //   child: CommonText(
                  //     title: getAdsString(storeWatch.agencyAdList.length),
                  //     textStyle: TextStyles.medium.copyWith(fontSize: 31.sp, color: AppColors.black, fontFamily: TextStyles.manropeFontFamily),
                  //   ),
                  // ),
                  SizedBox(
                    width: 10.w,
                  ),
                  CommonBackWidget(
                    onTap: () {
                      storeWatch.updateVisibility(null);
                    },
                  ),
                ],
              ),
            ),
          ),

          /// center image
          Positioned(
            top: 178.h,
            child: Container(
              height: context.width * 0.22,
              width: context.width * 0.22,
              padding: EdgeInsets.all(20.r),
              // Border width
              decoration: const BoxDecoration(color: AppColors.black, shape: BoxShape.circle),
              child: ClipOval(
                child: (widget.storeData.storeImagePath?.isNotEmpty ?? false)
                    ? Image.file(
                        File(widget.storeData.storeImagePath!),
                        height: 95.h,
                        fit: BoxFit.cover,
                        gaplessPlayback: true,
                      )
                    : Container(
                        color: AppColors.black,
                        child: Icon(
                          widget.isAgency ? Icons.real_estate_agent : Icons.store,
                          size: 60.h,
                          color: AppColors.white,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
