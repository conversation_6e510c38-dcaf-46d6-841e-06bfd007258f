import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/dashboard/help_info_dashboard_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_screen_controller.dart';
import 'package:odigo_display/framework/controller/operator/store_controller.dart';
import 'package:odigo_display/framework/repository/new_logic/model/store_response_model.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class StoreInfoMobile extends ConsumerStatefulWidget {
  final StoreData storeData;
  const StoreInfoMobile({Key? key, required this.storeData}) : super(key: key);

  @override
  ConsumerState<StoreInfoMobile> createState() => _StoreInfoMobileState();
}

class _StoreInfoMobileState extends ConsumerState<StoreInfoMobile> {

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final storeWatch = ref.read(storeController);
      await storeWatch.getAdsListForStore(widget.storeData.storeUuid??'');
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final storeWatch = ref.watch(storeController);
    return Column(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonText(
                      title: LocaleKeys.keyStore.localized.toUpperCase(),
                      textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp, fontFamily: TextStyles.manropeFontFamily),
                    ),
                    SizedBox(height: 17.h),

                    CommonText(
                      title: LocaleKeys.keyExploreStore.localized,
                      textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 30.sp, fontFamily: TextStyles.manropeFontFamily),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    final displayAdsScreenWatch = ref.read(displayAdsController);
                    final helpInfoDashboardWatch = ref.read(helpInfoDashboardController);
                    displayAdsScreenWatch.chewieController?.play();
                    // displayAdsScreenWatch.getCurrentIndexOfCurrentAds(context, isInterrupt: true, ref: ref, isFromOperator: true);
                    ref.read(helpInfoDashboardController).operatorEndInterruptionTime();
                    ref.read(displayAdsController).onOperatorInterrupt();
                    // helpInfoDashboardWatch.reStartTimer(context, ref: ref);
                    // helpInfoDashboardWatch.socketManager.resumeNavigation();
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(8.r),
                    decoration: BoxDecoration(color: AppColors.transparent, shape: BoxShape.circle, border: Border.all(width: 1.w, color: AppColors.white)),
                    child: Icon(Icons.close, color: AppColors.white, size: 42.r),
                  ),
                ),
              ],
            ),
            SizedBox(height: 38.h),

            const Divider(color: AppColors.clrWhite,),
            SizedBox(height: 38.h),

            _commonTitleBody(LocaleKeys.keyStore.localized,widget.storeData.name?? ''),
            _commonTitleBody(LocaleKeys.keyFloorNumber.localized,widget.storeData.floorNumber.toString()??''),
            SizedBox(height: 38.h),

            const Divider(color: AppColors.clrWhite,),
            SizedBox(height: 38.h),
            CommonText(
              title: LocaleKeys.keyAdvertisements.localized.toUpperCase(),
              textStyle: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 40.sp, fontFamily: TextStyles.manropeFontFamily),
            ),
            SizedBox(height: 17.h),

            // storeWatch.adsListData.isNotEmpty
            //     ? SizedBox(
            //   height: context.height * 0.17,
            //   child: ListView.builder(
            //     padding: EdgeInsets.zero,
            //     itemCount: storeWatch.adsListData.length,
            //     physics: const NeverScrollableScrollPhysics(),
            //     itemBuilder: (context, adIndex) {
            //       AdDetails adDetail = storeWatch.adsListData[adIndex];
            //       return AdvertisementListTile(
            //         showCount: false,
            //         thumbnailImage: adDetail.logo ?? '',
            //         name: adDetail.mediaList.first.split('/').last,
            //         type: adDetail.mediaType ?? '',
            //         typeDesc: '${adDetail.duration} Sec',
            //         count: storeWatch.getFullyPlayedAdCount(adDetail.uuid!).toString(),
            //         onTap: () async {
            //           // ref.read(navigationStackController).push(
            //           //   NavigationStackItem.mediaPreview(isImage: adDetail.mediaType?.toLowerCase() == 'image'.toLowerCase() ? true : false, mediaList: adDetail.mediaList),
            //           // );
            //         },
            //       ).paddingOnly(bottom: 40.h, top: context.height * 0.02);
            //     },
            //   ),
            // )
            //     : const EmptyStateWidget(),

          ],
        ),
      ],
    ).paddingSymmetric(horizontal: 50.w, vertical: context.height * 0.03);
  }

  _commonTitleBody(title, subTitle) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        /// title
        Expanded(
          flex: 2,
          child: Text(
            title,
            style: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 40.sp),
          ),
        ),
        SizedBox(
          width: context.width * 0.02,
        ),
        Expanded(
          flex: 2,
          child: Text(
            subTitle,
            style: TextStyles.medium.copyWith(color: AppColors.white, fontSize: 40.sp),
          ),
        ),
      ],
    );

  }


}
