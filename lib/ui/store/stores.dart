import 'package:odigo_display/framework/repository/new_logic/model/category_response_model.dart';
import 'package:odigo_display/ui/store/mobile/stores_mobile.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';

class Categories extends StatelessWidget {
  final List<CategoryData> categoriesList;

  const Categories({super.key, required this.categoriesList});

  @override
  Widget build(BuildContext context) {
    return CategoriesMobile(categoriesList: categoriesList);
  }
}
