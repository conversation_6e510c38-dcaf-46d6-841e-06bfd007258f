import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg_custom/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

const String appName = 'Odigo Display';
const String userBoxName = 'userBox';

String googlePlaceApiKey = '';
String staticPackageName = 'com.kody.odigo_display';

bool isInternetConnectionOn = true;

bool getIsIOSPlatform() => Platform.isIOS;

bool getIsAppleSignInSupport() => iosVersion >= 13;
int iosVersion = 11;
double dasherHeight = 23;
double dasherWidth = 48;
const String imageFolderName = 'images';
const String videoFolderName = 'videos';

const String staticPassword = '123456';

String getDeviceType() => getIsIOSPlatform() ? 'ios' : 'android';

int contactNumberLength = 10;
int accountNumberLength = 18;
int waitingTimeSeconds = 15;
int minWaitingTimeSeconds = 10;
int iBanCodeLengthWithSpaces = 29;
int zipCodeLength = 5;
String countryCode = '+91';
String currency = 'INR';
int pageCount = 10;
int nameLength = 30;
int userNameLength = 25;
int passwordLength = 16;
int houseNameLength = 5;
int roadAreaLength = 40;
int stopRobotSeconds = 10;

int maxImageSelection = 8;
int maxVideoSizeInBytes = ********;
int pageSize100000 = 100000;

BuildContext? globalContext;

/// Hide Keyboard
hideKeyboard(BuildContext context) {
  // FocusScope.of(context).unfocus();
  FocusManager.instance.primaryFocus?.unfocus();
}

///Show Log
showLog(String str) {
  // ignore: avoid_print
  print(str);
}

bool isAllowCellular = true;
int downloadTimeout = 1500;

String wifiSymbol(int strength, bool isLock) {
  if (strength == 0 && isLock == true) {
    return Assets.svgs.svgWifiLockOne.keyName;
  } else if (strength == 1 && isLock == true) {
    return Assets.svgs.svgWifiLockTwo.keyName;
  } else if (strength == 2 && isLock == true) {
    return Assets.svgs.svgWifiLockThree.keyName;
  } else if (strength == 0 && isLock == false) {
    return Assets.svgs.svgWifiOne.keyName;
  } else if (strength == 1 && isLock == false) {
    return Assets.svgs.svgWifiTwo.keyName;
  } else {
    return Assets.svgs.svgWifiThree.keyName;
  }
}

String generateFileName() {
  return DateFormat('yyyy_MM_dd_HH_mm_ss_SSS').format(DateTime.now());
}

/// Notification Channel Details
const String notificationChannelKey = 'basic_channel';
const String notificationChannelName = 'Basic Notification';
const String notificationChannelDescription = 'Notification channel for basic test';

const String apikeyGoogleIos = '';
const String apikeyGoogleAndroid = '';
const String apikeyGoogleAppIdIos = '';
const String apikeyGoogleAppIdAndroid = '';
const String apikeyGoogleMessagingSenderId = '';
const String apikeyGoogleProjectId = '';

/// Api Integration

const String accept = 'Accept';

int connectionTimeOut = 100;

const String headerAccept = 'application/json';
const String contentType = 'contentType';
const String headerContentType = 'application/json';
const String acceptLanguage = 'Accept-Language';
const String production = 'production';
const String development = 'development';
const String authorization = 'Authorization';
const String bearer = 'Bearer';
const String responseType = 'responseType';
const String followRedirects = 'followRedirects';
const String connectTimeout = 'connectTimeout';
const String receiveTimeout = 'receiveTimeout';
const String headers = 'Headers';
const String extras = 'Extras';
const String queryParameters = 'Query Parameters';
const String get = 'GET';
const String body = 'Body';
const String formData = 'Form data';
const String dioError = 'DioError';
const String status = 'Status:';
const int initialTabNumber = 1;
const int dioLoggerMaxWidth = 90;
const String response = 'Response';
const String requestText = 'Request';

const Null nullValue = null;
const String nullString = 'null';
const String emptyString = '{}';
const String error = 'error';

const String dateFormatHHMMSS = 'HH:mm:ss';
const String timeSlotSplit = '-';
const String timeSlotSplitInterrupt = ' - ';
const String stacktrace = 'stacktrace';

const String unknown = 'Unknown';
const String receivedInvalidStatusCode = 'Received invalid status code:';
const String socketException = 'SocketException';
const String formatException = 'FormatException';
const String isNotaSubtypeOf = 'is not a subtype of';
const String notImplemented = 'Not Implemented';
const String requestCancelled = 'Request Cancelled';
const String internalServerError = 'Internal Server Error';
const String serviceUnavailable = 'Service unavailable';
const String methodAllowed = 'Method Allowed';
const String badRequest = 'Bad request';
const String unauthorizedRequest = 'Unauthorized request';
const String unexpectedErrorOccurred = 'Unexpected error occurred';
const String connectionRequestTimeout = 'Connection request timeout';
const String noInternetConnection = 'No internet connection';
const String errorDueToaConflict = 'Error due to a conflict';
const String sendTimeoutInConnectionWithAPIServer = 'Send timeout in connection with API server';
const String unableToProcessTheData = 'Unable to process the data';
const String formatExceptionSomethingWentWrongWithData = 'FormatException something went wrong with data';
const String notAcceptable = 'Not acceptable';
const String badeCertificate = 'Bade Certificate';
const String connectionError = 'Connection error';
const String dateTimeIssue = 'Please Update Date And Time';

const String present = 'Present';
const String isEmailValidPattern = r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
const String isWebsiteInvalid = r'((https?:www\.)|(https?:\/\/)|(www\.))[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9]{1,6}(\/[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)?';

const String savedLocalData = 'Saved Local Data';

String productionPoint = 'ProductionPoint';
String chargingPoint = 'ChargingPoint';
String recyclePoint = 'RecyclePoint';

Future<void> hideBottomMenu() async {
  // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  // const MethodChannel('bottomMenuChannel').invokeMethod('hideBottomMenu');
}

DateTime getDateFromStringHHMMSS(String time) {
  return DateFormat.Hms().parse(time);
}

List<String>? getSplitTimeSlot(String timeSlot, {bool isFromInterrupt = false}) {
  if (timeSlot.isNotEmpty) {
    if (isFromInterrupt) {
      List<String> parts = timeSlot.split(timeSlotSplitInterrupt);
      // Trim each part to remove extra whitespace
      return parts.map((part) => part.trim()).toList();
    } else {
      List<String> parts = timeSlot.split(timeSlotSplit);
      // Trim each part to remove extra whitespace
      return parts.map((part) => part.trim()).toList();
    }
  } else {
    return null;
  }
}

/// Check Android ID Configuration
// const String frontDisplayAndroidId = '734357c0b1da9949';
// const String frontDisplayAndroidId = 'e6f383aec148c247';
// const String frontDisplayAndroidId = 'ce17449df7c840eb';
// const String backDisplayAndroidId = 'a4244f4ad5cffc22';

/// Time To Minutes Conversion
int timeToMinutes(String time) {
  final parts = time.split(':');
  final hours = int.parse(parts[0]);
  final minutes = int.parse(parts[1]);
  return hours * 60 + minutes;
}

/// Minutes To Time Conversion
String minutesToTime(int minutes) {
  final hours = minutes ~/ 60;
  final mins = minutes % 60;
  return '${hours.toString().padLeft(2, '0')}:${mins.toString().padLeft(2, '0')}';
}

List<String> weekDayList = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

Directory? directory;

Future deleteAllDirectory() async {
  directory = await getApplicationDocumentsDirectory();
  File file = File('${directory!.path}/${'android_log.txt'}');
  File file2 = File('${directory!.path}/${'flutter_log_file.json'}');
  if (await file.exists()) {
    await file.delete();
  }
  if (await file2.exists()) {
    await file2.delete();
  }
}

Future initDirectory({String? fileName}) async {
  directory = await getApplicationDocumentsDirectory();
  final File file = File('${directory!.path}/${fileName ?? 'flutter_log_file.json'}');
  if (!file.existsSync()) {
    await file.create();
  }
}

writeFlutterLogs(String log, {String? fileName}) async {
  if (fileName != null) {
    await initDirectory(fileName: fileName);
  }
  if (directory != null) {
    final File file = File('${directory!.path}/${fileName ?? 'flutter_log_file.json'}');
    if (file.existsSync()) {
      file.writeAsStringSync('\n$log', mode: FileMode.write);
    }
  }
}

Duration constantSlotDuration = const Duration(seconds: 30);
Duration constantAdSequenceSlotDuration = const Duration(hours: 1);
String advertisementDirectoryName = 'Advertisement';
String assetsDirectoryName = 'Assets';
const String storeOpened = 'Store Opened';
const String dayChanged = 'Day Changed';
const String deductWallet = 'Deduct Wallet';
const String storeClosed = 'Store Closed';
String separator = '_com.kodytechnolabs.odigo_';

const String headerDateFormat = 'EEE, dd MMM yyyy HH:mm:ss';

///Temp Variables
int subtractDays = 0;
WidgetRef? globalRef;
double sigma = 4;
int startYear = 2000;
int endYear = 2100;
int storeOpenAlarmCode = 10021;
int storeCloseAlarmCode = 10022;
int schedulerAlarmCode = 10023;
int deductAlarmCode = 10024;

bool checkCurrentDateTime(BuildContext context, List<String> dateTime) {
  DateTime date = dateTime.first.getDateTimeObject(headerDateFormat);
  DateTime currentDateTime = DateTime.now().toUtc();
  if (date.day == currentDateTime.day && date.month == currentDateTime.month && date.year == currentDateTime.year && date.hour == currentDateTime.hour) {
    return true;
  } else {
    return false;
  }
}


const String mealDeliverySettings = 'mealDeliverySettings';
const String freeDistributionDeliverySettings = 'freeDistributionDeliverySettings';
const String recyclingSettings = 'recyclingSettings';
const String celebrationSettings = 'celebrationSettings';
const String cruiseSettings = 'cruiseSettings';

String getPackageName() {
  return staticPackageName;
}

/// to get AdsString from count
String getAdsString(int count) {
  if (count < 2) {
    return '$count ${LocaleKeys.keyAd.localized}';
  } else {
    return '$count ${LocaleKeys.keyAds.localized}';
  }
}

/// to get Store from count
String getStoreString(int count) {
  if (count < 2) {
    return '$count ${LocaleKeys.keyStore.localized}';
  } else {
    return '$count ${LocaleKeys.keyStores.localized}';
  }
}

/// to get Client from count
String geClientString(int count) {
  if (count < 2) {
    return '$count ${LocaleKeys.keyClient.localized}';
  } else {
    return '$count ${LocaleKeys.keyClients.localized}';
  }
}

Future<DrawableRoot> loadSvgRoot(String svgString) async {
  final rawSvg = await rootBundle.loadString(svgString);
  final DrawableRoot svgRoot = await svg.fromSvgString(rawSvg, rawSvg);
  return svgRoot;
}

const String defaultAssetAd = 'Common Ad';

Future<String> getAppVersion() async {
  showLog('getAppVersion Called');
  double? parsedValue;
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  String iosAppVersion = packageInfo.version;
  String androidAppVersion = (packageInfo.version);
  showLog('androidAppVersion $androidAppVersion');
  try {
    // Split by '.' and make sure the parts are valid
    var parts = androidAppVersion.split('.');
    if (parts.length == 3) {
      // Fixing a case where there are multiple parts after a dot
      androidAppVersion = '${parts[0]}.${parts[1]}'; // Keep only the first two parts
    }
    parsedValue = double.parse(androidAppVersion);
    print('parsedValueparsedValueparsedValueparsedValue $parsedValue');
  } catch (e) {
    print('Error: Invalid double format');
  }

  if (getIsIOSPlatform()) {
    RobotSession.apkVersion = parsedValue ?? 0.0;
    return iosAppVersion;
  } else {
    RobotSession.apkVersion = parsedValue ?? 0.0;
    return androidAppVersion;
  }
}

// showToaster(BuildContext context, String title) {
//   ScaffoldMessenger.of(context).showSnackBar(
//     SnackBar(
//       behavior: SnackBarBehavior.floating,
//       backgroundColor: AppColors.white,
//       margin: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.h),
//       padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.h),
//       shape: const StadiumBorder(),
//       duration: const Duration(seconds: 1),
//       content: Text(
//         title,
//         style: TextStyles.medium.copyWith(fontSize: 44.sp, color: AppColors.primary),
//       ),
//     ),
//   );
// }

/// Show Common Toast Message
commonToaster(String message) {
  return Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_LONG,
    timeInSecForIosWeb: 4,
    gravity: ToastGravity.BOTTOM,
    backgroundColor: Colors.black,
    textColor: Colors.white,
    fontSize: 44.0,
  );
}




String extractJsonFromNetworkException(Object error) {
  final errorString = error.toString();
  final startIndex = errorString.indexOf('response:');
  if (startIndex == -1) return '{}'; // No response field found, return empty JSON

  var jsonPart = errorString.substring(startIndex + 'response:'.length).trim();

  // If the freezed class's toString adds a closing bracket at the end, remove it
  if (jsonPart.endsWith(')')) {
    jsonPart = jsonPart.substring(0, jsonPart.length - 1);
  }

  return jsonPart;
}

class CustomRangeTextInputFormatter extends TextInputFormatter {

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue,TextEditingValue newValue,) {
    if(newValue.text == '')
      return TextEditingValue();
    else if(int.parse(newValue.text) < 1)
      return TextEditingValue().copyWith(text: '1');

    return int.parse(newValue.text) > 20 ? TextEditingValue().copyWith(text: '20') : newValue;
  }
}