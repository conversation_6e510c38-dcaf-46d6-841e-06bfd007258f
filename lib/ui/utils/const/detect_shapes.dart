import 'dart:math';

import 'package:odigo_display/ui/utils/theme/theme.dart';

// Function to detect if the points form an "S"-like gesture
bool isSShapeDetected(List<Offset> points) {
  if (points.length < 15) return false; // Need enough points

  List<double> directions = [];

// Calculate the direction of movement for each point (dx)
  for (int i = 1; i < points.length; i++) {
    double deltaX = points[i].dx - points[i - 1].dx;
    double deltaY = points[i].dy - points[i - 1].dy;
    double angle = atan2(deltaY, deltaX);
    directions.add(angle);
  }

// Count changes in direction (from positive to negative slope and vice versa)
  int curveChangeCount = 0;
  for (int i = 1; i < directions.length; i++) {
    if ((directions[i] > 0 && directions[i - 1] < 0) || (directions[i] < 0 && directions[i - 1] > 0)) {
      curveChangeCount++;
    }
  }

// For an "S" shape, we expect at least 2 significant changes in direction
  return curveChangeCount >= 2;
}

// Function to detect if the points form a circle-like gesture
bool isOShapeDetected(List<Offset> points) {
  if (points.length < 10) return false; // Ensure there are enough points
  Offset center = const Offset(0, 0);
  double radius = 0;

// Calculate the center of the circle
  for (var point in points) {
    center += point;
  }
  center = center / points.length.toDouble();

// Calculate the average radius
  for (var point in points) {
    radius += (point - center).distance;
  }
  radius = radius / points.length;

// Check how close all points are to the average radius (tolerance)
  double tolerance = radius * 0.2; // Allow some variance
  for (var point in points) {
    double distanceFromCenter = (point - center).distance;
    if ((distanceFromCenter - radius).abs() > tolerance) {
      return false;
    }
  }
  return true;
}
