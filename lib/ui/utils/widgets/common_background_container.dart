


import 'package:odigo_display/ui/utils/theme/theme.dart';

class CommonBackgroundContainer extends StatelessWidget with BaseStatelessWidget {
  final Widget? child;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final double? height;
  final double? width;

  const CommonBackgroundContainer({super.key, this.child, this.margin, this.padding, this.backgroundColor, this.borderRadius, this.height, this.width});

  @override
  Widget buildPage(BuildContext context) {
    return Container(
      height: height,
      width: width,
      // margin: margin ?? EdgeInsets.symmetric(horizontal: context.width * 0.02, vertical: context.height * 0.03),
      padding: padding,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(15.r),
        color: backgroundColor ?? AppColors.white,
      ),
      child: child,
    );
  }
}
