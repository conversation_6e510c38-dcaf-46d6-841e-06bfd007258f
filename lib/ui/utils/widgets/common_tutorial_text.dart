
import 'package:odigo_display/ui/utils/theme/theme.dart';


class CommonTutorialText extends StatelessWidget with BaseStatelessWidget{
  final String title;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final double? fontSize;
  final Color? clrfont;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextDecoration? textDecoration;
  final TextStyle? textStyle;
  final TextOverflow? textOverflow;
  final String? fontFamily;

  const CommonTutorialText(
      {Key? key,
      this.title = '',
      this.fontWeight,
      this.fontStyle,
      this.fontSize,
      this.clrfont,
      this.maxLines,
      this.textAlign,
      this.textDecoration,
      this.textStyle, this.textOverflow,this.fontFamily})
      : super(key: key);

  @override
  Widget buildPage(BuildContext context) {
    return Text(
      title,
      textScaleFactor: 1.0,
      //-- will not change if system fonts size changed
      maxLines: maxLines ?? 1,
      textAlign: textAlign ?? TextAlign.start,
      overflow: textOverflow ??TextOverflow.ellipsis,
      style: textStyle ?? TextStyle(
              fontFamily: fontFamily ?? TextStyles.fontFamily,
              fontWeight: fontWeight ?? TextStyles.fwSemiBold,
              fontSize: fontSize ?? 24.sp,
              color: clrfont ?? AppColors.white,
              fontStyle: fontStyle ?? FontStyle.normal,
              decoration: textDecoration ?? TextDecoration.none),
    );
  }
}
