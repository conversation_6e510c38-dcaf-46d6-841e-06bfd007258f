import 'package:odigo_display/ui/utils/theme/theme.dart';

class DialogProgressBar extends StatelessWidget {
  final bool isLoading;
  final bool forPagination;

  const DialogProgressBar({
    Key? key,
    required this.isLoading,
    this.forPagination = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return !(isLoading)
        ? const Offstage()
        : (forPagination)
            ? SizedBox(
                height: 70.h,
                child: const Center(
                    child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                )),
              )
            : AbsorbPointer(
                absorbing: true,
                child: Container(
                  color: Colors.white.withOpacity(0.4),
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Center(
                          child: SizedBox(
                            height: MediaQuery.of(context).size.width * 0.1,
                            width: MediaQuery.of(context).size.width * 0.1,
                            child: const CircularProgressIndicator(
                              strokeWidth: 10,
                                                    valueColor:
                              AlwaysStoppedAnimation<Color>(AppColors.white),
                                                  ),
                          ))),
                ),
              );
  }
}
