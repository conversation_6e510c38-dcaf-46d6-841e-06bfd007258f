import 'package:flutter/material.dart';
import 'package:odigo_display/ui/utils/helper/base_widget.dart';
import 'package:odigo_display/ui/waiting_screen/mobile/waiting_screen_mobile.dart';

class WaitingScreen extends StatelessWidget with BaseStatelessWidget {
  final bool setAlarm;
  const WaitingScreen({Key? key, required this.setAlarm}) : super(key: key);

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    return WaitingScreenMobile(setAlarm: setAlarm);
  }
}

