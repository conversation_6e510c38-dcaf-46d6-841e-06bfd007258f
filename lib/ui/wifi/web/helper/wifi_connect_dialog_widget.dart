import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:odigo_display/framework/controller/wifi/wifi_controller.dart';
import 'package:odigo_display/framework/utils/helper/voice_helper.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:odigo_display/ui/utils/widgets/common_form_field.dart';

class WifiConnectDialogWidget extends ConsumerWidget with BaseConsumerWidget {
  final String networkName;
  final int networkStrength;

  const WifiConnectDialogWidget({super.key, required this.networkName, required this.networkStrength});

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final wifiWatch = ref.watch(wifiController);
    return Dialog(
      backgroundColor: AppColors.black,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          wifiWatch.isConnecting
              ? const Offstage()
              : InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const Icon(Icons.close, color: AppColors.white),
                ),
          Row(
            children: [
              CommonSVG(
                  height: context.height * 0.05,
                  strIcon: networkStrength == 0
                      ? Assets.svgs.svgWifiSecuredLevel0.path
                      : networkStrength == 1
                          ? Assets.svgs.svgWifiSecuredLevel1.path
                          : Assets.svgs.svgWifiSecuredLevel2.path),
              SizedBox(width: context.width * 0.02),
              CommonText(
                title: networkName,
                textStyle: TextStyles.semiBold.copyWith(color: AppColors.white, fontSize: 26.sp),
              ),
            ],
          ),
          SizedBox(height: 40.h),
          CommonInputFormField(
            fieldHeight: 100.h,
            hintTextStyle: TextStyles.semiBold.copyWith(color: AppColors.white, fontSize: 26.sp),
            hintText: LocalizationStrings.keyEnterNetworkSecurityKey.localized,
            obscureText: !wifiWatch.isPasswordVisible,
            textEditingController: wifiWatch.passwordController,
            onChanged: (password) {
              wifiWatch.notifyListeners();
            },
            validator: (value) {
              return null;
            },
            suffixWidget: InkWell(
              onTap: () {
                wifiWatch.updateIsPasswordVisible();
              },
              child: CommonSVG(
                strIcon: wifiWatch.isPasswordVisible ? Assets.svgs.svgPasswordHide.path : Assets.svgs.svgPasswordUnhide.path,
                svgColor: AppColors.white,
                height: context.height * 0.005,
                width: context.width * 0.01,
              ),
            ).paddingOnly(right: context.width * 0.02),
          ),
          SizedBox(height: context.height * 0.02),
          CommonButton(
            height: 65.h,
            width: 160.w,
            buttonTextStyle: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.black),
            isButtonEnabled: wifiWatch.passwordController.text.isNotEmpty,
            isLoading: wifiWatch.isConnecting,
            backgroundColor: AppColors.white,
            buttonText: LocalizationStrings.keyConnect.localized,
            onTap: () async {
              hideKeyboard(context);
              Navigator.pop(context);
              showCommonSwitchingDialog(
                key: wifiWatch.wifiConnectingDialogKey,
                context: context,
                text: LocalizationStrings.keyConnectingToNetwork.localized,
              );
              Timer.periodic(
                const Duration(seconds: 1),
                (timer) {
                  if (timer.tick == 60) {
                    timer.cancel();
                    if (wifiWatch.wifiConnectingDialogKey.currentContext != null) {
                      Navigator.pop(wifiWatch.wifiConnectingDialogKey.currentContext!);
                    }
                    wifiWatch.wifiConnectionStream?.cancel();
                    if (wifiWatch.connectedWifi == null) {
                      VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceLetMeConnectWithYourWifi);
                    }
                  }
                },
              );
              RobotSession.robotBox.put(networkName, wifiWatch.passwordController.text);
              VoiceHelper.instance.playAudioFromAsset(AppAssets.voiceEstablishingNetworkConnection);
              wifiWatch.connectToNetwork(networkSSID: networkName, password: wifiWatch.passwordController.text);
            },
          ).alignAtBottomRight()
        ],
      ).paddingOnly(top: context.height * 0.02, bottom: context.height * 0.02, left: context.width * 0.02, right: context.width * 0.02),
    ).paddingSymmetric(horizontal: context.height * 0.05);
  }
}
