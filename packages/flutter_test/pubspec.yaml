name: flutter_test

environment:
  sdk: '>=3.2.0-0 <4.0.0'

dependencies:
  # To update these, use "flutter update-packages --force-upgrade".
  #
  # For detailed instructions, refer to:
  # https://github.com/flutter/flutter/wiki/Updating-dependencies-in-Flutter

  flutter:
    sdk: flutter

  # We depend on very specific internal implementation details of the
  # 'test' package, which change between versions, so when upgrading
  # this, make sure the tests are still running correctly.
  test_api: 0.6.1
  matcher: 0.12.16+1

  # Used by golden file comparator
  path: 1.9.0

  # Testing utilities for dealing with async calls and time.
  fake_async: 1.3.1
  clock: 1.1.1

  # We import stack_trace because the test packages uses it and we
  # need to be able to unmangle the stack traces that it passed to
  # stack_trace. See https://github.com/dart-lang/test/issues/590
  stack_trace: 1.11.1

  # Used by globalToLocal et al.
  vector_math: 2.1.4

  # Used to detect memory leaks with `testWidgets`.
  leak_tracker_flutter_testing: 2.0.1

  async: 2.11.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  boolean_selector: 2.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  characters: 1.3.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  collection: 1.18.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  leak_tracker: 10.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  leak_tracker_testing: 2.0.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  material_color_utilities: 0.8.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  meta: 1.11.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_span: 1.10.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  stream_channel: 2.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  string_scanner: 1.2.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  term_glyph: 1.2.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  vm_service: 13.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

dev_dependencies:
  file: 7.0.0

# PUBSPEC CHECKSUM: 6913
