name: integration_test_macos
description: Desktop implementation of integration_test plugin
version: 0.0.1+1
homepage: https://github.com/flutter/flutter/tree/master/packages/integration_test/integration_test_macos

flutter:
  plugin:
    platforms:
      macos:
        pluginClass: IntegrationTestPlugin

environment:
  sdk: '>=3.2.0-0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  characters: 1.3.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  collection: 1.18.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  material_color_utilities: 0.8.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  meta: 1.11.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  vector_math: 2.1.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

dev_dependencies:
  pedantic: 1.11.1

# PUBSPEC CHECKSUM: d95b
